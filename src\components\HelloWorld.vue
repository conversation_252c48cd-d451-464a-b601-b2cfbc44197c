<script setup>
import { ref } from 'vue';

defineProps({
  msg: String,
});

const count = ref(0);
</script>

<template>
  <h1 class="title">{{ msg }}</h1>

  <div class="card">
    <button type="button" @click="count++">count is {{ count }}</button>
    <p>
      Edit
      <code>components/HelloWorld.vue</code> to test HMR
    </p>
  </div>

  <div class="info-section">
    <p>
      Check out
      <a href="https://vuejs.org/guide/quick-start.html#local" target="_blank"
        >create-vue</a
      >, the official Vue + Vite starter
    </p>
    <p>
      Learn more about IDE Support for Vue in the
      <a
        href="https://vuejs.org/guide/scaling-up/tooling.html#ide-support"
        target="_blank"
        >Vue Docs Scaling up Guide</a
      >.
    </p>
    <p class="read-the-docs">Click on the Vite and Vue logos to learn more</p>
  </div>
</template>

<style scoped>
.title {
  margin-bottom: 1.5rem;
}

.card {
  padding: 2em;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.05);
  margin-bottom: 1.5rem;
  width: 100%;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.info-section {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.read-the-docs {
  color: #888;
  margin-top: 1.5rem;
}

/* Responsive styles */
@media (max-width: 768px) {
  .card {
    padding: 1.5em;
    max-width: 90%;
  }
}

@media (max-width: 480px) {
  .card {
    padding: 1em;
  }

  .title {
    margin-bottom: 1rem;
  }
}
</style>
