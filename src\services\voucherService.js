/**
 * Voucher service for managing vouchers
 */
import { ref } from 'vue';
import httpClient from '../utils/httpClient';
import { mockVouchers, simulateApiDelay, createApiResponse, createApiErrorResponse } from './mockData';
import { useAuthService } from './authService';

// Configuration
const USE_MOCK_DATA = import.meta.env.VITE_USE_MOCK_DATA === 'true'; // Set to false to use real API

// Voucher state
const vouchers = ref([]);
const selectedVoucher = ref(null);
const isLoading = ref(false);
const error = ref(null);

// Get auth service
const { isLoggedIn } = useAuthService();

/**
 * Get all vouchers
 * @param {string} status - Filter by status (Active, Used, Expired) (optional)
 * @returns {Promise<Array>} List of vouchers
 */
const getVouchers = async (status = null) => {
  isLoading.value = true;
  error.value = null;

  try {
    if (!isLoggedIn.value) {
      throw new Error('User not authenticated');
    }

    if (USE_MOCK_DATA) {
      // Mock implementation
      await simulateApiDelay();

      // Filter vouchers by status if provided
      let filteredVouchers = [...mockVouchers];

      if (status) {
        filteredVouchers = filteredVouchers.filter(voucher =>
          voucher.status.toLowerCase() === status.toLowerCase()
        );
      }

      vouchers.value = filteredVouchers;
      return createApiResponse(filteredVouchers);
    } else {
      // Real API implementation
      const { accountNumber } = useAuthService();

      // Map status to API function
      let func = 'Active';
      if (status) {
        if (status.toLowerCase() === 'used') {
          func = 'Used';
        } else if (status.toLowerCase() === 'expired') {
          func = 'Expired';
        }
      }

      // Use the correct API endpoint based on our testing
      const requestData = {
        AcctNo: accountNumber.value,
        Func: func,
        Lang: 'en'
      };

      const response = await httpClient.post('api/AcccountVoucher', requestData);

      if (Array.isArray(response)) {
        // Format the vouchers
        const formattedVouchers = response.map(voucher => ({
          id: voucher.VoucherId || voucher.Id,
          brand: voucher.Brand || 'RVMPlus',
          title: voucher.Title || voucher.Descp,
          subtitle: voucher.SubTitle || '',
          expiryDate: voucher.ExpiryDate || voucher.ExpDate,
          status: func.toLowerCase(),
          type: voucher.Type || 'REWARD',
          content: {
            introText: voucher.Descp || '',
            terms: voucher.Terms ? [voucher.Terms] : []
          },
          bannerImage: voucher.BannerImg || voucher.Image || ''
        }));

        vouchers.value = formattedVouchers;
        return createApiResponse(formattedVouchers);
      } else if (response && response.length === 0) {
        // Empty response
        vouchers.value = [];
        return createApiResponse([]);
      }

      throw new Error('Failed to retrieve vouchers');
    }
  } catch (err) {
    error.value = err.message;
    return createApiErrorResponse('VOUCHER_SERVICE_UNAVAILABLE', err.message);
  } finally {
    isLoading.value = false;
  }
};

/**
 * Get voucher details by ID
 * @param {number} id - Voucher ID
 * @returns {Promise<Object>} Voucher details
 */
const getVoucherDetails = async (id) => {
  isLoading.value = true;
  error.value = null;

  try {
    if (!isLoggedIn.value) {
      throw new Error('User not authenticated');
    }

    if (!id) {
      throw new Error('Voucher ID is required');
    }

    if (USE_MOCK_DATA) {
      // Mock implementation
      await simulateApiDelay();

      // Find voucher in mock data
      const voucher = mockVouchers.find(v => v.id === parseInt(id));

      if (!voucher) {
        return createApiErrorResponse('VOUCHER_NOT_FOUND', 'Voucher not found');
      }

      selectedVoucher.value = voucher;
      return createApiResponse(voucher);
    } else {
      // Real API implementation
      const { accountNumber } = useAuthService();

      // Use the correct API endpoint based on our testing
      const requestData = {
        AcctNo: accountNumber.value,
        Func: 'ById',
        VoucherId: id,
        Lang: 'en'
      };

      const response = await httpClient.post('api/AcccountVoucher', requestData);

      if (Array.isArray(response) && response.length > 0) {
        const voucher = response[0];

        // Format the voucher
        const formattedVoucher = {
          id: voucher.VoucherId || voucher.Id,
          brand: voucher.Brand || 'RVMPlus',
          title: voucher.Title || voucher.Descp,
          subtitle: voucher.SubTitle || '',
          expiryDate: voucher.ExpiryDate || voucher.ExpDate,
          status: voucher.Status || 'active',
          type: voucher.Type || 'REWARD',
          content: {
            introText: voucher.Descp || '',
            terms: voucher.Terms ? [voucher.Terms] : []
          },
          bannerImage: voucher.BannerImg || voucher.Image || ''
        };

        selectedVoucher.value = formattedVoucher;
        return createApiResponse(formattedVoucher);
      }

      throw new Error('Voucher not found');
    }
  } catch (err) {
    error.value = err.message;
    return createApiErrorResponse('VOUCHER_NOT_FOUND', err.message);
  } finally {
    isLoading.value = false;
  }
};

/**
 * Redeem voucher
 * @param {number} id - Voucher ID
 * @returns {Promise<Object>} Redemption result
 */
const redeemVoucher = async (id) => {
  isLoading.value = true;
  error.value = null;

  try {
    if (!isLoggedIn.value) {
      throw new Error('User not authenticated');
    }

    if (!id) {
      throw new Error('Voucher ID is required');
    }

    if (USE_MOCK_DATA) {
      // Mock implementation
      await simulateApiDelay();

      // Find voucher in mock data
      const voucherIndex = mockVouchers.findIndex(v => v.id === parseInt(id));

      if (voucherIndex === -1) {
        return createApiErrorResponse('VOUCHER_NOT_FOUND', 'Voucher not found');
      }

      // Check if voucher is already redeemed
      if (mockVouchers[voucherIndex].status === 'Used') {
        return createApiErrorResponse('VOUCHER_ALREADY_REDEEMED', 'Voucher has already been redeemed');
      }

      // Check if voucher is expired
      const expiryDate = new Date(mockVouchers[voucherIndex].expiryDate);
      if (expiryDate < new Date()) {
        return createApiErrorResponse('VOUCHER_EXPIRED', 'Voucher has expired');
      }

      // Update voucher status
      mockVouchers[voucherIndex] = {
        ...mockVouchers[voucherIndex],
        status: 'Used'
      };

      // Update vouchers list
      vouchers.value = [...mockVouchers];

      // Return redemption result
      return createApiResponse({
        id: parseInt(id),
        status: 'Used',
        redeemedAt: new Date().toISOString()
      }, 'Voucher redeemed successfully');
    } else {
      // Real API implementation
      const { accountNumber } = useAuthService();

      // For voucher redemption, we would need the actual API endpoint
      // Since we don't have the exact endpoint from our testing, we'll use a placeholder
      // This should be updated with the correct endpoint when available
      const requestData = {
        AcctNo: accountNumber.value,
        VoucherId: id,
        Lang: 'en'
      };

      try {
        // This is a placeholder - replace with the actual redemption endpoint
        const response = await httpClient.post('api/RedeemVoucher', requestData);

        if (response && response.success !== false) {
          // Update voucher in the list
          const voucherIndex = vouchers.value.findIndex(v => v.id === parseInt(id));
          if (voucherIndex !== -1) {
            vouchers.value[voucherIndex] = {
              ...vouchers.value[voucherIndex],
              status: 'used'
            };
          }

          return createApiResponse({
            id: parseInt(id),
            status: 'used',
            redeemedAt: new Date().toISOString()
          }, 'Voucher redeemed successfully');
        }

        throw new Error(response?.message || 'Failed to redeem voucher');
      } catch (err) {
        // If the redemption endpoint doesn't exist or fails, we'll show an error
        console.error('Voucher redemption failed:', err);
        throw new Error('Voucher redemption is not available in the current API version');
      }
    }
  } catch (err) {
    error.value = err.message;
    return createApiErrorResponse('REDEMPTION_FAILED', err.message);
  } finally {
    isLoading.value = false;
  }
};

/**
 * Reset error state
 */
const resetError = () => {
  error.value = null;
};

// Export voucher service
export const useVoucherService = () => {
  return {
    // State
    vouchers,
    selectedVoucher,
    isLoading,
    error,

    // Methods
    getVouchers,
    getVoucherDetails,
    redeemVoucher,
    resetError
  };
};
