<script setup>
// Header component for the app
import { useRouter, useRoute } from 'vue-router';
import { computed } from 'vue';
import LanguageSelector from './LanguageSelector.vue';
import { useI18n } from 'vue-i18n';

const router = useRouter();
const route = useRoute();
const { t } = useI18n();

// Determine if we should show the back button
// Only hide on home page
const showBackButton = computed(() => {
  return route.name !== 'Home';
});

// Go back function
const goBack = () => {
  router.back();
};

// Get the translated page title
const getPageTitle = () => {
  // Map route names to translation keys
  const routeToTranslationMap = {
    'Home': 'navigation.home',
    'Vouchers': 'navigation.vouchers',
    'QRCode': 'navigation.qrcode',
    'Locations': 'navigation.locations',
    'Profile': 'navigation.profile',
    'Transactions': 'navigation.transactions',
    'Move': 'navigation.move',
    'Forest': 'navigation.rvmForest',
    'EditProfile': 'profile.editProfile',
    'Login': 'auth.login',
    'Help': 'common.help',
    'Contact': 'common.contact',
    'Privacy': 'profile.privacyPolicy',
    'VoucherDetail': 'vouchers.title',
    'Information': 'common.help',
    'HowToRedeem': 'information.redeem.title',
    'HowToUse': 'information.use.title',
    'BYC': 'information.byc.title',
    'Campaign': 'information.campaign.title',
  };

  // Special case for InAppBrowser
  if (route.name === 'InAppBrowser') {
    return route.params.title || route.meta?.title || route.name;
  }

  // Get the translation key for the current route
  const translationKey = routeToTranslationMap[route.name];

  // Try to get the translated title, fall back to meta title or route name
  if (translationKey && t(translationKey) !== translationKey) {
    return t(translationKey);
  } else {
    return route.meta?.title || route.name;
  }
};
</script>

<template>
  <header class="header">
    <div class="header-content">
      <!-- Back button -->
      <div class="left-section">
        <button v-if="showBackButton" class="d-flex flex-col align-items-center" @click="goBack">
          <span class="back-text">‹ {{ t('common.back') }}</span>
        </button>
      </div>

      <!-- Page title -->
      <div class="title-section">
        <h1 class="page-title">{{ getPageTitle() }}</h1>
      </div>

      <!-- Language selector -->
      <div class="right-section">
        <LanguageSelector />
      </div>
    </div>
  </header>
</template>

<style scoped>
.header {
  width: 100%;
  padding: 12px 16px;
  background-color: var(--primary-color);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

/* Left section with back button */
.left-section {
  width: 80px; /* Fixed width to ensure title stays centered */
  display: flex;
  align-items: center;
}

/* Center section with title */
.title-section {
  position: absolute;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none; /* Allow clicks to pass through to elements behind */
}

/* Right section with language selector */
.right-section {
  width: 80px; /* Fixed width to ensure title stays centered */
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

/* Page title */
.page-title {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  text-align: center;
}

/* Back button */
.back-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0;
  height: 24px; /* Fixed height for better alignment */
  pointer-events: auto; /* Ensure button is clickable */
}

.back-text {
  align-items: center;
  font-size: 0.9rem;
  line-height: 24px;
  display: inline-flex;
  align-items: center;
  height: 24px;
}

@media (min-width: 769px) {
  .header {
    padding: 16px 24px;
  }

  .page-title {
    font-size: 1.4rem;
  }
}
</style>
