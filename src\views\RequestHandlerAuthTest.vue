<script setup>
import { ref } from 'vue';
import { requestHandlerAuth } from '../services/bitmapService';

const token = ref('');
const bitmap = ref(7);
const func = ref('0');
const ref01 = ref('10000838');
const ref02 = ref('');
const lang = ref('en');
const response = ref(null);
const error = ref(null);

const sendRequest = async () => {
  error.value = null;
  response.value = null;
  try {
    const params = {
      Token: token.value,
      bitmap: bitmap.value,
      Func: func.value,
      Ref01: ref01.value,
      Ref02: ref02.value,
      Lang: lang.value,
    };
    response.value = await requestHandlerAuth(params);
  } catch (err) {
    error.value = err.message;
  }
};
</script>

<template>
  <div class="request-handler-auth-test">
    <h2>RequestHandlerAuth API Test</h2>
    <form @submit.prevent="sendRequest">
      <label>
        Token:
        <input v-model="token" placeholder="Enter token" />
      </label>
      <label>
        Bitmap:
        <input v-model.number="bitmap" type="number" />
      </label>
      <label>
        Func:
        <input v-model="func" />
      </label>
      <label>
        Ref01:
        <input v-model="ref01" />
      </label>
      <label>
        Ref02:
        <input v-model="ref02" />
      </label>
      <label>
        Lang:
        <input v-model="lang" />
      </label>
      <button type="submit">Send Request</button>
    </form>
    <div v-if="response" class="response">
      <h3>Response:</h3>
      <pre>{{ response }}</pre>
    </div>
    <div v-if="error" class="error">
      <h3>Error:</h3>
      <pre>{{ error }}</pre>
    </div>
  </div>
</template>

<style scoped>
.request-handler-auth-test {
  max-width: 500px;
  margin: 2rem auto;
  padding: 2rem;
  border: 1px solid #ccc;
  border-radius: 8px;
  background: #fff;
}
form label {
  display: block;
  margin-bottom: 1rem;
}
input {
  margin-left: 0.5rem;
}
button {
  margin-top: 1rem;
}
.response, .error {
  margin-top: 2rem;
  padding: 1rem;
  border-radius: 4px;
}
.response {
  background: #e6ffe6;
  border: 1px solid #b2ffb2;
}
.error {
  background: #ffe6e6;
  border: 1px solid #ffb2b2;
}
</style> 