/**
 * Encodes a URL for safe use in route parameters
 * @param {string} url - The URL to encode
 * @returns {string} - The encoded URL
 */
export const encodeURL = (url) => {
  return encodeURIComponent(url);
};

/**
 * Decodes a URL from route parameters
 * @param {string} encodedUrl - The encoded URL
 * @returns {string} - The decoded URL
 */
export const decodeURL = (encodedUrl) => {
  return decodeURIComponent(encodedUrl);
};
