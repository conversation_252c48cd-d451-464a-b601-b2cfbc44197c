import { ref } from 'vue';
import { usePublicBitmapRequest } from './usePublicBitmapRequest';
import { useAuth } from '../store/auth';

const data = ref(null);
const isLoading = ref(false);
const error = ref(null);
const { isLoggedIn, accountNumber } = useAuth();

export const useHomeBitmap = () => {
  const { fetchData, data: bitmapData, isLoading: bitmapLoading, error: bitmapError } = usePublicBitmapRequest();

  const fetchHomeBitmap = async () => {
    isLoading.value = true;
    error.value = null;
    try {
      await fetchData(3, isLoggedIn.value ? '' : '0', isLoggedIn.value ? accountNumber.value : '', '', '', '');
      data.value = bitmapData.value;
    } catch (err) {
      error.value = err;
    } finally {
      isLoading.value = false;
    }
  };

  return {
    data,
    isLoading,
    error,
    fetchHomeBitmap,
  };
}; 