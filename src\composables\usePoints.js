import { computed } from 'vue';
import { useBitmapRequest } from './useBitmapRequest';

export function usePoints() {
  const { data, isLoading, error, fetchData } = useBitmapRequest();

  const points = computed(() => {
    return data.value?.Table0?.[0]?.BalPts || 0;
  });

  const fetchPoints = async () => {
    await fetchData(1); // bitmap 1 for points
    return points.value;
  };

  return {
    points,
    isLoading,
    error,
    fetchPoints,
  };
} 