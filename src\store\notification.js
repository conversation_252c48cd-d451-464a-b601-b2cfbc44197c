import { ref } from 'vue';

// Notification state
const notification = ref({
  show: false,
  message: '',
  type: 'success',
  duration: 3000,
  position: 'top'
});

/**
 * Show a notification
 * @param {Object} options - Notification options
 * @param {string} options.message - Notification message
 * @param {string} options.type - Notification type (success, error, info, warning)
 * @param {number} options.duration - Duration in milliseconds
 * @param {string} options.position - Position (top, bottom)
 */
const showNotification = (options) => {
  // Hide any existing notification first
  hideNotification();
  
  // Set timeout to ensure the previous notification is hidden
  setTimeout(() => {
    notification.value = {
      show: true,
      message: options.message || 'Operation completed',
      type: options.type || 'success',
      duration: options.duration !== undefined ? options.duration : 3000,
      position: options.position || 'top'
    };
  }, 100);
};

/**
 * Show a success notification
 * @param {string} message - Notification message
 * @param {Object} options - Additional options
 */
const showSuccess = (message, options = {}) => {
  showNotification({
    message,
    type: 'success',
    ...options
  });
};

/**
 * Show an error notification
 * @param {string} message - Notification message
 * @param {Object} options - Additional options
 */
const showError = (message, options = {}) => {
  showNotification({
    message,
    type: 'error',
    ...options
  });
};

/**
 * Show an info notification
 * @param {string} message - Notification message
 * @param {Object} options - Additional options
 */
const showInfo = (message, options = {}) => {
  showNotification({
    message,
    type: 'info',
    ...options
  });
};

/**
 * Show a warning notification
 * @param {string} message - Notification message
 * @param {Object} options - Additional options
 */
const showWarning = (message, options = {}) => {
  showNotification({
    message,
    type: 'warning',
    ...options
  });
};

/**
 * Hide the notification
 */
const hideNotification = () => {
  notification.value.show = false;
};

/**
 * Use notification store
 * @returns {Object} Notification store
 */
export const useNotification = () => {
  return {
    // State
    notification,
    
    // Methods
    showNotification,
    showSuccess,
    showError,
    showInfo,
    showWarning,
    hideNotification
  };
};
