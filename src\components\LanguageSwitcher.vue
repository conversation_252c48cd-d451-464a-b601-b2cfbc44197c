<script setup>
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { updateHtmlLang } from '../utils/htmlLang';

const { t, locale } = useI18n();

// Available languages
const languages = [
  { code: 'en', name: 'English' },
  { code: 'ms', name: 'Bahasa Melayu' },
  { code: 'zh', name: '中文' }
];

// Current selected language
const selectedLanguage = ref(locale.value);

// Computed property to get the current language name
const currentLanguageName = computed(() => {
  const lang = languages.find(lang => lang.code === selectedLanguage.value);
  return lang ? lang.name : 'English';
});

// Show/hide dropdown
const showDropdown = ref(false);

// Toggle dropdown visibility
const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value;
};

// Change language
const changeLanguage = (langCode) => {
  selectedLanguage.value = langCode;
  locale.value = langCode;
  localStorage.setItem('language', langCode);
  updateHtmlLang(langCode);
  showDropdown.value = false;
};
</script>

<template>
  <div class="language-switcher">
    <div class="language-selector" @click="toggleDropdown">
      <span class="current-language">{{ currentLanguageName }}</span>
      <span class="dropdown-arrow">▼</span>
    </div>

    <div v-if="showDropdown" class="language-dropdown">
      <div
        v-for="lang in languages"
        :key="lang.code"
        class="language-option"
        :class="{ active: selectedLanguage === lang.code }"
        @click="changeLanguage(lang.code)"
      >
        {{ lang.name }}
      </div>
    </div>
  </div>
</template>

<style scoped>
.language-switcher {
  position: relative;
  width: 100%;
}

.language-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.language-selector:hover {
  background-color: var(--highlight-color);
}

.current-language {
  font-weight: 500;
  color: var(--text-color);
}

.dropdown-arrow {
  font-size: 0.8rem;
  color: var(--text-light);
  transition: transform 0.2s;
}

.language-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 0.5rem;
  background-color: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.language-option {
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
  color: var(--text-color);
}

.language-option:hover {
  background-color: var(--highlight-color);
}

.language-option.active {
  background-color: var(--primary-color);
  color: white;
}

/* Mobile styles */
@media (max-width: 480px) {
  .language-dropdown {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    top: auto;
    margin-top: 0;
    border-radius: 16px 16px 0 0;
    max-height: 40vh;
    overflow-y: auto;
    z-index: 100;
  }

  .language-option {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
  }
}
</style>
