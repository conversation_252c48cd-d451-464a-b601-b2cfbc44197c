// Test script for direct login API testing

// Configuration
const API_BASE_URL = 'https://staging.rvmplus.com';

// Test credentials
const TEST_PHONE = '60102431439';
const TEST_PASSWORD = '123456';
const LANG = 'en';

// Test login directly
async function testLoginDirect() {
  console.log('Testing direct login...');
  
  // Construct the URL
  const url = `${API_BASE_URL}/api/cmlogin/${TEST_PHONE}/${TEST_PASSWORD}/${LANG}`;
  console.log('Login URL:', url);
  
  try {
    // Make the request
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries([...response.headers.entries()]));
    
    // Try to parse as JSON
    try {
      const data = await response.json();
      console.log('Response data (JSON):', JSON.stringify(data, null, 2));
      
      // Check if login was successful
      if (Array.isArray(data) && data.length > 0) {
        const loginData = data[0];
        
        if (loginData.AcctNo && loginData.AcctNo !== 0) {
          console.log('Login successful!');
          console.log('Account number:', loginData.AcctNo);
          console.log('Token:', loginData.Token || loginData.access_token || loginData.token);
        } else {
          console.log('Login failed:', loginData.Descp || loginData.Message || 'Unknown error');
        }
      } else {
        console.log('Unexpected response format');
      }
    } catch (e) {
      console.error('Error parsing JSON:', e);
      const text = await response.text();
      console.log('Response text:', text);
    }
  } catch (error) {
    console.error('Error making request:', error);
  }
}

// Test login with POST method
async function testLoginPost() {
  console.log('\nTesting login with POST method...');
  
  // Construct the URL
  const url = `${API_BASE_URL}/api/cmlogin`;
  console.log('Login URL:', url);
  
  try {
    // Make the request
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        userId: TEST_PHONE,
        password: TEST_PASSWORD,
        lang: LANG
      })
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries([...response.headers.entries()]));
    
    // Try to parse as JSON
    try {
      const data = await response.json();
      console.log('Response data (JSON):', JSON.stringify(data, null, 2));
    } catch (e) {
      console.error('Error parsing JSON:', e);
      const text = await response.text();
      console.log('Response text:', text);
    }
  } catch (error) {
    console.error('Error making request:', error);
  }
}

// Run the tests
async function runTests() {
  await testLoginDirect();
  await testLoginPost();
}

runTests();
