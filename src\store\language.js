import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { updateHtmlLang } from '../utils/htmlLang';

// Create a composable function to manage language state
export const useLanguage = () => {
  // Get i18n instance
  const i18n = useI18n();

  // Available languages
  const availableLanguages = [
    { code: 'en', name: 'English' },
    { code: 'ms', name: 'Bahasa Melayu' },
    { code: 'zh', name: '中文' }
  ];

  // Current language
  const currentLanguage = ref(i18n.locale.value);

  // Watch for language changes and update localStorage and HTML lang attribute
  watch(currentLanguage, (newLang) => {
    i18n.locale.value = newLang;
    localStorage.setItem('language', newLang);
    updateHtmlLang(newLang);
  });

  // Change language
  const changeLanguage = (langCode) => {
    if (availableLanguages.some(lang => lang.code === langCode)) {
      currentLanguage.value = langCode;
    }
  };

  // Get language name by code
  const getLanguageName = (langCode) => {
    const lang = availableLanguages.find(lang => lang.code === langCode);
    return lang ? lang.name : 'English';
  };

  return {
    availableLanguages,
    currentLanguage,
    changeLanguage,
    getLanguageName
  };
};
