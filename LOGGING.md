# Logging Guidelines

This document explains how to use the logging utility in the RVMPlus Rewards application to ensure console logs are properly handled across different environments.

## Overview

The application uses a custom logging utility (`src/utils/logger.js`) that automatically handles console output based on the current environment:

- **Development**: All logging enabled
- **Staging**: All logging enabled (except debug)
- **Production**: Only error logging enabled, all other console output is removed

## Why Use the Logger Utility?

1. **Environment-aware**: Automatically disables non-critical logging in production
2. **Performance**: Reduces bundle size and improves performance in production
3. **Consistency**: Provides a standardized way to handle logging across the application
4. **Debugging**: Offers enhanced debugging features like grouping and performance timing

## Basic Usage

### Import the Logger

```javascript
// Import specific functions
import { log, warn, error, debug } from '@/utils/logger';

// Or import the default logger
import logger from '@/utils/logger';

// Or create a module-specific logger
import { createLogger } from '@/utils/logger';
const logger = createLogger('ComponentName');
```

### Basic Logging Methods

```javascript
import { log, info, warn, error, debug } from '@/utils/logger';

// General logging (development/staging only)
log('User logged in successfully');
info('API request completed');

// Warnings (development/staging only)
warn('API response took longer than expected');

// Errors (always logged, even in production)
error('Failed to authenticate user');

// Debug information (development only)
debug('Component state:', componentState);
```

### Advanced Logging Features

#### Grouped Logging

```javascript
import { group } from '@/utils/logger';

group('User Authentication', () => {
  log('Validating credentials');
  log('Checking user permissions');
  log('Authentication complete');
});
```

#### Performance Logging

```javascript
import { logPerformance } from '@/utils/logger';

const result = await logPerformance('API Request', async () => {
  return await fetchUserData();
});
```

#### API Request Logging

```javascript
import { logApiRequest } from '@/utils/logger';

const response = await fetch('/api/users');
const data = await response.json();

logApiRequest('GET', '/api/users', null, data);
```

#### Component Lifecycle Logging

```javascript
import { logComponentLifecycle } from '@/utils/logger';
import { onMounted, onUnmounted } from 'vue';

onMounted(() => {
  logComponentLifecycle('UserProfile', 'mounted');
});

onUnmounted(() => {
  logComponentLifecycle('UserProfile', 'unmounted');
});
```

## Module-Specific Loggers

For better organization, create module-specific loggers:

```javascript
// In a service file
import { createLogger } from '@/utils/logger';
const logger = createLogger('AuthService');

export const loginUser = async (credentials) => {
  logger.log('Attempting user login');
  
  try {
    const response = await api.login(credentials);
    logger.log('Login successful');
    return response;
  } catch (error) {
    logger.error('Login failed:', error);
    throw error;
  }
};
```

## Migration from Console

### Before (using console directly)

```javascript
console.log('User data loaded:', userData);
console.warn('API response delayed');
console.error('Authentication failed:', error);
```

### After (using logger utility)

```javascript
import { log, warn, error } from '@/utils/logger';

log('User data loaded:', userData);
warn('API response delayed');
error('Authentication failed:', error);
```

## Best Practices

### 1. Use Appropriate Log Levels

- **`log/info`**: General information, successful operations
- **`warn`**: Potential issues, deprecated features, performance concerns
- **`error`**: Actual errors, failed operations (always logged)
- **`debug`**: Detailed debugging information (development only)

### 2. Provide Context

```javascript
// Good
log('User profile updated:', { userId: user.id, changes: updatedFields });

// Better
const logger = createLogger('UserProfile');
logger.log('Profile updated:', { userId: user.id, changes: updatedFields });
```

### 3. Use Structured Logging

```javascript
// Good
log('API request completed', {
  method: 'POST',
  url: '/api/users',
  status: 200,
  duration: '150ms'
});
```

### 4. Handle Errors Appropriately

```javascript
try {
  await riskyOperation();
} catch (error) {
  // Use logError for comprehensive error logging
  logError(error, 'UserService.updateProfile', { userId: user.id });
  throw error; // Re-throw if needed
}
```

## Environment Configuration

The logger automatically detects the environment using the `envConfig` utility:

- **Development**: `isDevelopment()` returns true
- **Staging**: `isStaging()` returns true  
- **Production**: `isProduction()` returns true

## Build-Time Console Removal

In addition to the runtime logger utility, the build process automatically removes all `console.*` statements in production builds using Terser:

```javascript
// vite.config.js
terserOptions: {
  compress: {
    drop_console: isProduction,
    drop_debugger: isProduction,
  },
}
```

This ensures that even if direct console usage slips through, it won't appear in production builds.

## ESLint Integration

The ESLint configuration warns about direct console usage to encourage using the logger utility:

```javascript
// eslint.config.js
'no-console': 'warn', // Always warn about console usage
```

## Common Patterns

### Service Layer Logging

```javascript
import { createLogger } from '@/utils/logger';
const logger = createLogger('ApiService');

export const fetchData = async (endpoint) => {
  logger.time('API Request');
  
  try {
    logger.log(`Fetching data from ${endpoint}`);
    const response = await fetch(endpoint);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }
    
    const data = await response.json();
    logger.log('Data fetched successfully:', data);
    return data;
    
  } catch (error) {
    logger.error('Failed to fetch data:', error);
    throw error;
  } finally {
    logger.timeEnd('API Request');
  }
};
```

### Component Logging

```javascript
<script setup>
import { onMounted, onUnmounted } from 'vue';
import { createLogger } from '@/utils/logger';

const logger = createLogger('MyComponent');

onMounted(() => {
  logger.log('Component mounted');
});

onUnmounted(() => {
  logger.log('Component unmounted');
});

const handleUserAction = () => {
  logger.log('User performed action');
};
</script>
```

## Debugging in Production

Since console logs are removed in production, use these alternatives for production debugging:

1. **Error Reporting**: Errors are still logged and can be captured by error reporting services
2. **Analytics**: Use analytics events for tracking user behavior
3. **Remote Logging**: Implement remote logging services for critical information
4. **Debug Builds**: Create special debug builds for production troubleshooting

## Summary

- Use the logger utility instead of direct console statements
- Logger automatically handles environment-specific logging
- Build process removes console statements in production
- ESLint warns about direct console usage
- Create module-specific loggers for better organization
- Use appropriate log levels for different types of information

This approach ensures optimal performance in production while maintaining excellent debugging capabilities during development.
