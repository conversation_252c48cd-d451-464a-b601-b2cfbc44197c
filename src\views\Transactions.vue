<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useTheme } from '../store/theme';
import { useBitmapRequest } from '../composables/useBitmapRequest';

// Get theme state
const { isDarkMode } = useTheme();

// Transaction icons
const CollectedIcon = new URL('../assets/transaction_types_icons1.png', import.meta.url).href;
const SpentIcon = new URL('../assets/transaction_types_icon2.png', import.meta.url).href;

// Router for back navigation
const router = useRouter();

// Transaction filter tabs
const tabs = ['All', 'Collected', 'Spent'];
const activeTab = ref('All');

// Initialize bitmap request
const { fetchData } = useBitmapRequest();

// Transaction data
const transactions = ref([]);

// Function to map API data to transaction format
const mapTransactionData = (apiData) => {
  if (!apiData?.Table0) {return [];}
  
  // Calculate date range for last 3 months
  const endDate = new Date();
  const startDate = new Date();
  startDate.setMonth(startDate.getMonth() - 3);
  
  return apiData.Table0
    .filter(tx => {
      const txDate = new Date(tx.TxnDate);
      return txDate >= startDate && txDate <= endDate;
    })
    .map(tx => ({
      id: tx.TxnId,
      type: tx.Descp,
      amount: tx.Pts,
      date: new Date(tx.TxnDate).toLocaleDateString('en-US', {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
      }),
      time: new Date(tx.TxnDate).toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
      }),
      category: tx.Category === 'Points' ? 'Collected' : 
        tx.Category === 'Redeem' ? 'Spent' : 'Spent',
    }));
};

// Load transactions
const loadTransactions = async () => {
  try {
    // Calculate date range for last 3 months
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 3);

    // Format dates as YYYY-MM-DD
    const formatDate = (date) => {
      return date.toISOString().split('T')[0];
    };

    const response = await fetchData(
      '8',
      '0',
      '',
      formatDate(startDate),
      formatDate(endDate),
    );
    
    if (response) {
      transactions.value = mapTransactionData(response);
    }
  } catch (err) {
    console.error('Error loading transactions:', err);
  }
};

// Load transactions on component mount
onMounted(() => {
  loadTransactions();
});

// Function to change active tab
const setActiveTab = (tab) => {
  activeTab.value = tab;
};

// Go back to previous page
const goBack = () => {
  router.back();
};

// Filtered transactions based on active tab
const filteredTransactions = computed(() => {
  if (activeTab.value === 'All') {
    return transactions.value;
  }
  return transactions.value.filter(transaction => transaction.category === activeTab.value);
});
</script>

<template>
  <div class="transactions-container">
    <!-- Header removed - using the main Header.vue component -->

    <!-- Tab Navigation -->
    <div class="tab-navigation">
      <button
        v-for="tab in tabs"
        :key="tab"
        class="tab-button"
        :class="{ active: activeTab === tab }"
        @click="setActiveTab(tab)"
      >
        {{ tab }}
      </button>
    </div>

    <!-- Transactions List -->
    <div class="transactions-list">
      <div v-if="filteredTransactions.length === 0" class="empty-state">
        <p>No transactions found.</p>
      </div>

      <div v-for="transaction in filteredTransactions" :key="transaction.id" class="transaction-card">
        <div class="transaction-icon" :class="{ 'spent': transaction.category === 'Spent', 'collected': transaction.category === 'Collected' }">
          <img v-if="transaction.category === 'Collected'" :src="CollectedIcon" alt="Collected" width="24" height="24" />
          <img v-else :src="SpentIcon" alt="Spent" width="24" height="24" />
        </div>
        <div class="transaction-details">
          <div class="transaction-info">
            <h3 class="transaction-type">{{ transaction.type }}</h3>
            <p class="transaction-date">{{ transaction.date }} · {{ transaction.time }}</p>
          </div>
          <div class="transaction-amount-container">
            <span class="transaction-amount">{{ transaction.amount.toFixed(1) }}</span>
            <span class="transaction-badge" :class="transaction.category.toLowerCase()">
              {{ transaction.category }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.transactions-container {
  padding: 0;
  width: 100%;
  margin: 0 auto;
  margin-bottom: 5rem; /* Space for bottom navigation */
  background-color: var(--background-color);
  min-height: 100vh;
  padding-top: env(safe-area-inset-top, 20px); /* Add safe area inset for notched phones */
}

/* Header removed - using the main Header.vue component */

/* Tab Navigation */
.tab-navigation {
  display: flex;
  background-color: var(--border-color);
  border-radius: 28px;
  margin: 1rem auto;
  padding: 0.25rem;
  width: 100%;
  max-width: 500px;
}

.tab-button {
  flex: 1;
  background: none;
  border: none;
  padding: 0.75rem 0;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-color);
  border-radius: 24px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-button.active {
  background-color: var(--card-background);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Transactions List */
.transactions-list {
  display: flex;
  flex-direction: column;
  padding: 0 1rem;
}

.transaction-card {
  display: flex;
  background-color: var(--card-background);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  align-items: center;
}

.transaction-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

.transaction-icon.collected {
  background-color: #E0F7F5;
  color: #4ECDC4;
}

.transaction-icon.spent {
  background-color: #FFF3E0;
  color: #FF9966;
}

.transaction-details {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.transaction-info {
  flex: 1;
}

.transaction-type {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
}

.transaction-date {
  font-size: 0.8rem;
  color: var(--text-light);
  margin: 0;
}

.transaction-amount-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.transaction-amount {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.25rem;
}

.transaction-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
}

.transaction-badge.collected {
  background-color: #E0F7F5;
  color: #4ECDC4;
}

.transaction-badge.spent {
  background-color: #FFF3E0;
  color: #FF9966;
}

.empty-state {
  background-color: var(--card-background);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  color: var(--text-light);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin: 1rem 0;
}

/* Responsive styles */
@media (max-width: 480px) {
  .tab-navigation {
    margin: 1rem;
    max-width: calc(100% - 2rem);
  }

  .transactions-list {
    padding: 0 1rem;
  }
}

@media (min-width: 769px) {
  .tab-navigation {
    max-width: 500px;
  }

  .transactions-list {
    max-width: 500px;
    margin: 0 auto;
  }
}
</style>
