<script setup>
import { onMounted } from 'vue';
import PointsCard from '../components/PointsCard.vue';
import FeatureIcons from '../components/FeatureIcons.vue';
import Highlights from '../components/Highlights.vue';
import NewsUpdates from '../components/NewsUpdates.vue';
import { usePoints } from '@/composables/usePoints';
import { useHomeBitmap } from '@/composables/useHomeBitmap';

const { points, fetchPoints } = usePoints();
const { data, isLoading, error, fetchHomeBitmap } = useHomeBitmap();

onMounted(() => {
  fetchPoints();
  fetchHomeBitmap();
});
</script>

<template>
  <main class="main-content">
    <PointsCard :points="points" />
    <FeatureIcons />
    <Highlights
      :table0="data?.data?.[0]?.Table0 || []"
      :loading="isLoading"
      :error="error"
    />
    <NewsUpdates
      :table1="data?.data?.[0]?.Table1 || []"
      :loading="isLoading"
      :error="error"
    />
  </main>
</template>

<style scoped>
.main-content {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 1rem;
}
</style>
