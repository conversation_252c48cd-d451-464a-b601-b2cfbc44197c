import { test, expect } from '@playwright/test';

/**
 * Test suite specifically for the BottomNavigation.vue component
 * Tests component structure, styling, and theme changes
 */
test.describe('BottomNavigation Component Tests', () => {
  // Mock login function to bypass authentication
  async function mockLogin(page) {
    await page.goto('/login');
    
    // Mock the authentication state using localStorage
    await page.evaluate(() => {
      localStorage.setItem('isAuthenticated', 'true');
      localStorage.setItem('user', JSON.stringify({
        id: '123',
        name: 'Test User',
        phone: '+601234567890',
        points: 1000
      }));
    });
    
    // Navigate to home page
    await page.goto('/');
  }

  // Define viewport sizes for testing
  const viewports = {
    mobile: { width: 375, height: 667 },  // iPhone SE / common mobile size
    tablet: { width: 768, height: 1024 }, // iPad / tablet size
    desktop: { width: 1280, height: 800 } // Standard desktop size
  };

  // Test component structure
  test('BottomNavigation component should have correct structure', async ({ page }) => {
    await mockLogin(page);
    
    // Check bottom navigation structure
    const bottomNav = page.locator('.bottom-nav');
    await expect(bottomNav).toBeVisible();
    
    // Check for all navigation items
    await expect(page.locator('.nav-item', { hasText: 'Home' })).toBeVisible();
    await expect(page.locator('.nav-item', { hasText: 'Vouchers' })).toBeVisible();
    await expect(page.locator('.nav-item', { hasText: 'Locations' })).toBeVisible();
    await expect(page.locator('.nav-item', { hasText: 'Profile' })).toBeVisible();
    
    // Check for QR code button
    await expect(page.locator('.qr-code-button')).toBeVisible();
    
    // Check for icons in each nav item
    await expect(page.locator('.nav-item:has-text("Home") .nav-icon')).toBeVisible();
    await expect(page.locator('.nav-item:has-text("Vouchers") .nav-icon')).toBeVisible();
    await expect(page.locator('.nav-item:has-text("Locations") .nav-icon')).toBeVisible();
    await expect(page.locator('.nav-item:has-text("Profile") .nav-icon')).toBeVisible();
    await expect(page.locator('.qr-code-button .qr-icon')).toBeVisible();
  });

  // Test component styling in light mode
  test('BottomNavigation component should have correct light mode styling', async ({ page }) => {
    await mockLogin(page);
    
    // Ensure light mode is active
    await page.goto('/profile');
    const themeItem = page.locator('.settings-item', { hasText: 'Theme' });
    const themeValue = page.locator('.settings-item:has-text("Theme") .setting-value');
    const currentTheme = await themeValue.textContent();
    
    // If not in light mode, toggle to light mode
    if (currentTheme?.includes('Dark')) {
      await themeItem.click();
      await page.waitForTimeout(300);
    }
    
    await page.goto('/');
    
    // Check bottom navigation has light mode class
    const bottomNav = page.locator('.bottom-nav');
    await expect(bottomNav).toHaveClass(/light-nav/);
    
    // Check background color in light mode
    const backgroundColor = await bottomNav.evaluate(el => {
      return window.getComputedStyle(el).backgroundColor;
    });
    
    // Should have a light background color (white or close to white)
    // This is a loose check since the exact RGB values might vary
    const isLightColor = await page.evaluate(color => {
      // Extract RGB values
      const match = color.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
      if (match) {
        const [r, g, b] = [parseInt(match[1]), parseInt(match[2]), parseInt(match[3])];
        // Check if it's a light color (high RGB values)
        return r > 200 && g > 200 && b > 200;
      }
      return false;
    }, backgroundColor);
    
    expect(isLightColor).toBeTruthy();
  });

  // Test component styling in dark mode
  test('BottomNavigation component should have correct dark mode styling', async ({ page }) => {
    await mockLogin(page);
    
    // Ensure dark mode is active
    await page.goto('/profile');
    const themeItem = page.locator('.settings-item', { hasText: 'Theme' });
    const themeValue = page.locator('.settings-item:has-text("Theme") .setting-value');
    const currentTheme = await themeValue.textContent();
    
    // If not in dark mode, toggle to dark mode
    if (!currentTheme?.includes('Dark')) {
      await themeItem.click();
      await page.waitForTimeout(300);
    }
    
    await page.goto('/');
    
    // Check bottom navigation has dark mode class
    const bottomNav = page.locator('.bottom-nav');
    await expect(bottomNav).toHaveClass(/dark-nav/);
    
    // Check background color in dark mode
    const backgroundColor = await bottomNav.evaluate(el => {
      return window.getComputedStyle(el).backgroundColor;
    });
    
    // Should have a dark background color
    // This is a loose check since the exact RGB values might vary
    const isDarkColor = await page.evaluate(color => {
      // Extract RGB values
      const match = color.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
      if (match) {
        const [r, g, b] = [parseInt(match[1]), parseInt(match[2]), parseInt(match[3])];
        // Check if it's a dark color (low RGB values)
        return r < 50 && g < 50 && b < 50;
      }
      return false;
    }, backgroundColor);
    
    expect(isDarkColor).toBeTruthy();
  });

  // Test component responsiveness
  test('BottomNavigation component should be responsive', async ({ page }) => {
    await mockLogin(page);
    
    // Test on mobile viewport
    await page.setViewportSize(viewports.mobile);
    
    // Check bottom navigation sizing on mobile
    const bottomNav = page.locator('.bottom-nav');
    const bottomNavMobileBounds = await bottomNav.boundingBox();
    expect(bottomNavMobileBounds.width).toBeLessThanOrEqual(viewports.mobile.width);
    
    // Check QR button size on mobile
    const qrCodeButton = page.locator('.qr-code-button');
    const qrCodeButtonMobileBounds = await qrCodeButton.boundingBox();
    expect(qrCodeButtonMobileBounds.width).toBeLessThanOrEqual(60); // Should be 55-60px on mobile
    
    // Check nav icon size on mobile
    const navIcon = page.locator('.nav-icon').first();
    const navIconMobileBounds = await navIcon.boundingBox();
    expect(navIconMobileBounds.width).toBeLessThanOrEqual(22); // Should be 20-22px on mobile
    
    // Test on desktop viewport
    await page.setViewportSize(viewports.desktop);
    
    // Check bottom navigation sizing on desktop
    const bottomNavDesktopBounds = await bottomNav.boundingBox();
    expect(bottomNavDesktopBounds.width).toBeLessThan(viewports.desktop.width); // Should not be full width on desktop
    expect(bottomNavDesktopBounds.width).toBeLessThanOrEqual(540); // Should be max 540px as per CSS
  });

  // Test active state styling
  test('BottomNavigation component should style active page correctly', async ({ page }) => {
    await mockLogin(page);
    
    // Navigate to Home and check active state
    await page.goto('/');
    await expect(page.locator('.nav-item', { hasText: 'Home' })).toHaveClass(/active/);
    
    // Check active item color
    const activeItemColor = await page.locator('.nav-item.active').evaluate(el => {
      return window.getComputedStyle(el).color;
    });
    
    // Navigate to Vouchers and check active state changes
    await page.goto('/vouchers');
    await expect(page.locator('.nav-item', { hasText: 'Vouchers' })).toHaveClass(/active/);
    await expect(page.locator('.nav-item', { hasText: 'Home' })).not.toHaveClass(/active/);
    
    // Navigate to Locations and check active state changes
    await page.goto('/locations');
    await expect(page.locator('.nav-item', { hasText: 'Locations' })).toHaveClass(/active/);
    await expect(page.locator('.nav-item', { hasText: 'Vouchers' })).not.toHaveClass(/active/);
    
    // Navigate to Profile and check active state changes
    await page.goto('/profile');
    await expect(page.locator('.nav-item', { hasText: 'Profile' })).toHaveClass(/active/);
    await expect(page.locator('.nav-item', { hasText: 'Locations' })).not.toHaveClass(/active/);
  });

  // Test QR code button styling and behavior
  test('QR code button should have correct styling and behavior', async ({ page }) => {
    await mockLogin(page);
    
    // Check QR code button styling
    const qrCodeButton = page.locator('.qr-code-button');
    await expect(qrCodeButton).toBeVisible();
    
    // Check QR button background color
    const qrButtonBgColor = await qrCodeButton.evaluate(el => {
      return window.getComputedStyle(el).backgroundColor;
    });
    
    // Should have the primary color background (#26a69a or similar)
    const isPrimaryColor = await page.evaluate(color => {
      // Extract RGB values
      const match = color.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
      if (match) {
        const [r, g, b] = [parseInt(match[1]), parseInt(match[2]), parseInt(match[3])];
        // Check if it's close to the primary color #26a69a (38, 166, 154)
        return Math.abs(r - 38) < 30 && Math.abs(g - 166) < 30 && Math.abs(b - 154) < 30;
      }
      return false;
    }, qrButtonBgColor);
    
    expect(isPrimaryColor).toBeTruthy();
    
    // Check QR icon is visible and has correct styling
    const qrIcon = qrCodeButton.locator('.qr-icon');
    await expect(qrIcon).toBeVisible();
    
    // Check QR label is visible and has correct styling
    const qrLabel = qrCodeButton.locator('.qr-label');
    await expect(qrLabel).toBeVisible();
    const qrLabelColor = await qrLabel.evaluate(el => {
      return window.getComputedStyle(el).color;
    });
    
    // QR label should be white
    const isWhiteText = await page.evaluate(color => {
      // Extract RGB values
      const match = color.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
      if (match) {
        const [r, g, b] = [parseInt(match[1]), parseInt(match[2]), parseInt(match[3])];
        // Check if it's white or close to white
        return r > 240 && g > 240 && b > 240;
      }
      return false;
    }, qrLabelColor);
    
    expect(isWhiteText).toBeTruthy();
    
    // Test QR button navigation
    await qrCodeButton.click();
    await expect(page).toHaveURL('/qrcode');
  });
});
