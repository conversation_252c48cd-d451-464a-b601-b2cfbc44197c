<script setup>
import { useRouter, useRoute } from 'vue-router';
import { useTheme } from '../store/theme';
import { useI18n } from 'vue-i18n';
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { decodeURL } from '../utils/url';

const router = useRouter();
const route = useRoute();
const { isDarkMode } = useTheme();
const { t } = useI18n();

// Get the URL from the route params and decode it
const url = ref(route.params.url ? decodeURL(route.params.url) : '');
const title = ref(route.params.title ? decodeURL(route.params.title) : t('common.browser', 'Browser'));
const isLoading = ref(true);

// Handle iframe load event
const handleLoad = () => {
  isLoading.value = false;
};

// Close the browser and go back to profile page
const closeBrowser = () => {
  // Get the return path from sessionStorage or default to profile
  const returnPath = window.sessionStorage.getItem('browserReturnPath') || '/profile';
  router.push(returnPath);
};

// Watch for route changes to update URL
watch(() => route.params, (newParams) => {
  if (newParams.url) {
    url.value = decodeURL(newParams.url);
    isLoading.value = true;
  }
  if (newParams.title) {
    title.value = decodeURL(newParams.title);
  }
});

onMounted(() => {
  // Set initial values from route params
  if (route.params.url) {
    url.value = decodeURL(route.params.url);
  }
  if (route.params.title) {
    title.value = decodeURL(route.params.title);
  } else {
    title.value = t('common.browser', 'Browser');
  }

  // Prevent body scrolling when in-app browser is open
  document.body.style.overflow = 'hidden';
});

// Clean up when component is unmounted
onUnmounted(() => {
  // Restore body scrolling
  document.body.style.overflow = '';
});
</script>

<template>
  <div class="in-app-browser" :class="{ 'dark-theme': isDarkMode }">
    <div class="browser-header">
      <button class="close-button" aria-label="Close" @click="closeBrowser">
        <span class="close-icon">×</span>
      </button>
      <h1 class="browser-title">{{ title }}</h1>
      <div class="spacer"></div>
    </div>

    <div class="browser-content">
      <div v-if="isLoading" class="loading-indicator">
        <div class="spinner"></div>
        <p>{{ t('common.loading', 'Loading...') }}</p>
      </div>

      <iframe
        v-if="url"
        :src="url"
        class="browser-iframe"
        frameborder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowfullscreen
        title="In-app browser content"
        sandbox="allow-same-origin allow-scripts allow-forms allow-popups"
        @load="handleLoad"
      ></iframe>
    </div>
  </div>
</template>

<style scoped>
.in-app-browser {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--background-color);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.browser-header {
  display: flex;
  align-items: center;
  padding: 0.8rem;
  background-color: var(--primary-color);
  color: white;
  position: sticky;
  top: 0;
  z-index: 10;
  width: 100%;
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0;
  width: 36px;
  height: 36px;
  min-width: 36px;
  min-height: 36px;
  margin-right: 8px;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.close-icon {
  font-size: 1.8rem;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-text {
  font-size: 0.9rem;
  display: none;
}

.browser-title {
  flex: 1;
  margin: 0;
  font-size: 1.2rem;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 0.5rem;
}

.spacer {
  width: 44px; /* Match close button width for balance */
}

.browser-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  width: 100%;
  height: calc(100% - 60px); /* Adjust based on header height */
}

.browser-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background-color: var(--background-color);
  overflow: auto; /* Allow scrolling within iframe */
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

.loading-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: var(--background-color);
  z-index: 5;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.dark-theme .spinner {
  border-color: rgba(255, 255, 255, 0.1);
  border-top-color: var(--primary-color);
}

.dark-theme .loading-indicator p {
  color: var(--text-color);
}

/* Desktop styles */
@media (min-width: 769px) {
  .in-app-browser {
    width: 90%;
    max-width: 800px;
    height: 90vh;
    top: 5vh;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  }

  .browser-header {
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    padding: 1rem;
  }

  .browser-content {
    height: calc(100% - 64px); /* Adjust based on desktop header height */
  }
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  .in-app-browser {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    transform: none;
    border-radius: 0;
    padding-top: env(safe-area-inset-top, 0);
    padding-bottom: env(safe-area-inset-bottom, 0);
  }

  .browser-header {
    border-radius: 0;
    padding-top: max(0.8rem, env(safe-area-inset-top, 0.8rem));
  }

  .browser-content {
    height: calc(100% - 60px - env(safe-area-inset-top, 0px) - env(safe-area-inset-bottom, 0px));
  }

  .close-button {
    width: 40px;
    height: 40px;
    min-width: 40px;
    min-height: 40px;
  }
}
</style>
