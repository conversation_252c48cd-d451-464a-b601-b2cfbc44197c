<script setup>
// Highlights component
import { useI18n } from 'vue-i18n';
import { ref, onMounted, onBeforeUnmount, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useTheme } from '../store/theme';

const props = defineProps({
  table0: { type: Array, default: () => [] },
  loading: Boolean,
  error: Object,
});

const { t } = useI18n();
const router = useRouter();
const { isDarkMode } = useTheme();

// State for the carousel
const currentIndex = ref(0);
const isScrolling = ref(false);
const touchStartX = ref(0);
const touchEndX = ref(0);
const autoScrollInterval = ref(null);
const highlightsContainer = ref(null);
const scrollTimeout = ref(null);
const openedCardIndex = ref(null); // Track which card is opened

// Computed properties
const highlights = computed(() => {
  if (!props.table0) {return [];} 
  return props.table0.map(item => ({
    id: item.AdsId,
    title: item.Subject,
    description: item.Descp.replace(/\r\n/g, '\n'),
    image: item.ImgUrl,
    link: `/highlights/${item.AdsId}`,
    date: item.AdsDate,
  }));
});

const currentHighlight = computed(() => highlights.value[currentIndex.value]);

const navigateToLink = (link) => {
  router.push(link);
};

// Handle manual scrolling
const handleScroll = () => {
  if (!highlightsContainer.value || isScrolling.value) {return;}

  // Clear previous timeout
  if (scrollTimeout.value) {
    clearTimeout(scrollTimeout.value);
  }

  // Set a timeout to detect when scrolling stops
  scrollTimeout.value = setTimeout(() => {
    const container = highlightsContainer.value;
    const scrollPosition = container.scrollLeft;
    const cardWidth = container.clientWidth;

    // Calculate which card is most visible
    const newIndex = Math.round(scrollPosition / cardWidth);

    // Update current index if it's valid
    if (newIndex >= 0 && newIndex < highlights.value.length) {
      currentIndex.value = newIndex;
    }

    // Scroll to the centered position of the current card
    scrollToCard(currentIndex.value, true);
  }, 150);
};

// Scroll to a specific card
const scrollToCard = (index, smooth = false) => {
  if (!highlightsContainer.value) {return;}

  const container = highlightsContainer.value;
  const cardWidth = container.clientWidth;
  const scrollPosition = index * cardWidth;

  isScrolling.value = true;

  container.scrollTo({
    left: scrollPosition,
    behavior: smooth ? 'smooth' : 'auto',
  });

  // Reset scrolling flag after animation completes
  setTimeout(() => {
    isScrolling.value = false;
  }, 500);

  currentIndex.value = index;
};

// Handle touch events for mobile swiping
const handleTouchStart = (e) => {
  touchStartX.value = e.touches[0].clientX;
};

const handleTouchMove = (e) => {
  touchEndX.value = e.touches[0].clientX;
};

const handleTouchEnd = () => {
  const difference = touchStartX.value - touchEndX.value;

  // Minimum swipe distance (px)
  const minSwipeDistance = 50;

  if (Math.abs(difference) < minSwipeDistance) {return;}

  if (difference > 0) {
    // Swipe left, go to next
    goToNext();
  } else {
    // Swipe right, go to previous
    goToPrevious();
  }
};

// Navigation functions
const goToNext = () => {
  const newIndex = (currentIndex.value + 1) % highlights.value.length;
  scrollToCard(newIndex, true);
};

const goToPrevious = () => {
  const newIndex = (currentIndex.value - 1 + highlights.value.length) % highlights.value.length;
  scrollToCard(newIndex, true);
};

// Go to a specific slide
const goToSlide = (index) => {
  scrollToCard(index, true);
};

// Start auto-scrolling
const startAutoScroll = () => {
  stopAutoScroll(); // Clear any existing interval

  autoScrollInterval.value = setInterval(() => {
    if (!isScrolling.value) {
      goToNext();
    }
  }, 3000); // Change slide every 3 seconds
};

// Stop auto-scrolling
const stopAutoScroll = () => {
  if (autoScrollInterval.value) {
    clearInterval(autoScrollInterval.value);
    autoScrollInterval.value = null;
  }
};

// Format description text with proper HTML
const formatDescription = (text) => {
  if (!text) {return '';}
  
  // Split the text into lines
  const lines = text.split('\n');
  
  // Process each line
  return lines.map(line => {
    // Check if line starts with a number followed by a dot (e.g., "1. ")
    if (/^\d+\.\s/.test(line)) {
      return `<div class="description-step">${line}</div>`;
    }
    // Regular paragraph
    return `<div class="description-paragraph">${line}</div>`;
  }).join('');
};

// Toggle card open/close
const toggleCard = (index) => {
  openedCardIndex.value = openedCardIndex.value === index ? null : index;
};

const goToHighlightDetail = (highlight) => {
  router.push({ path: `/highlights/${highlight.id}` });
};

// Lifecycle hooks
onMounted(() => {
  startAutoScroll();
  setTimeout(() => {
    scrollToCard(0);
  }, 100);
});

onBeforeUnmount(() => {
  stopAutoScroll();
  if (scrollTimeout.value) {
    clearTimeout(scrollTimeout.value);
  }
});
</script>

<template>
  <div class="highlights-section">
    <h2 class="section-title">{{ t('highlights.title') }}</h2>
    
    <!-- Loading state -->
    <div v-if="loading" class="highlights-loading">
      <div class="loading-spinner"></div>
      <p>{{ t('common.loading') }}</p>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="highlights-error">
      <p>{{ t('common.error') }}</p>
    </div>

    <!-- Content -->
    <template v-else>
      <div
        v-if="highlights.length > 0"
        ref="highlightsContainer"
        class="highlights-container"
        @scroll="handleScroll"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
      >
        <div
          v-for="(highlight, index) in highlights"
          :key="highlight.id"
          class="highlight-card home-image-card"
          :class="{ 'active': index === currentIndex }"
          @click="goToHighlightDetail(highlight)"
        >
          <div class="highlight-content home-image-content">
            <div class="highlight-image-container home-full-image">
              <img :src="highlight.image" :alt="highlight.title" class="highlight-image home-full-image" />
            </div>
          </div>
        </div>
      </div>

      <!-- No highlights message -->
      <div v-else class="no-highlights">
        <p>{{ t('highlights.noHighlights') }}</p>
      </div>

      <!-- Indicators -->
      <div v-if="highlights.length > 0" class="highlight-indicators">
        <button
          v-for="(highlight, index) in highlights"
          :key="`indicator-${highlight.id}`"
          class="indicator-dot"
          :class="{ 'active': index === currentIndex }"
          :aria-label="`Go to slide ${index + 1}`"
          @click="goToSlide(index)"
        ></button>
      </div>
    </template>
  </div>
</template>

<style scoped>
.highlights-section {
  padding: 0 1rem;
  margin-bottom: 1.5rem;
  position: relative;
}

.highlights-container {
  display: flex;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  padding-bottom: 0.5rem;
  margin: 0 -1rem;
  padding-left: 1rem;
  -ms-overflow-style: none;  /* Hide scrollbar for IE and Edge */
  scrollbar-width: none;  /* Hide scrollbar for Firefox */
}

.highlights-container::-webkit-scrollbar {
  display: none;  /* Hide scrollbar for Chrome, Safari and Opera */
}

.highlight-card {
  flex: 0 0 100%;
  scroll-snap-align: center;
  width: calc(100% - 2rem);
  height: 150px;
  background-color: var(--card-background);
  border-radius: 12px;
  margin-right: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.highlight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.highlight-card.active {
  border: 2px solid var(--primary-color);
}

.highlight-content {
  display: flex;
  height: 100%;
  padding: 1rem;
}

.highlight-image-container {
  flex: 0 0 30%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.highlight-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.highlight-text {
  flex: 1;
  padding-left: 1rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.highlight-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.highlight-description {
  font-size: 0.9rem;
  color: var(--text-light);
  line-height: 1.6;
  white-space: pre-line;
  margin-bottom: 0.5rem;
}

.highlight-description :deep(.description-step) {
  margin: 0.5rem 0;
  padding-left: 1rem;
  position: relative;
}

.highlight-description :deep(.description-paragraph) {
  margin: 0.5rem 0;
}

.highlight-description :deep(.description-step)::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.5rem;
  width: 4px;
  height: 4px;
  background-color: var(--primary-color);
  border-radius: 50%;
}

.highlight-date {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-top: 0.5rem;
}

.highlight-indicators {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  gap: 0.5rem;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--border-color);
  border: none;
  padding: 0;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.indicator-dot.active {
  background-color: var(--primary-color);
  transform: scale(1.2);
}

/* Responsive styles */
@media (min-width: 769px) {
  .highlights-section {
    max-width: 800px;
    margin: 0 auto 1.5rem auto;
  }

  .highlight-card {
    height: 180px;
  }

  .highlight-title {
    font-size: 1.4rem;
  }

  .highlight-description {
    font-size: 1rem;
  }
}

.highlights-loading,
.highlights-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 150px;
  padding: 2rem;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.retry-button {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.retry-button:hover {
  background-color: var(--primary-color-dark);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.no-highlights {
  text-align: center;
  padding: 2rem;
  color: var(--text-light);
}

.highlight-image-container.full-image,
.highlight-image.full-image {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.highlight-image.full-image {
  object-fit: cover;
  width: 100%;
  height: 100%;
  border-radius: 12px;
}

.highlight-text.only-description {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

/* Home page highlight image: edge-to-edge, no border, no shadow, no background */
.home-image-card {
  background: transparent !important;
  border: 1.5px solid var(--border-color, #333) !important;
  box-shadow: none !important;
  border-radius: 14px !important;
  margin-right: 1rem;
  padding: 0 !important;
  overflow: hidden;
  width: 100%;
  height: auto;
  display: block;
}

.home-image-content,
.highlight-image-container.home-full-image {
  width: 100%;
  height: auto;
  padding: 0 !important;
  margin: 0 !important;
  background: transparent !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  display: block;
}

.highlight-image.home-full-image {
  object-fit: contain;
  width: 100%;
  height: auto;
  border-radius: 12px !important;
  background: transparent !important;
  display: block;
  max-width: 100%;
  max-height: 400px;
}
</style>
