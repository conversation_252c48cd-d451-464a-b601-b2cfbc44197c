# API Service Implementation

This document provides an overview of the API service implementation for the RewardRedemption application.

## Overview

The API service layer is designed to:

1. Provide a consistent interface for making API requests
2. Handle authentication and error handling
3. Support both mock data and real API endpoints
4. Provide type-safe and reactive data access

## Structure

The API service implementation consists of the following components:

### HTTP Client

The HTTP client (`src/utils/httpClient.js`) provides a wrapper around the Fetch API with:

- Consistent error handling
- Authentication token management
- Request timeout handling
- Response parsing

### Service Modules

Each API endpoint group has its own service module:

- `authService.js` - Authentication with OTP
- `userService.js` - User profile management
- `apiLocationService.js` - Location-related operations
- `voucherService.js` - Voucher management
- `transactionService.js` - Transaction history
- `movementService.js` - Movement tracking and challenges
- `forestService.js` - Forest/environmental impact tracking

### Mock Data

The `mockData.js` file contains mock data for testing and development while waiting for the backend to be implemented.

## Usage

### Configuration

The API services can be configured in `src/services/index.js`:

```javascript
export const API_CONFIG = {
  // Set to false to use real API endpoints
  USE_MOCK_DATA: true,
  
  // Base URL for API requests
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000',
  
  // API timeout in milliseconds
  API_TIMEOUT: 30000
};
```

### Using Services in Components

Import and use the services in your Vue components:

```javascript
import { useAuthService, useUserService } from '../services';

// In your component setup
const { 
  user, 
  isLoggedIn, 
  sendOTP, 
  verifyOTP 
} = useAuthService();

const { 
  profile, 
  getUserProfile, 
  updateUserProfile 
} = useUserService();
```

### Example Component

An example component demonstrating the usage of all services is provided in `src/components/examples/ApiExample.vue`.

## Authentication Flow

The authentication flow is implemented as follows:

1. User enters phone number
2. Call `sendOTP(phoneNumber)` to send OTP
3. User enters OTP
4. Call `verifyOTP(otp)` to verify OTP
5. On successful verification, the user is logged in and a JWT token is stored

## Error Handling

Each service provides consistent error handling:

- `error` ref for error messages
- `isLoading` ref for loading state
- `resetError()` method to clear errors

## Mock vs Real Implementation

Each service has both mock and real implementations:

- Mock implementation uses the data from `mockData.js`
- Real implementation makes actual API requests

To switch between mock and real implementations, set `USE_MOCK_DATA` to `false` in each service file or globally in `src/services/index.js`.

## Response Format

All API responses follow a consistent format:

### Success Response

```json
{
  "success": true,
  "message": "Optional success message",
  "data": {
    // Response data specific to each endpoint
  }
}
```

### Error Response

```json
{
  "success": false,
  "message": "Error message",
  "error": {
    "code": "ERROR_CODE"
  }
}
```

## Service-Specific Notes

### Location Service

The location service includes utilities for:

- Calculating distance between coordinates
- Formatting distance text
- Getting nearby locations based on user coordinates

### Voucher Service

The voucher service supports:

- Filtering vouchers by status (Active, Used, Expired)
- Voucher redemption with validation

### Movement Service

The movement service manages:

- Movement statistics
- Available challenges
- Active challenge tracking
- Challenge progress updates

### Forest Service

The forest service handles:

- Forest statistics
- CO2 absorption tracking
- Tree growth stages

## Next Steps

1. **Backend Integration**: When the backend is ready, set `USE_MOCK_DATA` to `false` to use real API endpoints.
2. **Error Handling**: Implement more robust error handling and retry mechanisms.
3. **Caching**: Add caching for frequently accessed data.
4. **Offline Support**: Implement offline support with local storage.
5. **Testing**: Add unit tests for API services.

## Example Usage

See `src/components/examples/ApiExample.vue` for a complete example of using all API services.
