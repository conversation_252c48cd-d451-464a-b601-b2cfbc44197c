<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useTheme } from '../store/theme';
import { useI18n } from 'vue-i18n';
import { useBitmapRequest } from '../composables/useBitmapRequest';
import QRCodeVue from 'qrcode.vue';

const router = useRouter();

// Get theme state and i18n
const { isDarkMode } = useTheme();
const { t } = useI18n();
const { data, isLoading, error, fetchData } = useBitmapRequest();

// Voucher status tabs
const tabs = [
  { key: 'Active', label: t('vouchers.active') },
  { key: 'Used', label: t('vouchers.used') },
  { key: 'Expired', label: t('vouchers.expired') },
];
const activeTab = ref('Active');

// Voucher data
const vouchers = ref([]);

// QR code related state
const showQr = ref(false);
const qrValue = ref('');
const isQrLoading = ref(false);
const qrError = ref('');

// Function to format date
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  });
};

// Function to check if voucher is expired
const isExpired = (expiryDate) => {
  return new Date(expiryDate) < new Date();
};

// Function to process voucher data
const processVoucherData = (data) => {
  const activeVouchers = (data?.Table0 || []).map(voucher => ({
    id: voucher.UniqueId,
    voucherId: voucher.VoucherId,
    title: voucher.Descp,
    expiryDate: formatDate(voucher.ExpiryDate),
    status: isExpired(voucher.ExpiryDate) ? 'Expired' : 'Active',
    imageUrl: voucher.ImgUrl,
    itemDescription: voucher.ItemDescp,
  }));

  const usedVouchers = (data?.Table2 || []).map(voucher => ({
    id: voucher.UniqueId,
    voucherId: voucher.VoucherId,
    title: voucher.Descp,
    redeemDate: formatDate(voucher.RedeemDate),
    status: 'Used',
    imageUrl: voucher.ImgUrl,
    itemDescription: voucher.ItemDescp,
  }));

  // Combine all vouchers
  vouchers.value = [...activeVouchers, ...usedVouchers];
};

// Function to change active tab
const setActiveTab = (tab) => {
  activeTab.value = tab;
};

// Function to redeem a voucher
const redeemVoucher = async (event, voucher) => {
  event.stopPropagation();
  showQr.value = true;
  isQrLoading.value = true;
  qrError.value = '';
  qrValue.value = '';
  try {
    const result = await fetchData(512, '', `512:${voucher.voucherId}`);
    if (result && result.Table0 && result.Table0[0] && result.Table0[0].Val) {
      qrValue.value = result.Table0[0].Val;
    } else {
      qrError.value = 'Invalid QR data.';
    }
  } catch (err) {
    qrError.value = 'Error fetching QR code.';
    console.error('Error fetching QR code:', err);
  } finally {
    isQrLoading.value = false;
  }
};

const closeQr = () => {
  showQr.value = false;
  qrValue.value = '';
  qrError.value = '';
  isQrLoading.value = false;
};

const copyQrValue = async () => {
  try {
    await navigator.clipboard.writeText(qrValue.value);
    alert('QR value copied to clipboard!');
  } catch (e) {
    alert('Failed to copy QR value.');
  }
};

// Function to view voucher details
const viewVoucherDetails = (voucher) => {
  router.push(`/vouchers/${voucher.id}`);
};

// Filtered vouchers based on active tab
const filteredVouchers = computed(() => {
  return vouchers.value.filter(voucher => voucher.status === activeTab.value);
});

// Fetch voucher data on component mount
onMounted(async () => {
  try {
    const result = await fetchData('32');
    if (result) {
      processVoucherData(result);
    }
  } catch (err) {
    console.error('Error fetching vouchers:', err);
  }
});
</script>

<template>
  <div class="vouchers-container">
    <!-- Tab Navigation -->
    <div class="tab-navigation">
      <button
        v-for="tab in tabs"
        :key="tab.key"
        class="tab-button"
        :class="{ active: activeTab === tab.key }"
        @click="setActiveTab(tab.key)"
      >
        {{ tab.label }}
      </button>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="loading-state">
      <p>{{ t('common.loading') }}</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-state">
      <p>{{ t('common.error') }}</p>
    </div>

    <!-- Vouchers List -->
    <div v-else class="vouchers-list">
      <div v-if="filteredVouchers.length === 0" class="empty-state">
        <p v-if="activeTab === 'Active'">{{ t('vouchers.noActiveVouchers') }}</p>
        <p v-else-if="activeTab === 'Used'">{{ t('vouchers.noUsedVouchers') }}</p>
        <p v-else-if="activeTab === 'Expired'">{{ t('vouchers.noExpiredVouchers') }}</p>
      </div>

      <div
        v-for="voucher in filteredVouchers"
        :key="voucher.id"
        class="voucher-card"
        @click="viewVoucherDetails(voucher)"
      >
        <div v-if="voucher.imageUrl" class="voucher-image">
          <img :src="voucher.imageUrl" :alt="voucher.title">
        </div>
        <div class="voucher-details">
          <h3 class="voucher-title">{{ voucher.title }}</h3>
          <p v-if="voucher.itemDescription" class="voucher-item">{{ voucher.itemDescription }}</p>
          <p v-if="voucher.expiryDate" class="voucher-expiry">
            {{ t('vouchers.expiresOn') }}: {{ voucher.expiryDate }}
          </p>
          <p v-if="voucher.redeemDate" class="voucher-redeem">
            {{ t('vouchers.redeemedOn') }}: {{ voucher.redeemDate }}
          </p>

          <button
            v-if="voucher.status === 'Active'"
            class="redeem-button"
            @click="redeemVoucher($event, voucher)"
          >
            {{ t('vouchers.redeem') }}
          </button>

          <div v-if="voucher.status === 'Used'" class="status-badge used">
            {{ t('vouchers.used') }}
          </div>

          <div v-if="voucher.status === 'Expired'" class="status-badge expired">
            {{ t('vouchers.expired') }}
          </div>
        </div>
      </div>
    </div>

    <!-- QR Code Modal -->
    <div v-if="showQr" class="qr-modal">
      <div class="qr-content">
        <div v-if="isQrLoading" class="qr-loading">
          <p>Loading QR code...</p>
        </div>
        <div v-else-if="qrError" class="qr-error">
          <p>{{ qrError }}</p>
        </div>
        <div v-else>
          <QRCodeVue :value="qrValue" :size="200" style="max-width: 80vw; height: auto;" />
        </div>
        <button :disabled="isQrLoading" class="qr-btn" style="margin-top:1rem" @click="closeQr">Close</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.vouchers-container {
  padding: 1rem;
  width: 100%;
  margin: 0 auto;
  margin-bottom: 7rem; /* Increased space for bottom navigation */
  background-color: var(--background-color);
  min-height: 100vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

/* Title removed - using the main Header.vue component */

/* Tab Navigation */
.tab-navigation {
  display: flex;
  background-color: var(--border-color);
  border-radius: 12px;
  margin-bottom: 1.5rem;
  width: 100%;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  overflow: hidden;
}

.tab-button {
  flex: 1;
  background: none;
  border: none;
  padding: 0.75rem 0;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-button.active {
  background-color: var(--card-background);
  color: var(--text-color);
  font-weight: 600;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Vouchers List */
.vouchers-list {
  display: flex;
  flex-direction: column;
}

.voucher-card {
  display: flex;
  flex-direction: column;
  background-color: var(--card-background);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  margin: 0 auto 1rem auto;
  width: 100%;
  max-width: 500px;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  border: 1px solid var(--border-color);
  min-height: unset;
}

.voucher-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.voucher-image {
  width: 100%;
  height: 140px;
  min-width: unset;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  background: none;
  overflow: hidden;
  border-right: none;
  border-bottom: 1px dashed var(--border-color);
}

.voucher-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.voucher-details {
  flex: 1;
  padding: 1rem;
  border-left: none;
  position: relative;
  background-color: var(--card-background);
  min-height: unset;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.voucher-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: var(--text-color);
}

.voucher-expiry {
  font-size: 0.8rem;
  color: var(--text-light);
  margin: 0 0 1rem 0;
}

.redeem-button {
  background-color: #4ECDC4;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 0.5rem 1.25rem;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: absolute;
  right: 1rem;
  bottom: 1rem;
}

.redeem-button:hover {
  background-color: #3bb3aa;
}

.status-badge {
  display: inline-block;
  padding: 0.5rem 1.25rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-align: center;
  text-transform: uppercase;
  position: absolute;
  right: 1rem;
  bottom: 1rem;
}

.status-badge.used {
  background-color: #9e9e9e;
  color: white;
}

.status-badge.expired {
  background-color: #f44336;
  color: white;
}

.empty-state {
  background-color: var(--card-background);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  color: var(--text-light);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

/* Dark theme specific adjustments */
:global(.dark-theme) .tab-navigation {
  background-color: #333333;
}

:global(.dark-theme) .tab-button.active {
  background-color: #1e1e1e;
}

:global(.dark-theme) .voucher-card {
  border-color: #333333;
}

:global(.dark-theme) .voucher-details {
  border-left-color: #333333;
}

/* Responsive styles */
@media (max-width: 480px) {
  .vouchers-container {
    padding: 0.5rem;
  }

  .voucher-image {
    width: 100%;
    height: 80px;
    min-width: unset;
  }

  .tab-navigation {
    max-width: 100%;
  }

  .empty-state {
    max-width: 100%;
  }

  .voucher-title {
    font-size: 0.9rem;
  }

  .voucher-expiry {
    font-size: 0.75rem;
  }

  .redeem-button, .status-badge {
    padding: 0.4rem 1rem;
    font-size: 0.75rem;
    right: 0.75rem;
    bottom: 0.75rem;
  }
}

.voucher-item {
  font-size: 0.8rem;
  color: var(--text-light);
  margin: 0.25rem 0;
}

.voucher-redeem {
  font-size: 0.8rem;
  color: var(--text-light);
  margin: 0 0 1rem 0;
}

.loading-state,
.error-state {
  background-color: var(--card-background);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  color: var(--text-light);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.error-state {
  color: #f44336;
}

.qr-modal {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex; align-items: center; justify-content: center;
  z-index: 1000;
  padding: 1rem;
}
.qr-content {
  background: #fff;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  max-width: 400px;   /* Desktop: fixed max width */
  width: 100%;
  box-sizing: border-box;
}
.qr-btn {
  background-color: #4ECDC4;
  color: white;
  border: none;
  border-radius: 10px;
  padding: 0.7rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  margin: 0.5rem 0 0 0;
  width: 100%;
  max-width: 300px;
}
.qr-btn:disabled {
  background-color: #b2dfdb;
  cursor: not-allowed;
}
@media (max-width: 600px) {
  .qr-content {
    padding: 1rem 0.5rem;
    border-radius: 8px;
    max-width: 95vw;  /* Mobile: nearly full width */
  }
  .qr-btn {
    font-size: 0.95rem;
    padding: 0.6rem 1rem;
    max-width: 100%;
  }
}
</style>
