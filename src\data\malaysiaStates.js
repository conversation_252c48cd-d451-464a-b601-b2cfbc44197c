/**
 * List of Malaysian states with their codes
 */
export const malaysiaStates = [
  { code: '<PERSON>H<PERSON>', name: '<PERSON><PERSON>' },
  { code: '<PERSON>D<PERSON>', name: '<PERSON><PERSON>' },
  { code: '<PERSON><PERSON><PERSON>', name: '<PERSON><PERSON><PERSON>' },
  { code: '<PERSON><PERSON><PERSON>', name: 'Kuala Lumpur' },
  { code: '<PERSON><PERSON><PERSON>', name: '<PERSON><PERSON>' },
  { code: '<PERSON><PERSON><PERSON>', name: '<PERSON><PERSON>' },
  { code: 'N<PERSON>', name: '<PERSON><PERSON><PERSON> Sembilan' },
  { code: 'PHG', name: '<PERSON><PERSON>' },
  { code: 'PNG', name: '<PERSON><PERSON><PERSON>' },
  { code: 'PR<PERSON>', name: '<PERSON><PERSON>' },
  { code: '<PERSON><PERSON>', name: '<PERSON><PERSON>' },
  { code: 'PJ<PERSON>', name: '<PERSON><PERSON><PERSON>' },
  { code: 'SB<PERSON>', name: 'Sabah' },
  { code: 'SW<PERSON>', name: 'Sarawak' },
  { code: 'S<PERSON>', name: 'Selangor' },
  { code: 'TRG', name: '<PERSON><PERSON><PERSON><PERSON>' }
];

/**
 * Get state by code
 * @param {string} code - State code
 * @returns {object|null} State object or null if not found
 */
export const getStateByCode = (code) => {
  return malaysiaStates.find(state => state.code === code) || null;
};

/**
 * Get state by name
 * @param {string} name - State name
 * @returns {object|null} State object or null if not found
 */
export const getStateByName = (name) => {
  return malaysiaStates.find(state => state.name.toLowerCase() === name.toLowerCase()) || null;
};
