# Changelog

All notable changes to the RVMPlus Rewards project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.1.0] - 2024-02-24

### Added
- Transaction history view with filtering (All, Collected, Spent)
- Integration with bitmap API for transaction data
- 3-month transaction history limit for better performance

### Changed
- Updated transaction display to show real data from API
- Improved date formatting for transaction history
- Optimized transaction data loading with date filtering

## [1.0.0] - 2023-11-15

### Added
- Mobile phone authentication with username/password
- Responsive design for mobile and desktop
- Vue Router for navigation
- Bottom navigation bar with U curve design
- Dark and light mode support
- Multiple language support (English, Malay, Chinese)
- Forest visualization with dynamic tree growth based on progress
- Weather visualization based on user location
- QR code scanning functionality
- Voucher redemption system
- User profile management
- Movement tracking and challenges
- Help & Support section with in-app browser
- Environment configuration for development, staging, and production

### Changed
- Switched from OTP authentication to username/password login
- Optimized API calls with Pinia store caching
- Improved UI with rounded corners and better visibility in dark mode

### Fixed
- Fixed SVG loading issues in production build
- Fixed dark mode text visibility issues
- Fixed alignment of icons and text in UI components
- Fixed scrollable content in UI pages

### Removed
- Removed OTP-related code as it's no longer used
- Removed language selection field from profile page
- Removed duplicate headers

## [Unreleased]
### Changed
- Voucher image in `Vouchers.vue` is now displayed at the top of the card and fills the full width for a modern look.
- Improved QR code modal responsiveness and appearance in `Vouchers.vue`: modal now has a fixed max-width on desktop (400px), nearly full width on mobile, QR code is centered, raw QR value is hidden, and modal/buttons are more user-friendly on all device sizes.
- Home page highlights are now fetched from the API instead of using static data.
- Removed unused static highlight data and variables from `src/data/highlights.js`.
- News in `NewsUpdates.vue` is now fetched from the API (bitmap 3, Table1) and displayed in a carousel with indicators, matching the highlights section style.
