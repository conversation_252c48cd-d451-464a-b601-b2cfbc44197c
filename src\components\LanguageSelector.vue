<script setup>
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { updateHtmlLang } from '../utils/htmlLang';

const { locale } = useI18n();

// Toggle between languages
const toggleLanguage = () => {
  // Cycle through languages: en -> ms -> zh -> en
  if (locale.value === 'en') {
    locale.value = 'ms';
  } else if (locale.value === 'ms') {
    locale.value = 'zh';
  } else {
    locale.value = 'en';
  }

  // Save to localStorage and update HTML lang attribute
  localStorage.setItem('language', locale.value);
  updateHtmlLang(locale.value);
};
</script>

<template>
  <button class="language-toggle" @click="toggleLanguage">
    <span class="language-code">{{ locale.toUpperCase() }}</span>
  </button>
</template>

<style scoped>
.language-toggle {
  background: none;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.language-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.language-code {
  text-transform: uppercase;
}
</style>
