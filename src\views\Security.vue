<script setup>
import { ref } from 'vue';
import { useTheme } from '../store/theme';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { isDarkMode } = useTheme();

// Password change form
const currentPassword = ref('');
const newPassword = ref('');
const confirmPassword = ref('');
const showCurrentPassword = ref(false);
const showNewPassword = ref(false);
const showConfirmPassword = ref(false);

// Dummy login history data
const loginHistory = ref([
  {
    id: 1,
    device: 'iPhone 13',
    location: 'Kuala Lumpur, MY',
    ip: '203.142.xx.xx',
    date: '2023-10-15 14:32',
    current: true,
  },
  {
    id: 2,
    device: 'Chrome on Windows',
    location: 'Petaling Jaya, MY',
    ip: '203.142.xx.xx',
    date: '2023-10-10 09:15',
    current: false,
  },
  {
    id: 3,
    device: 'Safari on Mac',
    location: 'Kuala Lumpur, MY',
    ip: '203.142.xx.xx',
    date: '2023-10-05 18:45',
    current: false,
  },
]);

// Clear login history function
const clearLoginHistory = () => {
  if (window.confirm('Are you sure you want to clear your login history? This action cannot be undone.')) {
    // Keep only the current session
    loginHistory.value = loginHistory.value.filter(login => login.current);
    window.alert('Login history cleared successfully.');
  }
};

// Change password function (dummy)
const changePassword = () => {
  if (!currentPassword.value || !newPassword.value || !confirmPassword.value) {
    window.alert('Please fill in all password fields.');
    return;
  }

  if (newPassword.value !== confirmPassword.value) {
    window.alert('New password and confirm password do not match.');
    return;
  }

  // In a real app, this would call an API to change the password
  window.alert('Password change functionality would be implemented here.');

  // Clear form
  currentPassword.value = '';
  newPassword.value = '';
  confirmPassword.value = '';
};
</script>

<template>
  <div class="security-container">
    <!-- Change Password Section -->
    <div class="section">
      <h2 class="section-title">{{ t('security.changePassword', 'Change Password') }}</h2>
      <div class="card">
        <form class="password-form" @submit.prevent="changePassword">
          <!-- Current Password -->
          <div class="form-group">
            <label class="form-label">{{ t('security.currentPassword', 'Current Password') }}</label>
            <div class="password-input-container">
              <input
                v-model="currentPassword"
                :type="showCurrentPassword ? 'text' : 'password'"
                class="form-input"
                :placeholder="t('security.enterCurrentPassword', 'Enter current password')"
                required
              />
              <button
                type="button"
                class="toggle-password-btn"
                @click="showCurrentPassword = !showCurrentPassword"
              >
                <svg v-if="showCurrentPassword" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                  <line x1="1" y1="1" x2="23" y2="23"></line>
                </svg>
                <svg v-else xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                  <circle cx="12" cy="12" r="3"></circle>
                </svg>
              </button>
            </div>
          </div>

          <!-- New Password -->
          <div class="form-group">
            <label class="form-label">{{ t('security.newPassword', 'New Password') }}</label>
            <div class="password-input-container">
              <input
                v-model="newPassword"
                :type="showNewPassword ? 'text' : 'password'"
                class="form-input"
                :placeholder="t('security.enterNewPassword', 'Enter new password')"
                required
              />
              <button
                type="button"
                class="toggle-password-btn"
                @click="showNewPassword = !showNewPassword"
              >
                <svg v-if="showNewPassword" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                  <line x1="1" y1="1" x2="23" y2="23"></line>
                </svg>
                <svg v-else xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                  <circle cx="12" cy="12" r="3"></circle>
                </svg>
              </button>
            </div>
          </div>

          <!-- Confirm New Password -->
          <div class="form-group">
            <label class="form-label">{{ t('security.confirmPassword', 'Confirm New Password') }}</label>
            <div class="password-input-container">
              <input
                v-model="confirmPassword"
                :type="showConfirmPassword ? 'text' : 'password'"
                class="form-input"
                :placeholder="t('security.confirmNewPassword', 'Confirm new password')"
                required
              />
              <button
                type="button"
                class="toggle-password-btn"
                @click="showConfirmPassword = !showConfirmPassword"
              >
                <svg v-if="showConfirmPassword" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                  <line x1="1" y1="1" x2="23" y2="23"></line>
                </svg>
                <svg v-else xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                  <circle cx="12" cy="12" r="3"></circle>
                </svg>
              </button>
            </div>
          </div>

          <button type="submit" class="action-button">
            {{ t('security.updatePassword', 'Update Password') }}
          </button>
        </form>
      </div>
    </div>

    <!-- Login History Section -->
    <div class="section">
      <div class="section-header">
        <h2 class="section-title">{{ t('security.loginHistory', 'Login History') }}</h2>
        <button class="clear-history-btn" @click="clearLoginHistory">
          {{ t('security.clearHistory', 'Clear History') }}
        </button>
      </div>
      <div class="login-history-list">
        <div v-for="login in loginHistory" :key="login.id" class="login-history-item card">
          <div class="login-device">
            <strong>{{ login.device }}</strong>
            <span v-if="login.current" class="current-device-badge">{{ t('security.currentDevice', 'Current') }}</span>
          </div>
          <div class="login-details">
            <div class="login-location">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                <circle cx="12" cy="10" r="3"></circle>
              </svg>
              {{ login.location }}
            </div>
            <div class="login-ip">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect>
                <rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect>
                <line x1="6" y1="6" x2="6.01" y2="6"></line>
                <line x1="6" y1="18" x2="6.01" y2="18"></line>
              </svg>
              {{ login.ip }}
            </div>
            <div class="login-time">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
              {{ login.date }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.security-container {
  padding: 0;
  width: 100%;
  margin: 0 auto;
  padding-bottom: 7rem;
  background-color: var(--background-color);
  min-height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

.section {
  width: 100%;
  margin-bottom: 1.5rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 1rem;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.clear-history-btn {
  background-color: var(--error-color);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.clear-history-btn:hover {
  background-color: #d32f2f;
}

.card {
  background-color: var(--card-background);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.phone-info {
  margin-bottom: 1rem;
}

.info-label {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 0.25rem;
}

.info-value {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-color);
}

.action-button {
  width: 100%;
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-button:hover {
  background-color: var(--primary-dark);
}

/* Password Form Styles */
.password-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.password-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  padding-right: 3rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 0.95rem;
  background-color: var(--card-background);
  color: var(--text-color);
  transition: border-color 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.form-input::placeholder {
  color: var(--text-light);
}

.toggle-password-btn {
  position: absolute;
  right: 0.75rem;
  background: none;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: color 0.2s;
}

.toggle-password-btn:hover {
  color: var(--primary-color);
}

/* Login History Styles */

.login-history-list {
  padding: 0 1rem;
  width: 100%;
}

.login-history-item {
  padding: 1rem;
  margin-bottom: 0.75rem;
  background-color: var(--card-background);
  border-radius: 8px;
  width: 100%;
}

.login-device {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  color: var(--text-color);
  font-weight: 500;
}

.current-device-badge {
  background-color: var(--primary-color);
  color: white;
  font-size: 0.7rem;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
}

.login-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.login-location,
.login-ip,
.login-time {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  color: var(--text-light);
}

.login-location svg,
.login-ip svg,
.login-time svg {
  margin-right: 0.5rem;
}

/* Media queries for responsive design */
@media (max-width: 480px) {
  .security-container {
    padding-bottom: 7rem; /* Ensure enough space for bottom navigation */
    padding-top: env(safe-area-inset-top, 0px);
    width: 100%;
  }

  .section {
    padding: 0;
    width: 100%;
  }

  .section-header {
    padding: 0.5rem 1rem;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1rem;
  }

  .section-title {
    color: var(--text-color);
    margin: 0;
  }

  .login-history-list {
    padding: 0 1rem;
    width: 100%;
  }

  .card {
    background-color: var(--card-background);
    border-radius: 8px;
    width: 100%;
  }

  /* Keep the mobile design for login device */
  .login-device {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .current-device-badge {
    margin-top: 0;
  }
}

@media (min-width: 769px) {
  .security-container {
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    height: auto;
    padding-bottom: 2rem; /* Less padding needed on desktop */
    overflow-y: auto;
  }

  .section {
    width: 100%;
    max-width: 100%;
    margin: 0 0 1.5rem 0;
    padding: 0;
  }

  .section-header {
    padding: 0.5rem 1rem;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1rem;
  }

  .section-title {
    color: var(--text-color);
    margin: 0;
  }

  .login-history-list {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0 1rem;
  }

  .card {
    width: 100%;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    background-color: var(--card-background);
  }

  .login-history-item {
    background-color: var(--card-background);
    margin-bottom: 0.5rem;
  }

  /* Fix login device display on web */
  .login-device {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .current-device-badge {
    margin-top: 0;
  }
}
</style>
