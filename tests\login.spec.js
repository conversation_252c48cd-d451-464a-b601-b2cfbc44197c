import { test, expect } from '@playwright/test';

test.describe('Login Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Go to the login page before each test
    await page.goto('/login');
  });

  test('should show login form initially', async ({ page }) => {
    // Check that the login form is visible
    await expect(page.getByText('Login')).toBeVisible();
    await expect(page.locator('.input-label', { hasText: 'Phone Number' })).toBeVisible();
    await expect(page.locator('.input-label', { hasText: 'Password' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Login' })).toBeVisible();
  });

  test('should validate phone number format', async ({ page }) => {
    const submitButton = page.getByRole('button', { name: 'Lo<PERSON>' });

    // Button should be disabled initially
    await expect(submitButton).toBeDisabled();

    // Enter invalid phone number (too short)
    await page.locator('#phone').fill('123');
    await page.locator('#password').fill('123456');
    await expect(submitButton).toBeDisabled();

    // Enter valid phone number
    await page.locator('#phone').fill('1234567890');
    await page.locator('#password').fill('123456');
    await expect(submitButton).toBeEnabled();
  });

  test('should validate password format', async ({ page }) => {
    const submitButton = page.getByRole('button', { name: 'Login' });

    // Enter valid phone number but invalid password (too short)
    await page.locator('#phone').fill('1234567890');
    await page.locator('#password').fill('123');
    await expect(submitButton).toBeDisabled();

    // Enter valid password
    await page.locator('#password').fill('123456');
    await expect(submitButton).toBeEnabled();
  });

  test.skip('should show error for incorrect credentials', async ({ page }) => {
    // This test is skipped for now as it requires a working backend
    // Enter invalid credentials
    await page.locator('#phone').fill('1234567890');
    await page.locator('#password').fill('wrongpassword');
    await page.getByRole('button', { name: 'Login' }).click();

    // Should show error message
    await expect(page.getByText(/Login failed/)).toBeVisible();
  });

  test.skip('should login successfully with correct credentials', async ({ page }) => {
    // This test is skipped for now as it requires a working backend
    // Enter valid credentials
    await page.locator('#phone').fill('60102431439');
    await page.locator('#password').fill('123456');
    await page.getByRole('button', { name: 'Login' }).click();

    // Should redirect to home page
    await expect(page).toHaveURL('/');

    // Home page elements should be visible
    await expect(page.getByText('Your RVMPlus Points')).toBeVisible();
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set viewport to mobile size
    await page.setViewportSize({ width: 375, height: 667 });

    // Check that the login form is properly displayed
    await expect(page.getByText('Login')).toBeVisible();
    await expect(page.locator('.input-label', { hasText: 'Phone Number' })).toBeVisible();
    await expect(page.locator('.input-label', { hasText: 'Password' })).toBeVisible();
  });
});
