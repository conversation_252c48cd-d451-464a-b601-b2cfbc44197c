import { test, expect } from '@playwright/test';

/**
 * Basic responsive design tests for mobile and desktop views
 */
test.describe('Basic Responsive Design Tests', () => {
  // Define viewport sizes for testing
  const viewports = {
    mobile: { width: 375, height: 667 },  // iPhone SE / common mobile size
    desktop: { width: 1280, height: 800 } // Standard desktop size
  };

  // Test login page responsiveness (no authentication needed)
  test('Login page should be responsive across different viewports', async ({ page }) => {
    await page.goto('/login');
    
    // Test on mobile viewport
    await page.setViewportSize(viewports.mobile);
    
    // Check login container visibility
    const loginContainer = page.locator('.login-container');
    await expect(loginContainer).toBeVisible();
    
    // Check that phone input has proper mobile styling
    const phoneInput = page.locator('.phone-input-wrapper');
    await expect(phoneInput).toBeVisible();
    
    // Get the dimensions of the phone input on mobile
    const phoneInputMobileBounds = await phoneInput.boundingBox();
    
    // Test on desktop viewport
    await page.setViewportSize(viewports.desktop);
    
    // Get the dimensions of the phone input on desktop
    const phoneInputDesktopBounds = await phoneInput.boundingBox();
    
    // Compare dimensions - should be different between mobile and desktop
    expect(phoneInputMobileBounds.width).not.toEqual(phoneInputDesktopBounds.width);
  });

  // Test bottom navigation visibility and structure with direct URL access
  test('Bottom navigation should be visible when authenticated', async ({ page }) => {
    // Go to login page
    await page.goto('/login');
    
    // Mock authentication
    await page.evaluate(() => {
      localStorage.setItem('isAuthenticated', 'true');
      localStorage.setItem('user', JSON.stringify({
        id: '123',
        name: 'Test User',
        phone: '+601234567890',
        points: 1000
      }));
    });
    
    // Go to home page
    await page.goto('/');
    
    // Wait for navigation to complete
    await page.waitForLoadState('networkidle');
    
    // Check if bottom navigation is visible
    const bottomNav = page.locator('.bottom-nav');
    if (await bottomNav.isVisible()) {
      // If visible, check for navigation items
      const homeItem = page.locator('.nav-item', { hasText: 'Home' });
      const vouchersItem = page.locator('.nav-item', { hasText: 'Vouchers' });
      const qrCodeButton = page.locator('.qr-code-button');
      
      // Log the result
      console.log('Bottom navigation is visible');
      
      // Check if navigation items are visible
      const homeVisible = await homeItem.isVisible();
      const vouchersVisible = await vouchersItem.isVisible();
      const qrCodeVisible = await qrCodeButton.isVisible();
      
      console.log(`Home item visible: ${homeVisible}`);
      console.log(`Vouchers item visible: ${vouchersVisible}`);
      console.log(`QR Code button visible: ${qrCodeVisible}`);
    } else {
      // If not visible, take a screenshot for debugging
      await page.screenshot({ path: 'bottom-nav-not-visible.png' });
      console.log('Bottom navigation is not visible');
      
      // Check if we're actually logged in
      const isLoggedIn = await page.evaluate(() => {
        return localStorage.getItem('isAuthenticated') === 'true';
      });
      
      console.log(`Is logged in according to localStorage: ${isLoggedIn}`);
      
      // Check what page we're on
      const url = page.url();
      console.log(`Current URL: ${url}`);
    }
  });

  // Test responsive design of the app container
  test('App container should adapt to different viewports', async ({ page }) => {
    // Go to login page (doesn't require authentication)
    await page.goto('/login');
    
    // Test on mobile viewport
    await page.setViewportSize(viewports.mobile);
    
    // Get app container dimensions on mobile
    const appContainer = page.locator('#app');
    const appContainerMobileBounds = await appContainer.boundingBox();
    
    // Test on desktop viewport
    await page.setViewportSize(viewports.desktop);
    
    // Get app container dimensions on desktop
    const appContainerDesktopBounds = await appContainer.boundingBox();
    
    // Compare dimensions - should be different between mobile and desktop
    expect(appContainerMobileBounds.width).not.toEqual(appContainerDesktopBounds.width);
    
    // On mobile, app container should be full width
    expect(appContainerMobileBounds.width).toBeCloseTo(viewports.mobile.width, -1); // Allow some margin of error
  });
});
