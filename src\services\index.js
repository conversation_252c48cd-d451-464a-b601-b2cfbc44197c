/**
 * Services index file
 * Exports all API services
 */

// Export HTTP client
export { default as httpClient } from '../utils/httpClient';

// Export services
export { useAuthService } from './authService';
export { useUserService } from './userService';
export { useApiLocationService } from './apiLocationService';
export { useVoucherService } from './voucherService';
export { useTransactionService } from './transactionService';
export { useMovementService } from './movementService';
export { useForestService } from './forestService';

// Export mock data for testing
export { default as mockData } from './mockData';

// Configuration
export const API_CONFIG = {
  // Set to false to use real API endpoints
  USE_MOCK_DATA: import.meta.env.VITE_USE_MOCK_DATA === 'true',

  // Base URL for API requests
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000',

  // API timeout in milliseconds
  API_TIMEOUT: parseInt(import.meta.env.VITE_API_TIMEOUT || '30000', 10)
};
