# API Error Codes

This document defines the standard error codes used by the RewardRedemption API. When an error occurs, the API will return a response with the following structure:

```json
{
  "success": false,
  "message": "Error message",
  "error": {
    "code": "ERROR_CODE",
    "description": "Detailed error description"
  }
}
```

The frontend can use the `code` field to determine the appropriate error message to display to the user.

## General Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `INTERNAL_SERVER_ERROR` | 500 | An unexpected server error occurred |
| `VALIDATION_ERROR` | 400 | Request validation failed |
| `UNAUTHORIZED` | 401 | Authentication token is missing or invalid |
| `FORBIDDEN` | 403 | User does not have permission to access the resource |
| `NOT_FOUND` | 404 | Resource not found |
| `METHOD_NOT_ALLOWED` | 405 | HTTP method not allowed for this endpoint |
| `CONFLICT` | 409 | Request conflicts with the current state of the resource |
| `TOO_MANY_REQUESTS` | 429 | Rate limit exceeded |
| `SERVICE_UNAVAILABLE` | 503 | Service temporarily unavailable |

## Authentication Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `INVALID_PHONE` | 400 | Phone number is invalid or not registered |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many OTP requests |
| `INVALID_OTP` | 400 | OTP is incorrect |
| `OTP_EXPIRED` | 400 | OTP has expired |
| `USER_NOT_FOUND` | 404 | User not found |
| `INVALID_CREDENTIALS` | 401 | Invalid credentials |
| `ACCOUNT_LOCKED` | 403 | Account is locked due to too many failed attempts |
| `TOKEN_EXPIRED` | 401 | Authentication token has expired |
| `INVALID_TOKEN` | 401 | Authentication token is invalid |
| `REFRESH_TOKEN_EXPIRED` | 401 | Refresh token has expired |
| `INVALID_REFRESH_TOKEN` | 401 | Refresh token is invalid |

## User Profile Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `PROFILE_NOT_FOUND` | 404 | User profile not found |
| `INVALID_EMAIL` | 400 | Email address is invalid |
| `EMAIL_ALREADY_EXISTS` | 409 | Email address is already in use |
| `INVALID_NAME` | 400 | Name is invalid |
| `INVALID_GENDER` | 400 | Gender is invalid |
| `INVALID_ADDRESS` | 400 | Address is invalid |
| `INVALID_ZIP_CODE` | 400 | ZIP code is invalid |
| `INVALID_STATE` | 400 | State is invalid |
| `INVALID_CITY` | 400 | City is invalid |
| `PROFILE_UPDATE_FAILED` | 500 | Failed to update user profile |

## Location Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `LOCATION_NOT_FOUND` | 404 | Location not found |
| `INVALID_COORDINATES` | 400 | Latitude or longitude is invalid |
| `LOCATION_SERVICE_UNAVAILABLE` | 503 | Location service is temporarily unavailable |
| `GEOCODING_FAILED` | 500 | Failed to geocode coordinates |
| `INVALID_RADIUS` | 400 | Radius is invalid |
| `LOCATION_LIMIT_EXCEEDED` | 400 | Too many locations requested |

## Voucher Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `VOUCHER_NOT_FOUND` | 404 | Voucher not found |
| `VOUCHER_EXPIRED` | 400 | Voucher has expired |
| `VOUCHER_ALREADY_REDEEMED` | 400 | Voucher has already been redeemed |
| `INSUFFICIENT_POINTS` | 400 | Insufficient points to redeem voucher |
| `VOUCHER_UNAVAILABLE` | 400 | Voucher is no longer available |
| `VOUCHER_LIMIT_REACHED` | 400 | User has reached the voucher limit |
| `INVALID_VOUCHER_STATUS` | 400 | Invalid voucher status |
| `REDEMPTION_FAILED` | 500 | Failed to redeem voucher |

## Transaction Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `TRANSACTION_NOT_FOUND` | 404 | Transaction not found |
| `INVALID_TRANSACTION_TYPE` | 400 | Transaction type is invalid |
| `INVALID_TRANSACTION_AMOUNT` | 400 | Transaction amount is invalid |
| `INVALID_TRANSACTION_DATE` | 400 | Transaction date is invalid |
| `TRANSACTION_FAILED` | 500 | Transaction failed to process |
| `INVALID_CATEGORY` | 400 | Invalid transaction category |
| `INVALID_PAGE` | 400 | Invalid page number |
| `INVALID_LIMIT` | 400 | Invalid limit value |

## Movement Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `CHALLENGE_NOT_FOUND` | 404 | Challenge not found |
| `CHALLENGE_ALREADY_ACTIVE` | 400 | User already has an active challenge |
| `CHALLENGE_ALREADY_COMPLETED` | 400 | Challenge has already been completed |
| `CHALLENGE_EXPIRED` | 400 | Challenge has expired |
| `INVALID_CHALLENGE_PROGRESS` | 400 | Invalid challenge progress |
| `INVALID_STEPS` | 400 | Invalid step count |
| `INVALID_DISTANCE` | 400 | Invalid distance value |
| `MOVEMENT_SERVICE_UNAVAILABLE` | 503 | Movement tracking service is temporarily unavailable |
| `NO_ACTIVE_CHALLENGE` | 400 | No active challenge found |
| `CHALLENGE_START_FAILED` | 500 | Failed to start challenge |
| `CHALLENGE_UPDATE_FAILED` | 500 | Failed to update challenge progress |

## Forest Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `FOREST_STATS_NOT_FOUND` | 404 | Forest statistics not found |
| `INVALID_CO2_AMOUNT` | 400 | Invalid CO2 amount |
| `INVALID_RECYCLED_ITEMS` | 400 | Invalid recycled items count |
| `FOREST_SERVICE_UNAVAILABLE` | 503 | Forest service is temporarily unavailable |
| `FOREST_UPDATE_FAILED` | 500 | Failed to update forest statistics |

## Implementation Guidelines

1. **Consistent Error Handling**: Ensure all API endpoints use these error codes consistently.

2. **HTTP Status Codes**: Return the appropriate HTTP status code along with the error response.

3. **Error Messages**: Provide clear and concise error messages that explain what went wrong.

4. **Error Descriptions**: Include detailed descriptions that help the client understand how to fix the error.

5. **Logging**: Log all errors on the server side for debugging and monitoring.

6. **Frontend Integration**: The frontend should handle these error codes and display appropriate messages to the user.

## Example Error Response

```json
{
  "success": false,
  "message": "Failed to redeem voucher",
  "error": {
    "code": "VOUCHER_ALREADY_REDEEMED",
    "description": "This voucher has already been redeemed and cannot be used again"
  }
}
```

## Frontend Error Handling

The frontend should map these error codes to user-friendly messages. For example:

```javascript
const errorMessages = {
  // General errors
  'INTERNAL_SERVER_ERROR': 'Something went wrong. Please try again later.',
  'VALIDATION_ERROR': 'Please check your input and try again.',
  'UNAUTHORIZED': 'Please log in to continue.',
  'FORBIDDEN': 'You do not have permission to access this resource.',
  'NOT_FOUND': 'The requested resource was not found.',
  
  // Authentication errors
  'INVALID_PHONE': 'Please enter a valid phone number.',
  'RATE_LIMIT_EXCEEDED': 'Too many attempts. Please try again later.',
  'INVALID_OTP': 'The verification code you entered is incorrect.',
  'OTP_EXPIRED': 'The verification code has expired. Please request a new one.',
  
  // Voucher errors
  'VOUCHER_EXPIRED': 'This voucher has expired.',
  'VOUCHER_ALREADY_REDEEMED': 'This voucher has already been redeemed.',
  'INSUFFICIENT_POINTS': 'You do not have enough points to redeem this voucher.',
  
  // Default error message
  'DEFAULT': 'An error occurred. Please try again.'
};

// Function to get error message
const getErrorMessage = (errorCode) => {
  return errorMessages[errorCode] || errorMessages['DEFAULT'];
};
```

This approach allows for centralized error handling and consistent error messages across the application.
