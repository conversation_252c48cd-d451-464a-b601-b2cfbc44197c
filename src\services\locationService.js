// Location service to handle geolocation functionality
import { ref } from 'vue';

// State variables
const currentPosition = ref(null);
const watchId = ref(null);
const isTracking = ref(false);
const locationError = ref(null);
const isLoading = ref(false);
const placeName = ref('');
const isLoadingPlaceName = ref(false);

// Callback for position updates
let positionUpdateCallback = null;

// Get the user's current position
const getCurrentPosition = () => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      const error = 'Geolocation is not supported by your browser';
      locationError.value = error;
      reject(error);
      return;
    }

    isLoading.value = true;
    locationError.value = null;

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude, accuracy } = position.coords;
        currentPosition.value = {
          latitude,
          longitude,
          accuracy,
          timestamp: position.timestamp,
        };
        isLoading.value = false;
        resolve(currentPosition.value);
      },
      (error) => {
        handleGeolocationError(error);
        isLoading.value = false;
        reject(error);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0,
      },
    );
  });
};

// Start tracking the user's position
const startTracking = (callback = null) => {
  if (!navigator.geolocation) {
    locationError.value = 'Geolocation is not supported by your browser';
    return false;
  }

  // Clear any existing watch
  stopTracking();

  locationError.value = null;

  // Store the callback if provided
  if (callback) {
    positionUpdateCallback = callback;
  }

  watchId.value = navigator.geolocation.watchPosition(
    (position) => {
      const { latitude, longitude, accuracy } = position.coords;
      currentPosition.value = {
        latitude,
        longitude,
        accuracy,
        timestamp: position.timestamp,
      };
      isTracking.value = true;

      // Call the callback if it exists
      if (positionUpdateCallback) {
        positionUpdateCallback(currentPosition.value);
      }
    },
    (error) => {
      handleGeolocationError(error);
      isTracking.value = false;
    },
    {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 0,
    },
  );

  return true;
};

// Stop tracking the user's position
const stopTracking = () => {
  if (watchId.value !== null) {
    navigator.geolocation.clearWatch(watchId.value);
    watchId.value = null;
    isTracking.value = false;

    // Clear the callback
    positionUpdateCallback = null;

    return true;
  }
  return false;
};

// Handle geolocation errors
const handleGeolocationError = (error) => {
  switch (error.code) {
    case error.PERMISSION_DENIED:
      locationError.value = 'Location access was denied by the user.';
      break;
    case error.POSITION_UNAVAILABLE:
      locationError.value = 'Location information is unavailable.';
      break;
    case error.TIMEOUT:
      locationError.value = 'The request to get location timed out.';
      break;
    default:
      locationError.value = 'An unknown error occurred while getting location.';
      break;
  }
  console.error('Geolocation error:', locationError.value);
};

// Calculate distance between two coordinates in kilometers (using Haversine formula)
const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c; // Distance in km
  return distance;
};

// Convert degrees to radians
const deg2rad = (deg) => {
  return deg * (Math.PI / 180);
};

// Get place name from coordinates using reverse geocoding
const getPlaceNameFromCoordinates = async (latitude, longitude) => {
  if (!latitude || !longitude) {
    return '';
  }

  isLoadingPlaceName.value = true;

  try {
    // Use Nominatim OpenStreetMap API for reverse geocoding
    const response = await fetch(
      `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=18&addressdetails=1`,
      {
        headers: {
          'Accept-Language': 'en-US,en;q=0.9',
          'User-Agent': 'RVMPlus-App/1.0',
        },
      },
    );

    if (!response.ok) {
      throw new Error('Failed to fetch location name');
    }

    const data = await response.json();

    // Extract the most relevant place name
    // Try to get a meaningful name from the address components
    if (data.address) {
      const address = data.address;

      // Try to get the most specific location name
      const locationName =
        address.suburb ||
        address.neighbourhood ||
        address.town ||
        address.city ||
        address.county ||
        address.state ||
        data.display_name.split(',')[0];

      placeName.value = locationName;
      return locationName;
    }

    // Fallback to the first part of the display name
    if (data.display_name) {
      const simpleName = data.display_name.split(',')[0];
      placeName.value = simpleName;
      return simpleName;
    }

    return '';
  } catch (error) {
    console.error('Error getting place name:', error);
    return '';
  } finally {
    isLoadingPlaceName.value = false;
  }
};

// Reset any errors
const resetError = () => {
  locationError.value = null;
};

// Export the location service
export const useLocationService = () => {
  return {
    currentPosition,
    isTracking,
    locationError,
    isLoading,
    placeName,
    isLoadingPlaceName,
    getCurrentPosition,
    startTracking,
    stopTracking,
    calculateDistance,
    getPlaceNameFromCoordinates,
    resetError,
  };
};
