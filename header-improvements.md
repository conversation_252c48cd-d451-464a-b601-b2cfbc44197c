# Header Component Improvements

## Changes Made

1. **Centered Page Titles**
   - Redesigned the header layout to properly center page titles
   - Added page titles to all routes in the router configuration
   - Created a three-section layout (left, center, right) for consistent alignment

2. **Removed <PERSON> Button from Home Page**
   - Excluded back button only from the Home page
   - Updated the `showBackButton` computed property to handle this exception
   - All other pages now show the back button

3. **Fixed Back Button Alignment**
   - Improved alignment between back arrow and text
   - Added proper vertical alignment with flexbox and line-height properties
   - Fixed spacing between icon and text

4. **Enhanced Header Layout**
   - Created fixed-width sections for left and right areas to ensure title stays centered
   - Added proper z-index and pointer-events handling
   - Improved responsive behavior for different screen sizes
   - Removed duplicate headers from all pages to use the main Header.vue component

## Technical Details

### Header Component Structure

```html
<header class="header">
  <div class="header-content">
    <!-- Left section with back button -->
    <div class="left-section">
      <button v-if="showBackButton" class="back-button" @click="goBack">
        <span class="back-arrow">‹</span>
        <span class="back-text">{{ t('common.back') }}</span>
      </button>
    </div>

    <!-- Center section with title -->
    <div class="title-section">
      <h1 class="page-title">{{ route.meta?.title || route.name }}</h1>
    </div>

    <!-- Right section with language selector -->
    <div class="right-section">
      <LanguageSelector />
    </div>
  </div>
</header>
```

### CSS Improvements

```css
/* Three-section layout */
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

/* Fixed-width side sections */
.left-section, .right-section {
  width: 80px;
  display: flex;
  align-items: center;
}

/* Absolutely positioned title for true centering */
.title-section {
  position: absolute;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;
}

/* Back button alignment fixes */
.back-button {
  display: flex;
  align-items: center;
  height: 24px;
  line-height: 1;
}

.back-arrow {
  font-size: 1.5rem;
  margin-right: 4px;
  line-height: 1;
  display: flex;
  align-items: center;
}

.back-text {
  font-size: 0.9rem;
  line-height: 1;
}
```

### Router Configuration

Added page titles to all routes:

```javascript
{
  path: '/vouchers',
  name: 'Vouchers',
  component: Vouchers,
  meta: { requiresAuth: true, title: 'Vouchers' },
},
```

## Results

- Page titles are now properly centered in the header
- Back button is hidden only on the Home page
- Back button icon and text are properly aligned
- Header layout is consistent across all pages
- Responsive design works on both mobile and desktop views
