import { ref } from 'vue';
import { useAuth } from '@/store/auth';
import { requestHandlerAuth } from '../services/bitmapService';

export function useBitmapRequest() {
  const { token, accountNumber } = useAuth();
  const data = ref(null);
  const isLoading = ref(false);
  const error = ref(null);

  const fetchData = async (bitmap, func = '0', ref02 = '', startDate = '', endDate = '') => {
    try {
      isLoading.value = true;
      error.value = null;

      const params = {
        Token: token.value,
        bitmap,
        Func: func,
        StartDate: startDate,
        EndDate: endDate,
        Ref01: accountNumber.value,
        Ref02: ref02,
        Lang: 'en',
      };

      const res = await requestHandlerAuth(params);
      data.value = res?.data?.[0] || null;
      return data.value;
    } catch (err) {
      error.value = err;
      console.error('Error in bitmap request:', err);
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  return {
    data,
    isLoading,
    error,
    fetchData,
  };
} 