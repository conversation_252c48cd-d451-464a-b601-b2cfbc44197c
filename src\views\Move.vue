<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useTheme } from '../store/theme';
import { useLocation } from '../store/location';
import { useBitmapRequest } from '../composables/useBitmapRequest';

// Import running image
const RunningImage = new URL('../assets/running.png', import.meta.url).href;

// Get theme state
const { isDarkMode } = useTheme();

// Get location service
const {
  currentPosition,
  isTracking,
  startLocationTracking,
  stopTracking,
} = useLocation();

const router = useRouter();

// Bitmap request for movement data
const { fetchData: fetchMovementData, isLoading: movementLoading, error: movementError } = useBitmapRequest();

// API data state
const movementApiData = ref(null);
const lastFetchTime = ref(null);

// Challenge state
const hasActiveChallenge = ref(false);
const challengeProgress = ref(0);
const challengeGoal = ref(10000);
const challengeTimeLeft = ref('');
const challengeStartTime = ref(null);
const challengeDuration = ref(24 * 60 * 60 * 1000); // 24 hours in milliseconds
const activeChallenge = ref(null);
const challengeUnit = ref('steps');

// Movement stats
const stepsWalked = ref(0);
const pointsEarned = ref(0);
const challengesCompleted = ref(0);

// Available challenges
const availableChallenges = ref([
  {
    id: 1,
    title: 'Daily 10,000 steps',
    description: 'Keep active with our daily strides goal.',
    reward: 150,
    goal: 10000,
    unit: 'steps',
  },
  {
    id: 2,
    title: '15 minutes walk',
    description: 'Take a break and get 1,500 steps in!',
    reward: 50,
    goal: 1500,
    unit: 'steps',
  },
  {
    id: 3,
    title: '30 minutes activity',
    description: 'Boost your energy with a 30-minute activity.',
    reward: 100,
    goal: 30,
    unit: 'minutes',
  },
  {
    id: 4,
    title: 'Weekend 5K',
    description: 'Complete a 5K distance over the weekend.',
    reward: 200,
    goal: 5000,
    unit: 'meters',
  },
]);

// Computed properties
const progressPercentage = computed(() => {
  return (challengeProgress.value / challengeGoal.value) * 100;
});

const progressCircleStyle = computed(() => {
  // Calculate the stroke-dasharray and stroke-dashoffset for the progress circle
  const circumference = 2 * Math.PI * 140; // 140 is the radius of the circle
  const dashoffset = circumference * (1 - progressPercentage.value / 100);

  return {
    strokeDasharray: `${circumference} ${circumference}`,
    strokeDashoffset: dashoffset,
  };
});

// Movement tracking
const isMovementTracking = ref(false);
const lastPosition = ref(null);
const totalDistance = ref(0);
const stepCount = ref(0);

// Constants for step calculation
const STEP_LENGTH_METERS = 0.762; // Average step length in meters
const DISTANCE_TO_STEPS_RATIO = 1 / STEP_LENGTH_METERS; // Steps per meter

// Fetch movement data from API
const fetchMovementStats = async () => {
  try {
    console.log('Fetching movement statistics from API...');

    // Use bitmap request to fetch movement data - using bitmap 1024 with function "1"
    const response = await fetchMovementData(1024, '1');

    if (response && response.data && response.data.length > 0) {
      movementApiData.value = response;
      lastFetchTime.value = new Date();

      // Check if we have Table0 with campaign data
      const responseData = response.data[0];
      if (responseData && responseData.Table0 && responseData.Table0.length > 0) {
        const campaignData = responseData.Table0[0];

        console.log('Campaign data received:', campaignData);

        // Map campaign data to movement stats
        if (campaignData.WalkStepCount !== undefined) {
          stepsWalked.value = campaignData.WalkStepCount;

          // Calculate points (1 point per 100 steps)
          pointsEarned.value = Math.floor(campaignData.WalkStepCount / 100);

          // Update step count for current tracking
          stepCount.value = campaignData.WalkStepCount;
        }

        // If there's an active challenge, update progress
        if (hasActiveChallenge.value && campaignData.WalkStepCount !== undefined) {
          challengeProgress.value = Math.min(campaignData.WalkStepCount, challengeGoal.value);

          // Check if challenge is completed
          if (challengeProgress.value >= challengeGoal.value) {
            completeChallenge();
          }
        }

        console.log('Movement data updated from API:', {
          campaignId: campaignData.CampaignId,
          walkStepCount: campaignData.WalkStepCount,
          description: campaignData.Descn,
          stepsWalked: stepsWalked.value,
          pointsEarned: pointsEarned.value,
          challengeProgress: challengeProgress.value,
        });
      } else {
        console.warn('No Table0 data found in API response');
      }
    } else {
      console.warn('No movement data found in API response');
    }
  } catch (error) {
    console.error('Error fetching movement statistics:', error);
    // Continue with local tracking if API fails
  }
};

// Start tracking movement
const startMovementTracking = async () => {
  if (isMovementTracking.value) {return;}

  isMovementTracking.value = true;
  lastPosition.value = currentPosition.value;

  // Start location tracking if not already tracking
  if (!isTracking.value) {
    await startLocationTracking();
  }
};

// Stop tracking movement
const stopMovementTracking = () => {
  isMovementTracking.value = false;

  // Stop location tracking if we started it
  if (isTracking.value) {
    stopTracking();
  }
};

// Calculate steps from distance
const calculateSteps = (distanceInMeters) => {
  return Math.floor(distanceInMeters * DISTANCE_TO_STEPS_RATIO);
};

// Update movement stats based on position changes
const updateMovementStats = () => {
  if (!isMovementTracking.value || !currentPosition.value || !lastPosition.value) {return;}

  // Calculate distance between current and last position
  const distance = calculateDistance(
    lastPosition.value.latitude,
    lastPosition.value.longitude,
    currentPosition.value.latitude,
    currentPosition.value.longitude,
  );

  // Convert distance to meters
  const distanceInMeters = distance * 1000;

  // Update total distance
  totalDistance.value += distanceInMeters;

  // Calculate steps
  const newSteps = calculateSteps(distanceInMeters);
  stepCount.value += newSteps;
  stepsWalked.value += newSteps;

  // Update challenge progress if there's an active challenge
  if (hasActiveChallenge.value) {
    challengeProgress.value += newSteps;

    // Check if challenge is completed
    if (challengeProgress.value >= challengeGoal.value) {
      completeChallenge();
    }
  }

  // Update points (1 point per 100 steps)
  pointsEarned.value = Math.floor(stepsWalked.value / 100);

  // Update last position
  lastPosition.value = currentPosition.value;
};

// Calculate distance between two coordinates (Haversine formula)
const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c; // Distance in km
  return distance;
};

const deg2rad = (deg) => {
  return deg * (Math.PI/180);
};

// Complete a challenge
const completeChallenge = () => {
  challengesCompleted.value++;
  hasActiveChallenge.value = false;
  activeChallenge.value = null;

  // Stop the timer
  stopTimer();

  // In a real app, you would send this to the server
  console.log('Challenge completed!');
};

// Calculate remaining time for challenge
const calculateRemainingTime = () => {
  if (!challengeStartTime.value) {return '00:00:00';}

  const now = new Date();
  const endTime = new Date(challengeStartTime.value.getTime() + challengeDuration.value);
  const remainingMs = endTime - now;

  if (remainingMs <= 0) {
    // Challenge time is up
    completeChallenge();
    return '00:00:00';
  }

  // Convert to hours, minutes, seconds
  const hours = Math.floor(remainingMs / (1000 * 60 * 60));
  const minutes = Math.floor((remainingMs % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((remainingMs % (1000 * 60)) / 1000);

  // Format as HH:MM:SS
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

// Update time left
const updateTimeLeft = () => {
  if (hasActiveChallenge.value && challengeStartTime.value) {
    challengeTimeLeft.value = calculateRemainingTime();
  }
};

// Start a challenge
const startChallenge = (challenge) => {
  challengeGoal.value = challenge.goal;
  challengeProgress.value = 0;
  challengeStartTime.value = new Date();
  hasActiveChallenge.value = true;
  activeChallenge.value = challenge;
  challengeUnit.value = challenge.unit;

  // Set challenge duration based on challenge type
  if (challenge.unit === 'minutes') {
    // Convert minutes to milliseconds
    challengeDuration.value = challenge.goal * 60 * 1000;
  } else {
    // Default to 24 hours for step and distance challenges
    challengeDuration.value = 24 * 60 * 60 * 1000;
  }

  // Update time immediately
  updateTimeLeft();

  // Start the timer
  startTimer();

  // Start movement tracking if not already tracking
  if (!isMovementTracking.value) {
    startMovementTracking();
  }
};

// Go back to previous page (unused but kept for future reference)
const _goBack = () => {
  router.back();
};

// Watch for position changes to update movement stats
watch(currentPosition, () => {
  updateMovementStats();
});

// Timer for updating challenge time
let timerInterval = null;

// Start timer for updating challenge time
const startTimer = () => {
  // Clear any existing timer
  if (timerInterval) {
    clearInterval(timerInterval);
  }

  // Update time every second
  timerInterval = setInterval(() => {
    updateTimeLeft();
  }, 1000);
};

// Stop timer
const stopTimer = () => {
  if (timerInterval) {
    clearInterval(timerInterval);
    timerInterval = null;
  }
};

// Component lifecycle
onMounted(async () => {
  // Fetch movement data from API first
  await fetchMovementStats();

  // Start movement tracking when component is mounted
  startMovementTracking();

  // Start timer if there's an active challenge
  if (hasActiveChallenge.value) {
    startTimer();
  }
});

onUnmounted(() => {
  // Stop movement tracking when component is unmounted
  stopMovementTracking();

  // Stop timer
  stopTimer();
});
</script>

<template>
  <div class="move-container" :class="{ 'dark-theme': isDarkMode }">
    <!-- Challenge Progress (when challenge is active) -->
    <div v-if="hasActiveChallenge" class="challenge-active">
      <!-- Progress Circle -->
      <div class="progress-circle-container">
        <svg class="progress-circle" width="300" height="300" viewBox="0 0 300 300">
          <!-- Background Circle -->
          <circle
            cx="150"
            cy="150"
            r="140"
            fill="none"
            stroke="#e0e0e0"
            stroke-width="18"
          />
          <!-- Progress Circle -->
          <circle
            cx="150"
            cy="150"
            r="140"
            fill="none"
            stroke="#6366f1"
            stroke-width="18"
            stroke-linecap="round"
            transform="rotate(-90 150 150)"
            :style="progressCircleStyle"
          />
        </svg>
        <div class="progress-text">
          {{ challengeProgress }}/{{ challengeGoal }}
        </div>
        <div class="progress-unit">{{ challengeUnit }}</div>
      </div>

      <!-- Challenge Info -->
      <div class="challenge-info">
        <div class="challenge-card">
          <div class="challenge-left">
            <div class="challenge-name">Current challenge</div>
            <div class="challenge-time">{{ challengeTimeLeft }}</div>
          </div>
          <div class="challenge-right">
            <div class="challenge-goal">{{ activeChallenge?.title || 'Daily Challenge' }}</div>
            <div class="challenge-current">{{ challengeProgress }}/{{ challengeGoal }} {{ challengeUnit }}</div>
          </div>
        </div>
      </div>

      <!-- Available Challenges -->
      <div class="available-challenges">
        <h2 class="section-title">Available challenges</h2>

        <div
          v-for="challenge in availableChallenges"
          :key="challenge.id"
          class="challenge-item"
        >
          <div class="challenge-item-content">
            <h3 class="challenge-item-title">{{ challenge.title }}</h3>
            <p class="challenge-item-description">{{ challenge.description }}</p>
            <div class="challenge-item-reward">Reward: {{ challenge.reward }} points</div>
          </div>
          <div class="challenge-status">
            <span class="status-text">Challenge in progress</span>
            <button class="more-options-button">•••</button>
          </div>
        </div>
      </div>
    </div>

    <!-- No Active Challenge -->
    <div v-else class="no-challenge">
      <!-- Illustration -->
      <div class="illustration-container">
        <img
          :src="RunningImage"
          alt="Movement illustration"
          class="move-illustration"
        />
      </div>

      <!-- Stats -->
      <div class="stats-container">
        <div class="stat-item">
          <div class="stat-label">Steps walked</div>
          <div class="stat-value">{{ stepsWalked }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">Points earned</div>
          <div class="stat-value">{{ pointsEarned }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">Challenges completed</div>
          <div class="stat-value">{{ challengesCompleted }}</div>
        </div>
      </div>

      <!-- API Status and Controls -->
      <div class="api-status">
        <!-- Loading state -->
        <div v-if="movementLoading" class="loading-state">
          <p>🏃‍♂️ Loading movement data...</p>
        </div>

        <!-- Error state -->
        <div v-if="movementError && !movementLoading" class="error-state">
          <p>⚠️ Error loading movement data. Using local tracking.</p>
        </div>

        <!-- Last update and refresh -->
        <div v-if="lastFetchTime && !movementLoading" class="update-controls">
          <p class="update-time">Last updated: {{ lastFetchTime.toLocaleTimeString() }}</p>
          <button class="refresh-btn" :disabled="movementLoading" title="Refresh Movement Data" @click="fetchMovementStats">
            <span v-if="!movementLoading">🔄</span>
            <span v-else>⏳</span>
          </button>
        </div>

        <!-- Campaign Message -->
        <div v-if="movementApiData && movementApiData.data && movementApiData.data[0]?.Table0 && movementApiData.data[0].Table0[0]?.Descn" class="campaign-message">
          <p class="message-text">{{ movementApiData.data[0].Table0[0].Descn }}</p>
        </div>
      </div>

      <!-- No Challenge Status -->
      <div class="no-challenge-status">
        <div class="status-card">
          <div class="status-left">
            <div class="status-text">No challenge activated</div>
          </div>
          <div class="status-right">
            <div class="status-dash">-</div>
          </div>
        </div>
      </div>

      <!-- Available Challenges -->
      <div class="available-challenges">
        <h2 class="section-title">Available challenges</h2>

        <div
          v-for="challenge in availableChallenges"
          :key="challenge.id"
          class="challenge-item"
        >
          <div class="challenge-item-content">
            <h3 class="challenge-item-title">{{ challenge.title }}</h3>
            <p class="challenge-item-description">{{ challenge.description }}</p>
            <div class="challenge-item-reward">Reward: {{ challenge.reward }} points</div>
          </div>
          <div class="challenge-action">
            <button class="start-challenge-button" @click="startChallenge(challenge)">
              <span class="start-icon">▶</span>
              <span>Start this challenge</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.move-container {
  padding: 1rem;
  padding-top: env(safe-area-inset-top, 1rem);
  padding-bottom: calc(5rem + env(safe-area-inset-bottom, 0px));
  min-height: 100vh;
  background-color: var(--background-color);
  color: var(--text-color);
  overflow-y: auto;
}

/* Header removed - using the main Header.vue component */

/* Challenge Active Styles */
.challenge-active {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-circle-container {
  position: relative;
  width: 300px;
  height: 300px;
  margin-bottom: 2rem;
}

.progress-text {
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-color);
}

.progress-unit {
  position: absolute;
  top: 60%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-light);
  text-transform: uppercase;
}

.challenge-info {
  width: 100%;
  margin-bottom: 2rem;
}

.challenge-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #6366f1;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  color: white;
}

.challenge-left {
  flex: 1;
}

.challenge-right {
  text-align: right;
  flex: 1;
}

.challenge-name {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: rgba(255, 255, 255, 0.9);
}

.challenge-time {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.challenge-goal {
  font-weight: 600;
  color: white;
}

.challenge-current {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.25rem;
}

/* No Challenge Styles */
.no-challenge {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.illustration-container {
  width: 100%;
  max-width: 350px;
  margin: 1.5rem 0 2.5rem;
  text-align: center;
}

.move-illustration {
  width: 100%;
  height: auto;
  min-height: 250px;
}

.stats-container {
  display: flex;
  width: 100%;
  margin-bottom: 2rem;
  justify-content: space-between;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
}

.no-challenge-status {
  width: 100%;
  margin-bottom: 2rem;
}

.status-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #9ca3af;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  color: white;
}

.status-left {
  flex: 1;
}

.status-right {
  text-align: right;
}

.status-text {
  font-weight: 600;
  color: white;
}

.status-dash {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
}

/* Available Challenges */
.available-challenges {
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.section-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-color);
}

.challenge-item {
  background-color: var(--card-background);
  border-radius: 12px;
  padding: 1.25rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.challenge-item-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: var(--text-color);
}

.challenge-item-description {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 0.5rem;
}

.challenge-item-reward {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 1rem;
}

.challenge-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-text {
  font-size: 0.9rem;
  font-weight: 500;
}

.more-options-button {
  background: none;
  border: none;
  color: var(--text-light);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
}

.challenge-action {
  display: flex;
  justify-content: flex-end;
}

.start-challenge-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #6366f1;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  padding: 0.5rem;
}

.start-icon {
  margin-right: 0.5rem;
}

/* Dark theme specific adjustments */
:global(.dark-theme) .challenge-card {
  background-color: #818cf8;
}

:global(.dark-theme) .status-card {
  background-color: #6b7280;
}

:global(.dark-theme) .start-challenge-button {
  color: #818cf8;
}

/* Responsive styles */
@media (max-width: 480px) {
  .move-container {
    padding: 0.75rem;
  }

  .progress-circle-container {
    width: 280px;
    height: 280px;
    margin-bottom: 1.5rem;
  }

  .progress-circle {
    width: 280px;
    height: 280px;
  }

  .progress-text {
    font-size: 1.5rem;
  }

  .progress-unit {
    font-size: 0.9rem;
  }

  .illustration-container {
    max-width: 320px;
    margin: 1rem 0 2rem;
  }

  .move-illustration {
    min-height: 220px;
  }

  .stats-container {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .stat-item {
    flex: 1 0 33%;
    margin-bottom: 1rem;
  }

  .challenge-item {
    padding: 1rem;
  }

  .available-challenges {
    max-height: 250px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* API Status Styles */
.api-status {
  width: 100%;
  margin-bottom: 1.5rem;
}

.loading-state {
  text-align: center;
  padding: 0.75rem;
  background-color: rgba(99, 102, 241, 0.1);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.loading-state p {
  margin: 0;
  color: #6366f1;
  font-weight: 500;
}

.error-state {
  text-align: center;
  padding: 0.75rem;
  background-color: rgba(239, 68, 68, 0.1);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.error-state p {
  margin: 0;
  color: #ef4444;
  font-weight: 500;
}

.update-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: rgba(156, 163, 175, 0.1);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.update-time {
  margin: 0;
  font-size: 0.8rem;
  color: var(--text-light);
  font-style: italic;
}

.refresh-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(99, 102, 241, 0.2);
  transition: all 0.2s;
}

.refresh-btn:hover {
  background-color: rgba(99, 102, 241, 0.3);
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.campaign-message {
  text-align: center;
  padding: 1rem;
  background-color: rgba(99, 102, 241, 0.1);
  border-radius: 12px;
  border-left: 4px solid #6366f1;
  margin-bottom: 1rem;
}

.message-text {
  margin: 0;
  font-size: 1rem;
  color: #6366f1;
  font-weight: 500;
  line-height: 1.4;
}

/* Dark theme adjustments */
:global(.dark-theme) .loading-state {
  background-color: rgba(129, 140, 248, 0.2);
}

:global(.dark-theme) .loading-state p {
  color: #818cf8;
}

:global(.dark-theme) .error-state {
  background-color: rgba(248, 113, 113, 0.2);
}

:global(.dark-theme) .error-state p {
  color: #f87171;
}

:global(.dark-theme) .update-controls {
  background-color: rgba(107, 114, 128, 0.2);
}

:global(.dark-theme) .refresh-btn {
  background-color: rgba(129, 140, 248, 0.3);
  color: #fff;
}

:global(.dark-theme) .refresh-btn:hover {
  background-color: rgba(129, 140, 248, 0.5);
}

:global(.dark-theme) .campaign-message {
  background-color: rgba(129, 140, 248, 0.15);
  border-left-color: #818cf8;
}

:global(.dark-theme) .message-text {
  color: #818cf8;
}
</style>
