/**
 * Forest service for managing forest statistics
 */
import { ref } from 'vue';
import httpClient from '../utils/httpClient';
import { mockForestStats, simulateApiDelay, createApiResponse, createApiErrorResponse } from './mockData';
import { useAuthService } from './authService';

// Configuration
const USE_MOCK_DATA = import.meta.env.VITE_USE_MOCK_DATA === 'true'; // Set to false to use real API

// Forest state
const forestStats = ref(null);
const isLoading = ref(false);
const error = ref(null);

// Get auth service
const { isLoggedIn } = useAuthService();

/**
 * Get forest statistics
 * @returns {Promise<Object>} Forest statistics
 */
const getForestStats = async () => {
  isLoading.value = true;
  error.value = null;

  try {
    if (!isLoggedIn.value) {
      throw new Error('User not authenticated');
    }

    if (USE_MOCK_DATA) {
      // Mock implementation
      await simulateApiDelay();

      forestStats.value = { ...mockForestStats };
      return createApiResponse(forestStats.value);
    } else {
      // Real API implementation
      const response = await httpClient.get('api/forest/stats');

      if (response.success) {
        forestStats.value = response.data;
      }

      return response;
    }
  } catch (err) {
    error.value = err.message;
    return createApiErrorResponse('FOREST_SERVICE_UNAVAILABLE', err.message);
  } finally {
    isLoading.value = false;
  }
};

/**
 * Update forest statistics
 * @param {Object} updateData - Update data
 * @param {number} updateData.co2Amount - CO2 amount
 * @param {number} updateData.recycledItems - Number of recycled items
 * @returns {Promise<Object>} Updated forest statistics
 */
const updateForestStats = async (updateData) => {
  isLoading.value = true;
  error.value = null;

  try {
    if (!isLoggedIn.value) {
      throw new Error('User not authenticated');
    }

    if (!updateData) {
      throw new Error('Update data is required');
    }

    if (USE_MOCK_DATA) {
      // Mock implementation
      await simulateApiDelay();

      // Validate update data
      if (updateData.co2Amount < 0) {
        return createApiErrorResponse('INVALID_CO2_AMOUNT', 'CO2 amount cannot be negative');
      }

      if (updateData.recycledItems < 0) {
        return createApiErrorResponse('INVALID_RECYCLED_ITEMS', 'Recycled items cannot be negative');
      }

      // Update forest stats
      const newCO2Absorbed = forestStats.value.co2Absorbed + updateData.co2Amount;
      const newTotalCO2Mitigated = forestStats.value.totalCO2Mitigated + updateData.co2Amount;
      const newTotalItemsRecycled = forestStats.value.totalItemsRecycled + updateData.recycledItems;

      // Check if level up
      const levelUp = newCO2Absorbed >= forestStats.value.co2Target;
      let newCurrentStage = forestStats.value.currentStage;
      let newCO2Target = forestStats.value.co2Target;
      let newTotalTreesGrown = forestStats.value.totalTreesGrown;

      if (levelUp) {
        newCurrentStage += 1;
        newCO2Target *= 2; // Double the target for next level
        newTotalTreesGrown += 1;
      }

      // Calculate new progress percentage
      const newProgressPercentage = (newCO2Absorbed / newCO2Target) * 100;

      // Update forest stats
      forestStats.value = {
        ...forestStats.value,
        currentStage: newCurrentStage,
        co2Absorbed: newCO2Absorbed,
        co2Target: newCO2Target,
        totalCO2Mitigated: newTotalCO2Mitigated,
        totalItemsRecycled: newTotalItemsRecycled,
        totalTreesGrown: newTotalTreesGrown,
        progressPercentage: newProgressPercentage,
        currentTreeStage: {
          id: newCurrentStage,
          name: getTreeStageName(newCurrentStage),
          co2Required: newCO2Target
        }
      };

      return createApiResponse({
        currentStage: newCurrentStage,
        co2Absorbed: newCO2Absorbed,
        co2Target: newCO2Target,
        totalCO2Mitigated: newTotalCO2Mitigated,
        totalItemsRecycled: newTotalItemsRecycled,
        progressPercentage: newProgressPercentage,
        levelUp
      }, 'Forest statistics updated successfully');
    } else {
      // Real API implementation
      const response = await httpClient.post('api/forest/update', updateData);

      if (response.success) {
        forestStats.value = {
          ...forestStats.value,
          ...response.data
        };
      }

      return response;
    }
  } catch (err) {
    error.value = err.message;
    return createApiErrorResponse('FOREST_UPDATE_FAILED', err.message);
  } finally {
    isLoading.value = false;
  }
};

/**
 * Get tree stage name based on stage ID
 * @param {number} stageId - Stage ID
 * @returns {string} Stage name
 */
const getTreeStageName = (stageId) => {
  const stageNames = {
    1: 'Seedling',
    2: 'Sapling',
    3: 'Young Tree',
    4: 'Mature Tree',
    5: 'Ancient Tree',
    6: 'Legendary Tree',
    7: 'Mythical Tree',
    8: 'Divine Tree',
    9: 'Cosmic Tree',
    10: 'Eternal Tree'
  };

  return stageNames[stageId] || `Stage ${stageId}`;
};

/**
 * Reset error state
 */
const resetError = () => {
  error.value = null;
};

// Export forest service
export const useForestService = () => {
  return {
    // State
    forestStats,
    isLoading,
    error,

    // Methods
    getForestStats,
    updateForestStats,
    resetError
  };
};
