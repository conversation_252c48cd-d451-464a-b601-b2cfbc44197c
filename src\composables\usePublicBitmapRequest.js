import { ref } from 'vue';
import { requestHandler } from '../services/bitmapService';

export const usePublicBitmapRequest = () => {
  const data = ref(null);
  const isLoading = ref(false);
  const error = ref(null);

  const fetchData = async (bitmap, func = '0', ref01 = '', ref02 = '', startDate = '', endDate = '') => {
    try {
      isLoading.value = true;
      error.value = null;

      const params = {
        bitmap,
        Func: func,
        StartDate: startDate,
        EndDate: endDate,
        Ref01: ref01,
        Ref02: ref02,
        Lang: 'en',
      };

      console.log('Sending bitmap request with params:', params);
      const res = await requestHandler(params);
      console.log('Bitmap response:', res);

      // Store the entire response
      data.value = res;
      return res;
    } catch (err) {
      error.value = err;
      console.error('Error in public bitmap request:', err);
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  return {
    data,
    isLoading,
    error,
    fetchData,
  };
}; 