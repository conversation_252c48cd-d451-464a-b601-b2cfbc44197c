/**
 * Location API service for managing location data
 */
import { ref } from 'vue';
import httpClient from '../utils/httpClient';
import { simulateApiDelay, createApiResponse, createApiErrorResponse } from './mockData';
import mockLocations from '../data/locations.json';

// Configuration
const USE_MOCK_DATA = import.meta.env.VITE_USE_MOCK_DATA === 'true'; // Set to false to use real API

// Location state
const locations = ref([]);
const selectedLocation = ref(null);
const isLoading = ref(false);
const error = ref(null);

/**
 * Calculate distance between two coordinates
 * @param {number} lat1 - Latitude of first point
 * @param {number} lon1 - Longitude of first point
 * @param {number} lat2 - Latitude of second point
 * @param {number} lon2 - Longitude of second point
 * @returns {number} Distance in kilometers
 */
const calculateDistance = (lat1, lon1, lat2, lon2) => {
  if (!lat1 || !lon1 || !lat2 || !lon2) {
    return null;
  }

  const R = 6371; // Radius of the earth in km
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c; // Distance in km

  return parseFloat(distance.toFixed(1));
};

/**
 * Format distance text
 * @param {number} distance - Distance in kilometers
 * @returns {string} Formatted distance text
 */
const formatDistanceText = (distance) => {
  if (distance === null || distance === undefined) {
    return '';
  }

  if (distance < 1) {
    return `${(distance * 1000).toFixed(0)} m`;
  }

  return `${distance.toFixed(1)} km`;
};

/**
 * Get all locations
 * @param {Object} coords - User coordinates (optional)
 * @returns {Promise<Array>} List of locations
 */
const getLocations = async (coords = null) => {
  isLoading.value = true;
  error.value = null;

  try {
    if (USE_MOCK_DATA) {
      // Mock implementation
      await simulateApiDelay();

      // Process locations with distance if coordinates provided
      let processedLocations = [...mockLocations];

      if (coords && coords.latitude && coords.longitude) {
        processedLocations = processedLocations.map(location => {
          const distance = calculateDistance(
            coords.latitude,
            coords.longitude,
            location.latitude,
            location.longitude
          );

          return {
            ...location,
            distance,
            distanceText: formatDistanceText(distance)
          };
        });

        // Sort by distance
        processedLocations.sort((a, b) => a.distance - b.distance);
      }

      locations.value = processedLocations;
      return createApiResponse(processedLocations);
    } else {
      // Real API implementation
      let endpoint = 'api/locations';

      // Add query parameters if coordinates provided
      if (coords && coords.latitude && coords.longitude) {
        endpoint += `?latitude=${coords.latitude}&longitude=${coords.longitude}`;
      }

      const response = await httpClient.get(endpoint);

      if (response.success) {
        locations.value = response.data;
      }

      return response;
    }
  } catch (err) {
    error.value = err.message;
    return createApiErrorResponse('LOCATION_SERVICE_UNAVAILABLE', err.message);
  } finally {
    isLoading.value = false;
  }
};

/**
 * Get location details by ID
 * @param {string} id - Location ID
 * @returns {Promise<Object>} Location details
 */
const getLocationDetails = async (id) => {
  isLoading.value = true;
  error.value = null;

  try {
    if (!id) {
      throw new Error('Location ID is required');
    }

    if (USE_MOCK_DATA) {
      // Mock implementation
      await simulateApiDelay();

      // Find location in mock data
      const location = mockLocations.find(loc => loc.id === id);

      if (!location) {
        return createApiErrorResponse('LOCATION_NOT_FOUND', 'Location not found');
      }

      // Add additional details for the mock
      const locationDetails = {
        ...location,
        description: `Main recycling center in ${location.name.split('-')[1].trim()}`,
        phoneNumber: '+60123456789',
        email: `${id}@example.com`,
        website: `https://${id}.example.com`,
        openingHours: [
          'Monday-Friday: 9:00 AM - 6:00 PM',
          'Saturday: 9:00 AM - 1:00 PM',
          'Sunday: Closed'
        ],
        images: [
          `https://example.com/location-images/${id}-1.jpg`,
          `https://example.com/location-images/${id}-2.jpg`
        ]
      };

      selectedLocation.value = locationDetails;
      return createApiResponse(locationDetails);
    } else {
      // Real API implementation
      const response = await httpClient.get(`api/locations/${id}`);

      if (response.success) {
        selectedLocation.value = response.data;
      }

      return response;
    }
  } catch (err) {
    error.value = err.message;
    return createApiErrorResponse('LOCATION_NOT_FOUND', err.message);
  } finally {
    isLoading.value = false;
  }
};

/**
 * Get nearby locations
 * @param {Object} coords - User coordinates
 * @param {number} radius - Search radius in kilometers (default: 10)
 * @returns {Promise<Array>} List of nearby locations
 */
const getNearbyLocations = async (coords, radius = 10) => {
  isLoading.value = true;
  error.value = null;

  try {
    if (!coords || !coords.latitude || !coords.longitude) {
      throw new Error('Coordinates are required');
    }

    if (USE_MOCK_DATA) {
      // Mock implementation
      await simulateApiDelay();

      // Filter and sort locations by distance
      const nearbyLocations = mockLocations
        .map(location => {
          const distance = calculateDistance(
            coords.latitude,
            coords.longitude,
            location.latitude,
            location.longitude
          );

          return {
            ...location,
            distance,
            distanceText: formatDistanceText(distance)
          };
        })
        .filter(location => location.distance <= radius)
        .sort((a, b) => a.distance - b.distance);

      return createApiResponse(nearbyLocations);
    } else {
      // Real API implementation
      const endpoint = `api/locations/nearby?latitude=${coords.latitude}&longitude=${coords.longitude}&radius=${radius}`;
      const response = await httpClient.get(endpoint);

      return response;
    }
  } catch (err) {
    error.value = err.message;
    return createApiErrorResponse('LOCATION_SERVICE_UNAVAILABLE', err.message);
  } finally {
    isLoading.value = false;
  }
};

/**
 * Reset error state
 */
const resetError = () => {
  error.value = null;
};

// Export location service
export const useApiLocationService = () => {
  return {
    // State
    locations,
    selectedLocation,
    isLoading,
    error,

    // Methods
    getLocations,
    getLocationDetails,
    getNearbyLocations,
    calculateDistance,
    formatDistanceText,
    resetError
  };
};
