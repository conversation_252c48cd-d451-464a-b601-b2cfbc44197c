/**
 * Bitmap Service
 * Handles API requests for bitmap functionality
 */
import { ref } from 'vue';
import { httpClient } from '../utils/httpClient';
import { API_CONFIG } from './index';

// State
const isLoading = ref(false);
const error = ref(null);
const bitmapResult = ref(null);

/**
 * Bitmap Service composable
 * @returns {Object} - Bitmap service methods and state
 */
export const useBitmapService = () => {
  /**
   * Reset error state
   */
  const resetError = () => {
    error.value = null;
  };

  /**
   * Send a request to the bitmap API
   * @param {Object} params - Request parameters
   * @param {string} params.Token - Authentication token
   * @param {number} params.bitmap - Bitmap value
   * @param {string} params.Func - Function code
   * @param {string} params.Ref01 - Account number (changed from AcctNo)
   * @param {string} [params.ref02] - Secondary reference (VoucherId, etc.)
   * @param {string} [params.startDate] - Start date for date range queries
   * @param {string} [params.endDate] - End date for date range queries
   * @param {string} params.Lang - Language code
   * @returns {Promise<Object>} - API response
   */
  const sendBitmapRequest = async (params) => {
    try {
      isLoading.value = true;
      resetError();

      // Validate required parameters
      if (!params.Token) {
        throw new Error('Token is required');
      }

      if (params.bitmap === undefined) {
        throw new Error('bitmap is required');
      }

      if (!params.Func) {
        throw new Error('Func is required');
      }

      if (!params.Ref01) {
        throw new Error('Ref01 is required');
      }

      if (!params.Lang) {
        throw new Error('Lang is required');
      }

      // Log the full request for debugging
      console.log('Bitmap request parameters:', params);

      // Use the local development proxy instead of direct API call or CORS proxy
      const url = '/bitmap-api/RequestHandlerAuth/';
      console.log('Sending bitmap request to:', url, '(proxied to http://api2.rvmplus.com/api/RequestHandlerAuth/)');

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(params),
      });

      // Parse the response
      const data = await response.json();
      console.log('Bitmap response:', data);

      // Store the result
      bitmapResult.value = data;

      return { data, success: true };
    } catch (err) {
      error.value = err;
      console.error('Bitmap request error:', err);
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * Get bitmap result by function code
   * @param {string} funcCode - Function code to filter by
   * @returns {Object|null} - Filtered bitmap result or null if not found
   */
  const getBitmapResultByFunc = (funcCode) => {
    if (!bitmapResult.value || !funcCode) {
      return null;
    }

    // Filter result by function code
    return bitmapResult.value.find(item => item.Func === funcCode) || null;
  };

  return {
    // State
    isLoading,
    error,
    bitmapResult,

    // Methods
    sendBitmapRequest,
    getBitmapResultByFunc,
    resetError,
  };
};

export default useBitmapService;

/**
 * Calls the RequestHandlerAuth endpoint with the provided parameters.
 * @param {Object} params - The request parameters.
 * @returns {Promise} - The API response.
 */
export async function requestHandlerAuth(params) {
  const response = await fetch('/bitmap-api/RequestHandlerAuth/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });
  if (!response.ok) {
    throw new Error('Network response was not ok');
  }
  return response.json();
}

/**
 * Calls the RequestHandler endpoint with the provided parameters (public version without token).
 * @param {Object} params - The request parameters.
 * @returns {Promise} - The API response.
 */
export async function requestHandler(params) {
  const response = await fetch('/bitmap-api/RequestHandler/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });
  if (!response.ok) {
    throw new Error('Network response was not ok');
  }
  return response.json();
}

/**
 * Public Bitmap Service composable
 * @returns {Object} - Public bitmap service methods and state
 */
export const usePublicBitmapService = () => {
  const isLoading = ref(false);
  const error = ref(null);
  const bitmapResult = ref(null);

  const resetError = () => {
    error.value = null;
  };

  const sendPublicBitmapRequest = async (params) => {
    try {
      isLoading.value = true;
      resetError();

      // Validate required parameters
      if (params.bitmap === undefined) {
        throw new Error('bitmap is required');
      }

      if (!params.Func) {
        throw new Error('Func is required');
      }

      if (!params.Ref01) {
        throw new Error('Ref01 is required');
      }

      if (!params.Lang) {
        throw new Error('Lang is required');
      }

      // Log the full request for debugging
      console.log('Public bitmap request parameters:', params);

      const url = '/bitmap-api/RequestHandler/';
      console.log('Sending public bitmap request to:', url);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(params),
      });

      const data = await response.json();
      console.log('Public bitmap response:', data);

      bitmapResult.value = data;
      return { data, success: true };
    } catch (err) {
      error.value = err;
      console.error('Public bitmap request error:', err);
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const getPublicBitmapResultByFunc = (funcCode) => {
    if (!bitmapResult.value || !funcCode) {
      return null;
    }

    return bitmapResult.value.find(item => item.Func === funcCode) || null;
  };

  return {
    isLoading,
    error,
    bitmapResult,
    sendPublicBitmapRequest,
    getPublicBitmapResultByFunc,
    resetError,
  };
};
