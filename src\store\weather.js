// Weather store to manage weather state
import { ref, computed } from 'vue';
import { useWeatherService, WEATHER_TYPES } from '../services/weatherService';

// Get weather service
const {
  weatherData,
  isLoading,
  error,
  getWeatherForCurrentLocation,
  resetError,
} = useWeatherService();

// Weather images and emojis
const getWeatherImage = (weatherType) => {
  switch (weatherType) {
    case WEATHER_TYPES.SUNNY:
      return new URL('../assets/weather-sunny.svg', import.meta.url).href;
    case WEATHER_TYPES.CLOUDY:
      return new URL('../assets/weather-cloudy.svg', import.meta.url).href;
    case WEATHER_TYPES.RAINY:
      return new URL('../assets/weather-rainy.svg', import.meta.url).href;
    default:
      return new URL('../assets/weather-sunny.svg', import.meta.url).href;
  }
};

// Weather emojis for simpler display
const getWeatherEmoji = (weatherType) => {
  switch (weatherType) {
    case WEATHER_TYPES.SUNNY:
      return '☀️';
    case WEATHER_TYPES.CLOUDY:
      return '☁️';
    case WEATHER_TYPES.RAINY:
      return '🌧️';
    default:
      return '☀️';
  }
};

// Computed properties
const weatherImage = computed(() => {
  if (!weatherData.value) {return null;}
  return getWeatherImage(weatherData.value.weatherType);
});

const weatherEmoji = computed(() => {
  if (!weatherData.value) {return '☀️';}
  return getWeatherEmoji(weatherData.value.weatherType);
});

const weatherDescription = computed(() => {
  if (!weatherData.value) {return '';}
  return weatherData.value.description;
});

const temperature = computed(() => {
  if (!weatherData.value) {return null;}
  return weatherData.value.temperature;
});

// Initialize weather data
const initializeWeather = async () => {
  try {
    await getWeatherForCurrentLocation();
    return weatherData.value;
  } catch (err) {
    console.error('Error initializing weather:', err);
    return null;
  }
};

// Export the weather store
export const useWeather = () => {
  return {
    // State
    weatherData,
    isLoading,
    error,

    // Computed
    weatherImage,
    weatherEmoji,
    weatherDescription,
    temperature,

    // Methods
    initializeWeather,
    getWeatherForCurrentLocation,
    resetError,
  };
};
