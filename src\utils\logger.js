/**
 * Logger utility for conditional logging based on environment
 * This provides a centralized way to handle logging throughout the application
 */

import { isDevelopment, isStaging } from './envConfig';

/**
 * Check if logging is enabled
 * Logging is enabled in development and staging, but disabled in production
 */
const isLoggingEnabled = () => {
  return isDevelopment() || isStaging();
};

/**
 * Log levels
 */
export const LOG_LEVELS = {
  DEBUG: 'debug',
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error'
};

/**
 * Conditional console.log - only logs in development and staging
 * @param {...any} args - Arguments to log
 */
export const log = (...args) => {
  if (isLoggingEnabled()) {
    console.log(...args);
  }
};

/**
 * Conditional console.info - only logs in development and staging
 * @param {...any} args - Arguments to log
 */
export const info = (...args) => {
  if (isLoggingEnabled()) {
    console.info(...args);
  }
};

/**
 * Conditional console.warn - only logs in development and staging
 * @param {...any} args - Arguments to log
 */
export const warn = (...args) => {
  if (isLoggingEnabled()) {
    console.warn(...args);
  }
};

/**
 * Conditional console.error - always logs (even in production for critical errors)
 * @param {...any} args - Arguments to log
 */
export const error = (...args) => {
  // Always log errors, even in production, as they are critical
  console.error(...args);
};

/**
 * Debug logging - only in development mode
 * @param {...any} args - Arguments to log
 */
export const debug = (...args) => {
  if (isDevelopment()) {
    console.debug(...args);
  }
};

/**
 * Group logging - only in development and staging
 * @param {string} label - Group label
 * @param {Function} fn - Function to execute within the group
 */
export const group = (label, fn) => {
  if (isLoggingEnabled()) {
    console.group(label);
    try {
      fn();
    } finally {
      console.groupEnd();
    }
  } else {
    // Still execute the function, just without grouping
    fn();
  }
};

/**
 * Table logging - only in development and staging
 * @param {any} data - Data to display in table format
 * @param {Array} columns - Optional columns to display
 */
export const table = (data, columns) => {
  if (isLoggingEnabled()) {
    if (columns) {
      console.table(data, columns);
    } else {
      console.table(data);
    }
  }
};

/**
 * Time logging - only in development and staging
 * @param {string} label - Timer label
 */
export const time = (label) => {
  if (isLoggingEnabled()) {
    console.time(label);
  }
};

/**
 * End time logging - only in development and staging
 * @param {string} label - Timer label
 */
export const timeEnd = (label) => {
  if (isLoggingEnabled()) {
    console.timeEnd(label);
  }
};

/**
 * API request logging helper
 * @param {string} method - HTTP method
 * @param {string} url - Request URL
 * @param {any} data - Request data
 * @param {any} response - Response data
 */
export const logApiRequest = (method, url, data = null, response = null) => {
  if (isLoggingEnabled()) {
    group(`API ${method.toUpperCase()} ${url}`, () => {
      if (data) {
        log('Request data:', data);
      }
      if (response) {
        log('Response:', response);
      }
    });
  }
};

/**
 * Component lifecycle logging helper
 * @param {string} componentName - Name of the component
 * @param {string} lifecycle - Lifecycle event (mounted, unmounted, etc.)
 * @param {any} data - Optional data to log
 */
export const logComponentLifecycle = (componentName, lifecycle, data = null) => {
  if (isDevelopment()) {
    const message = `[${componentName}] ${lifecycle}`;
    if (data) {
      log(message, data);
    } else {
      log(message);
    }
  }
};

/**
 * Error boundary logging helper
 * @param {Error} error - Error object
 * @param {string} context - Context where error occurred
 * @param {any} additionalInfo - Additional information
 */
export const logError = (error, context = 'Unknown', additionalInfo = null) => {
  // Always log errors, even in production
  group(`Error in ${context}`, () => {
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
    if (additionalInfo) {
      console.error('Additional info:', additionalInfo);
    }
  });
};

/**
 * Performance logging helper
 * @param {string} operation - Operation name
 * @param {Function} fn - Function to measure
 * @returns {any} Result of the function
 */
export const logPerformance = async (operation, fn) => {
  if (isLoggingEnabled()) {
    const startTime = performance.now();
    try {
      const result = await fn();
      const endTime = performance.now();
      log(`Performance [${operation}]: ${(endTime - startTime).toFixed(2)}ms`);
      return result;
    } catch (error) {
      const endTime = performance.now();
      warn(`Performance [${operation}] failed after ${(endTime - startTime).toFixed(2)}ms:`, error);
      throw error;
    }
  } else {
    return await fn();
  }
};

/**
 * Create a logger instance for a specific module
 * @param {string} moduleName - Name of the module
 * @returns {Object} Logger instance with prefixed methods
 */
export const createLogger = (moduleName) => {
  const prefix = `[${moduleName}]`;
  
  return {
    log: (...args) => log(prefix, ...args),
    info: (...args) => info(prefix, ...args),
    warn: (...args) => warn(prefix, ...args),
    error: (...args) => error(prefix, ...args),
    debug: (...args) => debug(prefix, ...args),
    group: (label, fn) => group(`${prefix} ${label}`, fn),
    table: (data, columns) => {
      if (isLoggingEnabled()) {
        log(prefix);
        table(data, columns);
      }
    },
    time: (label) => time(`${prefix} ${label}`),
    timeEnd: (label) => timeEnd(`${prefix} ${label}`),
  };
};

// Default export with all logging functions
export default {
  log,
  info,
  warn,
  error,
  debug,
  group,
  table,
  time,
  timeEnd,
  logApiRequest,
  logComponentLifecycle,
  logError,
  logPerformance,
  createLogger,
  isLoggingEnabled,
  LOG_LEVELS
};
