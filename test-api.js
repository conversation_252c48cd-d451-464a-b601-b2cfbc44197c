// Test script for API calls to staging environment

// Configuration
const API_BASE_URL = 'https://staging.rvmplus.com';
const API_TIMEOUT = 30000;

// Helper function to make API requests
async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;

  // Default headers
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...options.headers,
  };

  // Add authorization header if token exists
  const token = options.token;
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  // Request options
  const requestOptions = {
    ...options,
    headers,
  };

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT);

    requestOptions.signal = controller.signal;

    const response = await fetch(url, requestOptions);
    clearTimeout(timeoutId);

    // Parse response
    let data;
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }

    // Handle API error responses
    if (!response.ok) {
      const errorMessage = typeof data === 'object' && data.message ? data.message : 'An unexpected error occurred';
      const errorCode = typeof data === 'object' && data.error && data.error.code ? data.error.code : 'UNKNOWN_ERROR';

      const error = new Error(errorMessage);
      error.status = response.status;
      error.code = errorCode;
      error.response = data;

      throw error;
    }

    return data;
  } catch (error) {
    if (error.name === 'AbortError') {
      throw new Error('Request timed out');
    }

    throw error;
  }
}

// HTTP GET request
async function get(endpoint, options = {}) {
  return apiRequest(endpoint, {
    method: 'GET',
    ...options,
  });
}

// HTTP POST request
async function post(endpoint, data, options = {}) {
  return apiRequest(endpoint, {
    method: 'POST',
    body: JSON.stringify(data),
    ...options,
  });
}

// Test functions
async function testGetFAQ() {
  console.log('Testing GET FAQ endpoint...');
  try {
    const data = await get('api/FAQ/en/');
    console.log('Success! Received', data.length, 'FAQs');
    console.log('First FAQ:', data[0]);
  } catch (error) {
    console.error('Error fetching FAQs:', error.message);
  }
}

async function testGetAnnouncements() {
  console.log('\nTesting GET Announcements endpoint...');
  try {
    const data = await get('api/Announcement/en/');
    console.log('Success! Received', data.length, 'announcements');
    console.log('First announcement:', data[0]);
  } catch (error) {
    console.error('Error fetching announcements:', error.message);
  }
}

async function testLogin() {
  console.log('\nTesting POST Login endpoint...');
  try {
    const data = await post('api/cmlogin', {
      userId: 'test',
      password: 'test',
      lang: 'en'
    });
    console.log('Login response:', data);
  } catch (error) {
    console.error('Error during login:', error.message);
    console.error('Response:', error.response);
  }
}



// Run tests
async function runTests() {
  await testGetFAQ();
  await testGetAnnouncements();
  await testLogin();

  console.log('\nAll tests completed!');
}

// Execute tests
runTests();
