/**
 * Environment configuration utility
 * Provides access to environment variables and environment-specific configurations
 */

// Environment mode
export const ENV_MODE = import.meta.env.MODE;

// API configuration
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000',
  TIMEOUT: parseInt(import.meta.env.VITE_API_TIMEOUT || '30000', 10),
  USE_MOCK_DATA: import.meta.env.VITE_USE_MOCK_DATA === 'true'
};

// App configuration
export const APP_CONFIG = {
  NAME: import.meta.env.VITE_APP_NAME || 'RVMPlus Rewards',
  VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0'
};

/**
 * Check if the current environment is development
 * @returns {boolean} True if development environment
 */
export const isDevelopment = () => ENV_MODE === 'development';

/**
 * Check if the current environment is staging
 * @returns {boolean} True if staging environment
 */
export const isStaging = () => ENV_MODE === 'staging';

/**
 * Check if the current environment is production
 * @returns {boolean} True if production environment
 */
export const isProduction = () => ENV_MODE === 'production';

/**
 * Get a value based on the current environment
 * @param {Object} options - Environment-specific options
 * @param {*} options.development - Value for development environment
 * @param {*} options.staging - Value for staging environment
 * @param {*} options.production - Value for production environment
 * @param {*} options.default - Default value if no environment match
 * @returns {*} The value for the current environment
 */
export const getEnvValue = (options) => {
  if (isDevelopment() && 'development' in options) {
    return options.development;
  }
  
  if (isStaging() && 'staging' in options) {
    return options.staging;
  }
  
  if (isProduction() && 'production' in options) {
    return options.production;
  }
  
  return options.default;
};

/**
 * Log environment information to the console
 * Only logs in development mode
 */
export const logEnvInfo = () => {
  if (isDevelopment()) {
    console.log('Environment Mode:', ENV_MODE);
    console.log('API Base URL:', API_CONFIG.BASE_URL);
    console.log('Using Mock Data:', API_CONFIG.USE_MOCK_DATA);
    console.log('App Name:', APP_CONFIG.NAME);
    console.log('App Version:', APP_CONFIG.VERSION);
  }
};

export default {
  ENV_MODE,
  API_CONFIG,
  APP_CONFIG,
  isDevelopment,
  isStaging,
  isProduction,
  getEnvValue,
  logEnvInfo
};
