// Test script for different login request formats with the staging API

// Configuration
const API_BASE_URL = 'https://staging.rvmplus.com';
const API_TIMEOUT = 30000;

// Test credentials
const TEST_PHONE = '60102431439';
const TEST_PASSWORD = '123456';

// Helper function to make API requests
async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
  
  // Default headers
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...options.headers,
  };
  
  // Request options
  const requestOptions = {
    ...options,
    headers,
  };
  
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT);
    
    requestOptions.signal = controller.signal;
    
    console.log(`Making ${options.method || 'GET'} request to: ${url}`);
    if (options.body) {
      console.log('Request body:', options.body);
    }
    
    const response = await fetch(url, requestOptions);
    clearTimeout(timeoutId);
    
    console.log('Response status:', response.status);
    
    // Parse response
    let data;
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }
    
    return {
      status: response.status,
      data: data
    };
  } catch (error) {
    if (error.name === 'AbortError') {
      return {
        status: 'timeout',
        error: 'Request timed out'
      };
    }
    
    return {
      status: 'error',
      error: error.message
    };
  }
}

// HTTP POST request
async function post(endpoint, data, options = {}) {
  return apiRequest(endpoint, {
    method: 'POST',
    body: JSON.stringify(data),
    ...options,
  });
}

// HTTP GET request
async function get(endpoint, options = {}) {
  return apiRequest(endpoint, { 
    method: 'GET',
    ...options,
  });
}

// Test login with different request formats
async function testLoginFormats() {
  console.log('Testing different login request formats...');
  
  // Format 1: Using userId and password
  console.log('\nFormat 1: Using userId and password');
  const result1 = await post('api/cmlogin', {
    userId: TEST_PHONE,
    password: TEST_PASSWORD,
    lang: 'en'
  });
  console.log('Response:', JSON.stringify(result1, null, 2));
  
  // Format 2: Using id and pass
  console.log('\nFormat 2: Using id and pass');
  const result2 = await post('api/cmlogin', {
    id: TEST_PHONE,
    pass: TEST_PASSWORD,
    lang: 'en'
  });
  console.log('Response:', JSON.stringify(result2, null, 2));
  
  // Format 3: Using phoneNumber and password
  console.log('\nFormat 3: Using phoneNumber and password');
  const result3 = await post('api/cmlogin', {
    phoneNumber: TEST_PHONE,
    password: TEST_PASSWORD,
    lang: 'en'
  });
  console.log('Response:', JSON.stringify(result3, null, 2));
  
  // Format 4: Using GET endpoint with parameters
  console.log('\nFormat 4: Using GET endpoint with parameters');
  const result4 = await get(`api/cmlogin/${TEST_PHONE}/${TEST_PASSWORD}/en`);
  console.log('Response:', JSON.stringify(result4, null, 2));
}

// Run the tests
testLoginFormats();
