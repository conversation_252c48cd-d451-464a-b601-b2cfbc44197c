/**
 * Home Service
 * Handles API requests for the home page data
 */
import { ref } from 'vue';
import { useBitmapService } from './bitmapService';
import { useAuthService } from '../services/authService';

// State (shared across all instances)
const isLoading = ref(false);
const error = ref(null);
const highlights = ref([]);
const newsUpdates = ref([]);
const lastFetchTime = ref(0);
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds
const pendingRequest = ref(null); // Store the pending promise to avoid duplicate requests

/**
 * Home Service composable
 * @returns {Object} - Home service methods and state
 */
export const useHomeService = () => {
  const bitmapService = useBitmapService();
  const { token, accountNumber, language } = useAuthService();

  /**
   * Reset error state
   */
  const resetError = () => {
    error.value = null;
  };

  /**
   * Check if cache is valid
   * @returns {boolean} - Whether the cache is still valid
   */
  const isCacheValid = () => {
    const now = Date.now();
    return (
      highlights.value.length > 0 &&
      newsUpdates.value.length > 0 &&
      now - lastFetchTime.value < CACHE_DURATION
    );
  };

  /**
   * Fetch home page data from the API
   * @param {boolean} [forceRefresh=false] - Whether to force a refresh even if cache is valid
   * @returns {Promise<Object>} - Home page data
   */
  const fetchHomeData = async (forceRefresh = false) => {
    try {
      // Return cached data if it's still valid and we're not forcing a refresh
      if (!forceRefresh && isCacheValid()) {
        console.log('Using cached home data');
        return {
          highlights: highlights.value,
          newsUpdates: newsUpdates.value,
          success: true,
        };
      }

      // If there's already a request in progress, return that promise
      // This prevents multiple simultaneous requests for the same data
      if (pendingRequest.value) {
        console.log('Using pending request for home data');
        return pendingRequest.value;
      }

      isLoading.value = true;
      resetError();

      // Create a new promise for this request
      pendingRequest.value = (async () => {
        try {
          // Prepare request parameters for bitmap 6 (Profile Basic + Home)
          const params = {
            Token: token.value,
            bitmap: 6, // Bitmap 6 = 2 (Profile Basic) + 4 (Profile Home)
            Func: '0',
            Ref01: accountNumber.value,
            Ref02: 'Home', // For Profile Home data
            Lang: language?.value || 'en',
          };

          console.log('Fetching home data from API');
          // Send request
          const result = await bitmapService.sendBitmapRequest(params);

          if (result && result.data) {
            console.log('Home data response:', result.data);

            // For bitmap 6, we need to extract data from a different structure
            // The response structure will be different from bitmap 1

            // Always use fallback data for highlights for now
            console.log('Using fallback data for highlights');
            const { highlights: fallbackHighlights } = await import('../data/highlights');
            highlights.value = fallbackHighlights;

            // Always use fallback data for news for now
            console.log('Using fallback data for news');
            const { newsItems: fallbackNews } = await import('../data/news');
            newsUpdates.value = fallbackNews;

            // Update last fetch time
            lastFetchTime.value = Date.now();

            return {
              highlights: highlights.value,
              newsUpdates: newsUpdates.value,
              success: true,
            };
          }

          return { success: false };
        } catch (err) {
          console.error('Error fetching home data:', err);
          error.value = err;
          throw err;
        } finally {
          isLoading.value = false;
          // Clear the pending request
          pendingRequest.value = null;
        }
      })();

      // Return the promise
      return pendingRequest.value;
    } catch (err) {
      console.error('Error in fetchHomeData:', err);
      error.value = err;
      throw err;
    }
  };

  return {
    // State
    isLoading,
    error,
    highlights,
    newsUpdates,

    // Methods
    fetchHomeData,
    resetError,
  };
};

export default useHomeService;
