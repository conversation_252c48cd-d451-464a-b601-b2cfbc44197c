<script setup>
import { ref, watch } from 'vue';
import { useAuth } from '../store/auth';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useNotification } from '../store/notification';

const { login, isLoading, error } = useAuth();
const router = useRouter();
const { t } = useI18n();
const { showSuccess } = useNotification();

const phoneNumber = ref('');
const password = ref('');
const isPhoneValid = ref(false);
const isPasswordValid = ref(false);
const showPassword = ref(false);

// Validate phone number format
const validatePhone = () => {
  // Simple validation - at least 6 digits (international numbers can vary in length)
  isPhoneValid.value = /^\d{6,}$/.test(phoneNumber.value);
};

// Validate password
const validatePassword = () => {
  isPasswordValid.value = password.value.length >= 4;
};

// Watch for changes in inputs
watch(phoneNumber, validatePhone);
watch(password, validatePassword);

// Handle form submission
const handleSubmit = async () => {
  if (!isPhoneValid.value || !isPasswordValid.value || isLoading.value) {
    return;
  }

  const response = await login(phoneNumber.value, password.value);

  if (response && response.success) {
    // Show success notification
    showSuccess(t('auth.loginSuccess', 'Login successful!'), {
      duration: 3000,
      position: 'top'
    });

    // Redirect to home page on successful login
    router.push('/');
  }
};
</script>

<template>
  <div class="password-login-container">
    <h2 class="login-title">{{ t('auth.login', 'Login') }}</h2>
    <p class="login-subtitle">{{ t('auth.loginWithPassword', 'Login with your phone number and password') }}</p>

    <form class="login-form" @submit.prevent="handleSubmit">
      <!-- Phone Number Input -->
      <div class="input-group">
        <label for="phone" class="input-label">{{ t('auth.phone', 'Phone Number') }}</label>
        <input
          id="phone"
          v-model="phoneNumber"
          type="tel"
          :placeholder="t('auth.enterPhone', 'Enter your phone number')"
          class="form-input"
          autocomplete="tel"
          inputmode="numeric"
          pattern="[0-9]*"
        />
      </div>

      <!-- Password Input -->
      <div class="input-group">
        <label for="password" class="input-label">{{ t('auth.password', 'Password') }}</label>
        <div class="password-input-wrapper">
          <input
            id="password"
            v-model="password"
            :type="showPassword ? 'text' : 'password'"
            :placeholder="t('auth.enterPassword', 'Enter your password')"
            class="form-input"
            autocomplete="current-password"
          />
          <button
            type="button"
            class="toggle-password-btn"
            @click="showPassword = !showPassword"
            tabindex="-1"
          >
            <span v-if="showPassword">{{ t('auth.hide', 'Hide') }}</span>
            <span v-else>{{ t('auth.show', 'Show') }}</span>
          </button>
        </div>
      </div>

      <!-- Error Message -->
      <p v-if="error" class="error-message">{{ error }}</p>

      <!-- Submit Button -->
      <button
        type="submit"
        class="submit-button"
        :disabled="!isPhoneValid || !isPasswordValid || isLoading"
      >
        <span v-if="isLoading">{{ t('auth.loggingIn', 'Logging in...') }}</span>
        <span v-else>{{ t('auth.login', 'Login') }}</span>
      </button>

      <!-- Test Account Info -->
      <p class="test-note">
        {{ t('auth.testNote', 'For testing, use: *********** / 123456') }}
      </p>
    </form>
  </div>
</template>

<style scoped>
.password-login-container {
  padding: 1.5rem;
  max-width: 480px;
  margin: 0 auto;
  position: relative;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.login-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: var(--text-color);
  text-align: center;
  letter-spacing: -0.5px;
}

.login-subtitle {
  font-size: 1rem;
  color: var(--text-light);
  margin-bottom: 2rem;
  text-align: center;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.input-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-color);
}

.form-input {
  width: 100%;
  height: 3.5rem;
  padding: 0 1.2rem;
  border: 1px solid var(--border-color);
  border-radius: 12px;
  font-size: 1rem;
  outline: none;
  color: var(--text-color);
  background-color: var(--card-background);
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.2);
}

.form-input::placeholder {
  color: var(--text-light);
  opacity: 0.7;
}

.password-input-wrapper {
  position: relative;
}

.toggle-password-btn {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
}

.toggle-password-btn:hover {
  text-decoration: underline;
}

.error-message {
  color: var(--error-color);
  font-size: 0.9rem;
  margin-top: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: rgba(255, 82, 82, 0.1);
  border-radius: 8px;
  border-left: 3px solid var(--error-color);
}

.submit-button {
  height: 3.5rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.submit-button:hover:not(:disabled) {
  background-color: var(--primary-dark);
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.test-note {
  font-size: 0.85rem;
  color: var(--text-light);
  text-align: center;
  margin-top: 1.5rem;
  padding: 0.75rem;
  background-color: var(--highlight-color);
  border-radius: 8px;
}

/* Dark mode adjustments */
:global(.dark-mode) .form-input {
  background-color: var(--card-background);
  color: var(--text-color);
}

:global(.dark-mode) .toggle-password-btn {
  color: var(--primary-light);
}
</style>
