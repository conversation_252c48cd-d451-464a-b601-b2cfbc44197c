<script setup>
import { ref, watch, onMounted, computed } from 'vue';
import { useAuth } from '../store/auth';
import { useCountryService } from '../services/countryService';
import { useI18n } from 'vue-i18n';

const { sendOTP, isLoading, error } = useAuth();
const { t } = useI18n();
const {
  getCountryData,
  isLoading: isLoadingCountries,
  error: countryError,
} = useCountryService();

const phoneNumber = ref('');
const isValid = ref(false);
const countries = ref([]);
const selectedCountry = ref(null);
const showCountryDropdown = ref(false);
const searchQuery = ref('');

// Fetch country data on component mount
onMounted(async () => {
  try {
    countries.value = await getCountryData();
    console.log(`Loaded ${countries.value.length} countries`);

    // Default to Malaysia (MY) if available, otherwise use the first country
    const malaysiaCountry = countries.value.find(c => c.code === 'MY');
    if (malaysiaCountry) {
      console.log('Found Malaysia in country data');
      selectedCountry.value = malaysiaCountry;
    } else {
      console.log('Malaysia not found, using first country');
      selectedCountry.value = countries.value[0];
    }

    // Log the selected country for debugging
    if (selectedCountry.value) {
      console.log('Selected country:', selectedCountry.value.code, selectedCountry.value.name, selectedCountry.value.phoneCode);
    } else {
      console.error('No country selected');
    }
  } catch (err) {
    console.error('Failed to load country data:', err);
  }
});

// Filtered countries based on search query
const filteredCountries = computed(() => {
  if (!searchQuery.value) {return countries.value;}

  const query = searchQuery.value.toLowerCase();
  return countries.value.filter(country =>
    country.name.toLowerCase().includes(query) ||
    country.code.toLowerCase().includes(query) ||
    country.phoneCode.includes(query),
  );
});

// Validate phone number format
const validatePhone = () => {
  // Simple validation - at least 6 digits (international numbers can vary in length)
  isValid.value = /^\d{6,}$/.test(phoneNumber.value);
};

// Watch for changes in phone number
watch(phoneNumber, validatePhone);

// Handle form submission
const handleSubmit = async () => {
  if (!isValid.value || !selectedCountry.value) {return;}

  // Format the phone number with country code
  const formattedNumber = `${selectedCountry.value.phoneCode}${phoneNumber.value}`;

  await sendOTP(phoneNumber.value, {
    countryCode: selectedCountry.value.code,
    countryName: selectedCountry.value.name,
    phoneCode: selectedCountry.value.phoneCode,
    formattedNumber,
  });
};

// Select a country from the dropdown
const selectCountry = (country) => {
  selectedCountry.value = country;
  showCountryDropdown.value = false;
  searchQuery.value = '';
};

// Toggle country dropdown
const toggleDropdown = () => {
  console.log('Toggle dropdown called, current state:', showCountryDropdown.value);
  showCountryDropdown.value = !showCountryDropdown.value;
  console.log('Dropdown state after toggle:', showCountryDropdown.value);

  if (showCountryDropdown.value) {
    console.log('Dropdown should be visible now');

    // Only add body class on mobile to prevent background scrolling
    if (window.innerWidth <= 480) {
      document.body.classList.add('dropdown-open');
    }

    // Reset search query when opening
    searchQuery.value = '';

    // Focus the search input when dropdown opens
    setTimeout(() => {
      const searchInput = document.getElementById('country-search');
      if (searchInput) {
        searchInput.focus();
        console.log('Search input focused');
      } else {
        console.error('Search input element not found');
      }

      // For mobile: scroll to top of the list to ensure good starting position
      const countryList = document.querySelector('.country-list');
      if (countryList) {
        countryList.scrollTop = 0;
      }

      // On mobile, ensure the dropdown is visible by scrolling to it
      if (window.innerWidth <= 480) {
        window.scrollTo(0, 0);
      }
    }, window.innerWidth <= 480 ? 300 : 100); // Longer timeout for mobile
  } else {
    // Remove body class when dropdown closes
    document.body.classList.remove('dropdown-open');
  }
};

// Close dropdown function
const closeDropdown = () => {
  showCountryDropdown.value = false;
  document.body.classList.remove('dropdown-open');

  // Reset search query when closing
  setTimeout(() => {
    searchQuery.value = '';
  }, 300); // Wait for animation to complete
};

// Handle outside clicks
const handleOutsideClick = (event) => {
  console.log('Outside click handler called');
  // Check if the click was outside both the selector and dropdown
  if (!event.target.closest('.country-selector') && !event.target.closest('.country-dropdown')) {
    console.log('Click was outside dropdown, closing it');
    closeDropdown();
  } else {
    console.log('Click was inside dropdown or selector, keeping it open');
  }
};



// Add event listeners
onMounted(() => {
  document.addEventListener('click', handleOutsideClick);

  // Clean up event listeners when component is unmounted
  return () => {
    document.removeEventListener('click', handleOutsideClick);
    document.body.classList.remove('dropdown-open');
  };
});
</script>

<template>
  <div class="phone-input-container" :class="{ 'dropdown-active': showCountryDropdown }">
    <h2 class="login-title">{{ t('auth.login') }}</h2>
    <p class="login-subtitle">{{ t('auth.enterPhone') }}</p>

    <form class="phone-form" @submit.prevent="handleSubmit">
      <div class="input-group">
        <label for="phone" class="input-label">{{ t('auth.phone') }}</label>

        <div class="phone-input-wrapper">
          <!-- Country Selector -->
          <div class="country-selector" @click="toggleDropdown">
            <div v-if="selectedCountry" class="selected-country">
              <span class="country-flag">{{ selectedCountry.code }}</span>
              <span class="country-code">+{{ selectedCountry.phoneCode.replace('+', '') }}</span>
            </div>
            <div v-else class="selected-country">
              <span class="country-code">+</span>
            </div>
            <span class="dropdown-arrow">▾</span>
          </div>

          <!-- Phone Number Input -->
          <input
            id="phone"
            v-model="phoneNumber"
            type="tel"
            :placeholder="t('auth.enterPhone')"
            class="phone-input"
            autocomplete="tel"
            inputmode="numeric"
            pattern="[0-9]*"
          />

          <!-- Country Dropdown (positioned absolutely) - Desktop version -->
          <div v-show="showCountryDropdown" class="country-dropdown">
            <div class="country-search">
              <div class="search-header">
                <h3 class="search-title">Select Country</h3>
                <button type="button" class="close-dropdown-button" aria-label="Close" @click="closeDropdown">
                  ✕
                </button>
              </div>
              <div class="search-input-wrapper">
                <span class="search-icon">🔍</span>
                <input
                  id="country-search"
                  v-model="searchQuery"
                  type="text"
                  placeholder="Search countries..."
                  class="search-input"
                  autocomplete="off"
                  autocorrect="off"
                  spellcheck="false"
                />
              </div>
            </div>

            <!-- Show error message if any -->
            <div v-if="countryError" class="api-status-message error">
              <p>{{ countryError }}</p>
            </div>

            <div class="country-list-wrapper">
              <div v-if="!isLoadingCountries" class="country-list">
                <div
                  v-for="country in filteredCountries"
                  :key="country.code"
                  class="country-item"
                  @click="selectCountry(country)"
                >
                  <span class="country-flag">{{ country.code }}</span>
                  <span class="country-name">{{ country.name || country.code }}</span>
                  <span class="country-code">{{ country.phoneCode }}</span>
                </div>

                <div v-if="filteredCountries.length === 0" class="no-results">
                  {{ t('common.noResults', 'No countries found') }}
                </div>

                <!-- Extra space at the bottom for better scrolling -->
                <div class="scroll-spacer"></div>
              </div>

              <div v-if="isLoadingCountries" class="loading-countries">
                {{ t('common.loading') }}
              </div>
            </div>
          </div>
        </div>

        <p v-if="error" class="error-message">{{ error }}</p>
      </div>

      <button
        type="submit"
        class="submit-button"
        :disabled="!isValid || isLoading || !selectedCountry"
      >
        <span v-if="isLoading">{{ t('common.sending', 'Sending...') }}</span>
        <span v-else>{{ t('auth.sendOtp') }}</span>
      </button>

      <p class="test-note">
        {{ t('auth.testNote', 'For testing, use: 1234567890 or 9876543210') }}
      </p>
    </form>
  </div>
</template>

<style scoped>
.phone-input-container {
  padding: 1.5rem;
  max-width: 480px;
  margin: 0 auto;
  position: relative;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.login-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: var(--text-color);
  text-align: center;
  letter-spacing: -0.5px;
}

.login-subtitle {
  font-size: 1.1rem;
  color: var(--text-light);
  margin-bottom: 2rem;
  text-align: center;
  line-height: 1.5;
}

.phone-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.input-label {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-left: 0.25rem;
}

.phone-input-wrapper {
  display: flex;
  align-items: center;
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: visible; /* Changed from hidden to visible to allow dropdown to show */
  transition: all 0.3s ease;
  position: relative; /* Added to ensure proper positioning of dropdown */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  background-color: var(--card-background); /* Use theme background color */
  height: 48px; /* Fixed height for better alignment */
  margin-bottom: 0;
}

.phone-input-wrapper:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.2);
}

.country-selector {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.75rem;
  background-color: var(--highlight-color); /* Use theme highlight color */
  border-right: 1px solid var(--border-color); /* Use theme border color */
  cursor: pointer;
  min-width: 80px;
  user-select: none;
  height: 100%;
  transition: all 0.2s ease;
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;
}

.country-selector:hover {
  background-color: var(--border-color); /* Use theme border color for hover */
}

.selected-country {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  font-size: 0.9rem;
  padding: 0;
  white-space: nowrap;
}

.country-flag {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--primary-color);
}

.country-code {
  font-weight: 500;
  color: var(--text-color); /* Use theme text color */
}

.dropdown-arrow {
  margin-left: 0.25rem;
  font-size: 0.8rem;
  color: var(--text-light); /* Use theme light text color */
  transition: transform 0.2s ease;
}

.country-selector:hover .dropdown-arrow {
  transform: translateY(1px);
}

.country-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 320px;
  max-height: 350px;
  background-color: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000; /* Increased z-index to ensure it appears above other elements */
  margin-top: 0.75rem;
  overflow: hidden;
  display: flex; /* Use flexbox for layout */
  flex-direction: column; /* Stack children vertically */
  animation: fadeIn 0.2s ease-out;
}

/* Wrapper for the country list in web view */
.country-list-wrapper {
  flex: 1;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 250px; /* Fixed height for web view to ensure scrolling works */
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.country-search {
  padding: 0.75rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--highlight-color); /* Use theme highlight color */
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.search-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-color);
}

.close-dropdown-button {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: var(--text-light); /* Use theme light text color */
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  display: none; /* Hidden by default, shown on mobile */
}

.close-dropdown-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-color); /* Use theme text color */
}

.search-input-wrapper {
  position: relative;
  width: 100%;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.9rem;
  color: var(--text-light); /* Use theme light text color */
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding: 0.7rem 1rem 0.7rem 2.5rem; /* Reduced vertical padding */
  border: 1px solid var(--border-color);
  border-radius: 12px;
  font-size: 0.95rem; /* Slightly smaller font */
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) inset;
  background-color: var(--card-background); /* Use theme background color */
  color: var(--text-color); /* Use theme text color */
}

.search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.2);
  outline: none;
}

.country-list {
  flex: 1; /* Take remaining space */
  overflow-y: auto;
  padding: 0.5rem 0;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  overscroll-behavior: contain; /* Prevent scroll chaining */
  max-height: 250px; /* Ensure scrolling works in web view */
  min-height: 100px; /* Minimum height to ensure scrolling is possible */
}

.scroll-spacer {
  height: 20px; /* Space at the bottom of the list for better scrolling */
  flex-shrink: 0;
}

.country-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.country-item:hover {
  background-color: rgba(78, 205, 196, 0.1);
  border-left: 3px solid var(--primary-color);
}

.country-name {
  flex: 1;
  margin: 0 0.75rem;
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-color); /* Use theme text color */
}

.no-results, .loading-countries {
  padding: 1rem;
  text-align: center;
  color: var(--text-light);
  font-size: 0.9rem;
}

.api-status-message {
  padding: 0.75rem;
  background-color: var(--highlight-color);
  border-radius: 4px;
  margin: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-color);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.api-status-message.error {
  background-color: rgba(255, 99, 71, 0.1);
  border: 1px solid rgba(255, 99, 71, 0.3);
}

.api-status-message p {
  margin: 0;
  text-align: center;
}

.phone-input {
  flex: 1;
  padding: 0 1.2rem;
  border: none;
  font-size: 1rem;
  outline: none;
  border-top-right-radius: 12px;
  border-bottom-right-radius: 12px;
  color: var(--text-color);
  background-color: var(--card-background); /* Use theme background color */
  transition: all 0.3s ease;
  height: 100%;
  line-height: normal;
}

.phone-input::placeholder {
  color: var(--text-light);
  opacity: 0.7;
}

.error-message {
  color: var(--error-color);
  font-size: 0.9rem;
  margin-top: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: rgba(255, 82, 82, 0.1);
  border-radius: 8px;
  border-left: 3px solid var(--error-color);
}

.submit-button {
  padding: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 12px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(78, 205, 196, 0.3);
  position: relative;
  overflow: hidden;
}

.submit-button:hover:not(:disabled) {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(78, 205, 196, 0.4);
}

.submit-button:active:not(:disabled) {
  transform: translateY(1px);
  box-shadow: 0 2px 5px rgba(78, 205, 196, 0.2);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  box-shadow: none;
}

.test-note {
  font-size: 0.9rem;
  color: var(--text-light);
  text-align: center;
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: var(--highlight-color);
  border-radius: 12px;
  border: 1px dashed var(--border-color);
}

/* Prevent body scrolling when dropdown is open - added in style.css */

/* Responsive styles */
@media (max-width: 480px) {
  .phone-input-container {
    padding: 1rem;
  }

  .login-title {
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
  }

  .login-subtitle {
    font-size: 0.95rem;
    margin-bottom: 1.5rem;
  }

  .phone-input-wrapper {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }

  .submit-button {
    padding: 0.9rem;
    margin-top: 0.5rem;
  }

  .test-note {
    padding: 0.5rem;
    margin-top: 0.75rem;
    font-size: 0.85rem;
  }

  /* Mobile-specific styles for the dropdown */
  .phone-input-container.dropdown-active .main-content {
    transform: translateY(-45vh); /* Push content up when dropdown is active */
    transition: transform 0.3s ease-out;
  }

  .country-dropdown {
    width: 100%;
    left: 0;
    right: 0;
    bottom: 0;
    position: fixed;
    height: 45vh; /* Reduced height for mobile screens */
    max-height: 400px; /* Set a maximum height */
    border-radius: 16px 16px 0 0; /* Increased top radius for better appearance */
    border-bottom: none;
    animation: slideUp 0.3s ease-out;
    display: flex;
    flex-direction: column; /* Stack children vertically */
    box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.15);
    background-color: var(--card-background); /* Use theme background color */
    /* iOS-specific fixes */
    transform: translateZ(0); /* Force hardware acceleration */
    -webkit-backface-visibility: hidden; /* Prevent rendering issues */
    backface-visibility: hidden;
    z-index: 9999; /* Ensure it's above everything else */
    margin: 0; /* Reset any margins */
    top: auto; /* Ensure it sticks to bottom */
    max-width: 100vw; /* Ensure it doesn't overflow the viewport */
    overflow: hidden; /* Prevent content from overflowing */
  }

  @keyframes slideUp {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }

  /* Wrapper for the country list */
  .country-list-wrapper {
    flex: 1; /* Take remaining space */
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    max-height: 300px; /* Ensure wrapper has a max height for mobile view */
  }

  /* Make the country list take remaining space and scroll independently */
  .country-list {
    flex: 1;
    overflow-y: auto; /* Auto instead of scroll to only show scrollbar when needed */
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    overscroll-behavior: contain; /* Prevent scroll chaining */
    padding-bottom: env(safe-area-inset-bottom, 20px); /* Add padding for notched phones */
    margin: 0; /* Reset margin */
    position: relative; /* Establish stacking context */
    /* iOS-specific fixes */
    transform: translateZ(0); /* Force hardware acceleration */
    will-change: transform; /* Hint for browser optimization */
    min-height: 180px; /* Reduced minimum height for smaller mobile screens */
    -ms-overflow-style: -ms-autohiding-scrollbar; /* Better scrollbars on Edge */
    scrollbar-width: thin; /* Thin scrollbars on Firefox */
  }

  /* Extra space at the bottom of the list */
  .scroll-spacer {
    height: 30px; /* Reduced height for more compact view */
    flex-shrink: 0;
  }

  /* Style the search header for mobile */
  .country-search {
    flex-shrink: 0; /* Prevent shrinking */
    background-color: var(--highlight-color); /* Use theme highlight color */
    z-index: 10;
    padding: 0.75rem 1rem; /* Reduced vertical padding */
    border-bottom: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    border-radius: 16px 16px 0 0; /* Match the dropdown radius */
    box-shadow: 0 1px 3px rgba(0,0,0,0.05); /* Subtle shadow for depth */
  }

  .close-dropdown-button {
    display: block; /* Show close button on mobile */
    padding: 0.5rem;
    font-size: 1.2rem;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0,0,0,0.05);
    border-radius: 50%;
    color: var(--text-color); /* Use theme text color */
  }

  .search-header {
    margin-bottom: 0.5rem; /* Reduced margin */
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .search-title {
    font-size: 1rem; /* Slightly smaller font */
    font-weight: 600;
  }

  /* Make country items more touch-friendly but more compact */
  .country-item {
    padding: 0.75rem 1.25rem; /* Reduced vertical padding */
    min-height: 3rem; /* Slightly smaller touch targets */
    border-bottom: 1px solid rgba(0,0,0,0.05); /* Add subtle separator */
    display: flex;
    align-items: center;
  }

  /* Style for country items in mobile view */
  .country-item .country-flag {
    font-size: 1rem;
    font-weight: 600;
    min-width: 2rem; /* Fixed width for alignment */
    color: var(--primary-color); /* Ensure consistent color in dark mode */
  }

  .country-item .country-name {
    font-size: 1rem;
    flex: 1;
  }

  .country-item .country-code {
    font-size: 0.9rem;
    color: var(--text-light); /* Use theme light text color */
    margin-left: auto;
  }

  /* Add some extra padding at the end of the list for better scrolling */
  .country-list::after {
    content: "";
    display: block;
    height: 30px; /* Reduced extra space at the bottom */
  }
}
</style>
