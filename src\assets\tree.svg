<?xml version="1.0" encoding="UTF-8"?>
<svg width="120" height="180" viewBox="0 0 120 180" xmlns="http://www.w3.org/2000/svg">
  <!-- Tree trunk -->
  <rect x="50" y="110" width="20" height="70" fill="#8B4513"/>
  
  <!-- Tree foliage - multiple overlapping circles for a natural look -->
  <g>
    <!-- Bottom layer of leaves -->
    <circle cx="60" cy="100" r="30" fill="#2E7D32"/>
    <circle cx="40" cy="90" r="25" fill="#388E3C"/>
    <circle cx="80" cy="90" r="25" fill="#388E3C"/>
    
    <!-- Middle layer of leaves -->
    <circle cx="60" cy="70" r="28" fill="#43A047"/>
    <circle cx="35" cy="75" r="20" fill="#4CAF50"/>
    <circle cx="85" cy="75" r="20" fill="#4CAF50"/>
    
    <!-- Top layer of leaves -->
    <circle cx="60" cy="45" r="25" fill="#66BB6A"/>
    <circle cx="40" cy="55" r="18" fill="#81C784"/>
    <circle cx="80" cy="55" r="18" fill="#81C784"/>
    
    <!-- Highlight spots -->
    <circle cx="50" cy="40" r="8" fill="#A5D6A7"/>
    <circle cx="70" cy="35" r="6" fill="#A5D6A7"/>
  </g>
</svg>
