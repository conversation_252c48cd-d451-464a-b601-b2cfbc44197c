# Environment Setup for RVMPlus Rewards

This document explains how to set up and use different environments (development, staging, production) for the RVMPlus Rewards application.

## Environment Files

The application uses the following environment files:

| File | Purpose |
|------|---------|
| `.env` | Default environment variables |
| `.env.development` | Development environment variables |
| `.env.staging` | Staging environment variables |
| `.env.production` | Production environment variables |

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `VITE_API_BASE_URL` | Base URL for API requests | `http://localhost:5000` |
| `VITE_API_TIMEOUT` | API request timeout in milliseconds | `30000` |
| `VITE_USE_MOCK_DATA` | Whether to use mock data instead of real API calls | `true` |
| `VITE_APP_NAME` | Application name | `RVMPlus Rewards` |
| `VITE_APP_VERSION` | Application version | `1.0.0` |

## Environment-Specific Configurations

### Development (Local)

```
VITE_API_BASE_URL=http://localhost:5000
VITE_USE_MOCK_DATA=true
```

### Staging

```
VITE_API_BASE_URL=https://staging.rvmplus.com
VITE_USE_MOCK_DATA=false
```

### Production

```
VITE_API_BASE_URL=https://api.rvmplus.com
VITE_USE_MOCK_DATA=false
```

## Build Commands

The following build commands are available:

```bash
# Development build
npm run build

# Staging build
npm run build:staging

# Production build
npm run build:production
```

## Running in Different Environments

### Local Development

```bash
# Development environment (default)
npm run dev

# Staging environment
npm run dev -- --mode staging

# Production environment
npm run dev -- --mode production
```

### Vercel Deployment

The application is configured to use the appropriate environment based on the Vercel deployment:

- Preview deployments use the staging environment
- Production deployments use the production environment

This is configured in `vercel.json`:

```json
{
  "buildCommand": "npm run build:${VERCEL_ENV}"
}
```

## Debugging Environment Variables

In development mode, you can view the current environment configuration by double-tapping anywhere on the screen. This will display a debug overlay with the current environment information.

## API Endpoints

### Staging API

The staging API is available at:

```
https://staging.rvmplus.com
```

API documentation for the staging environment can be found at:

```
https://staging.rvmplus.com/Help
```

### Production API

The production API is available at:

```
https://api.rvmplus.com
```

## Adding New Environment Variables

1. Add the variable to all environment files (`.env`, `.env.development`, `.env.staging`, `.env.production`)
2. Prefix the variable with `VITE_` to make it accessible in the client-side code
3. Access the variable in code using `import.meta.env.VITE_YOUR_VARIABLE`

## Environment Utility

The application includes an environment utility at `src/utils/envConfig.js` that provides helper functions for working with environment variables:

```javascript
import { isDevelopment, isStaging, isProduction, API_CONFIG } from './utils/envConfig';

// Check current environment
if (isDevelopment()) {
  console.log('Running in development mode');
}

// Access API configuration
console.log('API Base URL:', API_CONFIG.BASE_URL);
console.log('Using Mock Data:', API_CONFIG.USE_MOCK_DATA);
```
