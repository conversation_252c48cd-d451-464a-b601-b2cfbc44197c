// Simple test script for profile update

// Configuration
const API_BASE_URL = 'https://staging.rvmplus.com';

// Test credentials
const TEST_PHONE = '60102431439';
const TEST_PASSWORD = '123456';

// Login and get token
async function login() {
  console.log('Logging in...');
  try {
    const response = await fetch(`${API_BASE_URL}/api/cmlogin/${TEST_PHONE}/${TEST_PASSWORD}/en`);
    const data = await response.json();

    if (Array.isArray(data) && data.length > 0) {
      console.log('Login successful!');
      return {
        acctNo: data[0].AcctNo,
        token: data[0].access_token
      };
    }

    console.error('Unexpected login response format:', data);
    return null;
  } catch (error) {
    console.error('Login error:', error);
    return null;
  }
}

// Get profile
async function getProfile(authData) {
  console.log('Getting profile...');
  try {
    const response = await fetch(`${API_BASE_URL}/api/GetProfile/${authData.acctNo}/Basic/en`, {
      headers: {
        'Authorization': authData.token
      }
    });

    const data = await response.json();
    console.log('Profile data:', JSON.stringify(data, null, 2));
    return data;
  } catch (error) {
    console.error('Get profile error:', error);
    return null;
  }
}

// Update profile
async function updateProfile(authData, profileData) {
  console.log('Updating profile...');
  try {
    // Format data for API
    const formattedData = {
      AcctNo: authData.acctNo,
      FullName: profileData.name,
      Email: profileData.email,
      Gender: profileData.gender,
      Street1: profileData.address?.street || '',
      Street2: profileData.address?.street2 || '',
      ZipCd: profileData.address?.zipCode || '',
      City: profileData.address?.city || '',
      StateCd: profileData.address?.state || '',
      Lang: profileData.language || 'en'
    };

    console.log('Update data:', JSON.stringify(formattedData, null, 2));

    const response = await fetch(`${API_BASE_URL}/api/UpdateProfile`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authData.token
      },
      body: JSON.stringify(formattedData)
    });

    // Get response status
    console.log('Update response status:', response.status);

    // Try to get response body
    try {
      // Check if response is JSON
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const data = await response.json();
        console.log('Update response:', JSON.stringify(data, null, 2));
        return data;
      } else {
        const text = await response.text();
        console.log('Update response (text):', text);
        return { success: response.ok, text };
      }
    } catch (error) {
      console.log('Error parsing response:', error.message);
      return { success: response.ok, message: 'Could not parse response' };
    }
  } catch (error) {
    console.error('Update profile error:', error);
    return null;
  }
}

// Main function
async function main() {
  // Login
  const authData = await login();
  if (!authData) {
    console.error('Login failed');
    return;
  }

  // Get profile
  const profileData = await getProfile(authData);
  if (!profileData) {
    console.error('Failed to get profile');
    return;
  }

  // Extract profile info
  const basicInfo = profileData.Table0[0];
  const detailedInfo = profileData.Table1 && profileData.Table1.length > 0 ? profileData.Table1[0] : {};

  // Create updated profile data
  const updatedProfile = {
    name: basicInfo.FullName,
    email: `test${Date.now()}@example.com`, // Change email to test update
    gender: detailedInfo.Gender || 'M',
    address: {
      street: detailedInfo.Street1 || '',
      street2: detailedInfo.Street2 || '',
      zipCode: detailedInfo.ZipCd || '',
      city: detailedInfo.City || '',
      state: detailedInfo.StateCd || ''
    },
    language: basicInfo.Lang || 'English'
  };

  // Update profile
  const updateResult = await updateProfile(authData, updatedProfile);

  // Get profile again to verify update
  if (updateResult) {
    console.log('Verifying update...');
    await getProfile(authData);
  }
}

// Run the test
main().catch(error => {
  console.error('Test failed:', error);
});
