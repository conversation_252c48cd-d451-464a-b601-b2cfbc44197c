<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useTheme } from '../store/theme';
import { useAuth } from '../store/auth';
import QRCodeVue from 'qrcode.vue';
import { QrcodeStream } from 'vue-qrcode-reader';
import { useI18n } from 'vue-i18n';

const { isDarkMode } = useTheme();
const { user } = useAuth();
const { t } = useI18n();

// User data for QR code
const userId = ref(user?.id || 'user123');
const qrValue = ref(`https://rvmplus.com/collect?userId=${userId.value}`);

// Tab state
const activeTab = ref('collection'); // 'collection' or 'payment'

// QR Scanner state
const scanResult = ref('');
const scanError = ref('');
const camera = ref('auto');
const hasCamera = ref(true);
const processingPayment = ref(false);

// Switch tabs
const switchTab = (tab) => {
  activeTab.value = tab;
  // Reset scanner states when switching tabs
  scanResult.value = '';
  scanError.value = '';
  processingPayment.value = false;
};

// Handle successful scan
const onDecode = (result) => {
  scanResult.value = result;
  processingPayment.value = true;

  // Here you would process the scanned QR code for payment
  // For example, make an API call to process the payment
  setTimeout(() => {
    // Simulate payment processing
    alert(`${t('qrcode.paymentSuccess')  }: ${result}`);
    processingPayment.value = false;
  }, 1500);
};

// Handle scan error
const onScanError = (error) => {
  scanError.value = error.message || t('qrcode.paymentFailed');
  console.error('QR scan error:', error);

  if (error.name === 'NotAllowedError') {
    scanError.value = t('qrcode.cameraAccessDenied', 'Camera access denied. Please allow camera access to scan QR codes.');
  } else if (error.name === 'NotFoundError') {
    scanError.value = t('qrcode.noCameraFound', 'No camera found on this device.');
    hasCamera.value = false;
  } else if (error.name === 'NotSupportedError') {
    scanError.value = t('qrcode.browserNotSupported', 'Your browser does not support camera access.');
    hasCamera.value = false;
  }
};

// Clean up when component is unmounted
onUnmounted(() => {
  // Any cleanup needed for camera
});
</script>

<template>
  <div class="qr-code-page">
    <!-- Collection Tab - QR Code Display -->
    <div v-if="activeTab === 'collection'" class="qr-content">
      <p class="description">
        {{ t('qrcode.description') }}
      </p>

      <!-- QR Code -->
      <div class="qr-code-container">
        <QRCodeVue
          :value="qrValue"
          :size="220"
          level="H"
          render-as="svg"
          :margin="0"
          :background="'transparent'"
          :foreground="'#ffffff'"
        />
      </div>

      <!-- Tab Buttons -->
      <div class="tab-buttons">
        <button
          class="tab-button"
          :class="{ active: activeTab === 'collection' }"
          @click="switchTab('collection')"
        >
          {{ t('qrcode.collection') }}
        </button>
        <button
          class="tab-button"
          :class="{ active: activeTab === 'payment' }"
          @click="switchTab('payment')"
        >
          {{ t('qrcode.payment') }}
        </button>
      </div>
    </div>

    <!-- Payment Tab - QR Scanner -->
    <div v-if="activeTab === 'payment'" class="scanner-content">
      <p class="description">
        {{ t('qrcode.scanDescription') }}
      </p>

      <!-- Scanner -->
      <div class="scanner">
        <div v-if="processingPayment" class="processing-overlay">
          <div class="spinner"></div>
          <p>{{ t('qrcode.processingPayment') }}</p>
        </div>

        <QrcodeStream
          v-if="hasCamera && !scanResult"
          :camera="camera"
          @decode="onDecode"
          @error="onScanError"
        />
        <div v-else-if="!hasCamera" class="scanner-placeholder">
          <p>{{ scanError || t('qrcode.scanningInstructions') }}</p>
        </div>
      </div>

      <div v-if="scanError && hasCamera && !scanResult" class="scan-error">
        {{ scanError }}
      </div>

      <div v-if="scanResult && !processingPayment" class="scan-result">
        <p>{{ t('qrcode.paymentSuccess') }}</p>
        <button class="scan-again-button" @click="switchTab('payment')">
          {{ t('qrcode.scanQrCode') }}
        </button>
      </div>

      <!-- Tab Buttons -->
      <div class="tab-buttons">
        <button
          class="tab-button"
          :class="{ active: activeTab === 'collection' }"
          @click="switchTab('collection')"
        >
          {{ t('qrcode.collection') }}
        </button>
        <button
          class="tab-button"
          :class="{ active: activeTab === 'payment' }"
          @click="switchTab('payment')"
        >
          {{ t('qrcode.payment') }}
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.qr-code-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 1rem;
  padding-top: env(safe-area-inset-top, 0px);
  padding-bottom: calc(5rem + env(safe-area-inset-bottom, 0px));
  background: linear-gradient(135deg, #26a69a 0%, #69d4a5 100%);
  color: white;
  overflow-y: auto;
  position: relative;
}

.qr-content, .scanner-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
  padding: 1rem;
  flex: 1;
}

/* Title removed - using the main Header.vue component */

.description {
  text-align: center;
  margin-bottom: 2rem;
  font-size: 1rem;
  line-height: 1.5;
  max-width: 90%;
}

.qr-code-container {
  background-color: transparent;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.tab-buttons {
  display: flex;
  width: 100%;
  max-width: 300px;
  margin: 0 0 2rem;
  border-radius: 30px;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.3);
  padding: 4px;
}

.tab-button {
  flex: 1;
  padding: 0.7rem 1rem;
  border: none;
  background-color: transparent;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
  border-radius: 30px;
  margin: 0 2px;
}

.tab-button.active {
  background-color: #26a69a;
}

.tab-button:not(.active) {
  background-color: transparent;
}

.scanner {
  width: 100%;
  max-width: 320px;
  height: 320px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  position: relative;
  overflow: hidden;
}

.scanner-placeholder {
  text-align: center;
  padding: 1rem;
}

.scan-error {
  color: #ff5252;
  margin-bottom: 1rem;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  max-width: 320px;
}

.scan-result {
  color: #4caf50;
  margin-bottom: 1rem;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.3);
  padding: 1rem;
  border-radius: 8px;
  font-weight: 500;
  width: 100%;
  max-width: 320px;
}

.scan-again-button {
  margin-top: 0.5rem;
  padding: 0.7rem 1.5rem;
  background-color: white;
  color: #26a69a;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.processing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.processing-overlay p {
  margin-top: 1rem;
  color: white;
  font-weight: 500;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Add a wave effect at the bottom */
.qr-code-page::before {
  content: '';
  position: absolute;
  bottom: 4.5rem;
  left: 0;
  width: 100%;
  height: 80px;
  background: white;
  border-radius: 100% 100% 0 0;
  opacity: 0.1;
  z-index: 1;
}

.qr-code-page::after {
  content: '';
  position: absolute;
  bottom: 4.5rem;
  left: -50%;
  width: 200%;
  height: 70px;
  background: white;
  border-radius: 100% 100% 0 0;
  opacity: 0.1;
  z-index: 1;
}

@media (max-width: 480px) {
  /* Title removed - using the main Header.vue component */

  .description {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
  }

  .qr-code-container {
    padding: 1rem;
  }

  .tab-buttons {
    max-width: 280px;
  }

  .tab-button {
    padding: 0.7rem 0.8rem;
    font-size: 0.9rem;
  }

  .scanner {
    height: 280px;
  }
}
</style>
