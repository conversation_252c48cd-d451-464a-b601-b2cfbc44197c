import { ref, computed } from 'vue';
import { useAuthService } from '../services/authService';

// Get the auth service
const {
  user: serviceUser,
  isLoggedIn: serviceIsLoggedIn,
  isLoading: serviceIsLoading,
  error: serviceError,
  login: serviceLogin,
  logout: serviceLogout,
  token: serviceToken,
  accountNumber: serviceAccountNumber,
} = useAuthService();

// Authentication state - use the service state
const user = serviceUser;
const isLoggedIn = serviceIsLoggedIn;
const isLoading = serviceIsLoading;
const error = serviceError;
const token = serviceToken;
const accountNumber = serviceAccountNumber;

// Login function - use the service login
const login = async (phone, password, lang = 'en') => {
  return serviceLogin(phone, password, lang);
};

// Logout function - use the service logout
const logout = () => {
  serviceLogout();
};

export const useAuth = () => {
  return {
    // State
    user,
    isLoggedIn,
    isLoading,
    error,
    token,
    accountNumber,

    // Methods
    login,
    logout,
  };
};
