<script setup>
import { ref, watch } from 'vue';
import { useTheme } from '../store/theme';
import { useI18n } from 'vue-i18n';

const { isDarkMode } = useTheme();
const { t } = useI18n();

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  message: {
    type: String,
    default: ''
  },
  confirmText: {
    type: String,
    default: ''
  },
  cancelText: {
    type: String,
    default: ''
  },
  isDanger: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['confirm', 'cancel']);

// Handle body class for preventing background scrolling
watch(() => props.show, (newValue) => {
  if (newValue) {
    document.body.classList.add('modal-open');
  } else {
    document.body.classList.remove('modal-open');
  }
});

// Handle confirm action
const handleConfirm = () => {
  emit('confirm');
};

// Handle cancel action
const handleCancel = () => {
  emit('cancel');
};
</script>

<template>
  <div v-if="show" class="modal-overlay" @click="handleCancel">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h2 class="modal-title">{{ title || t('common.confirmation', 'Confirmation') }}</h2>
      </div>
      <div class="modal-body">
        <p class="modal-message">{{ message }}</p>
      </div>
      <div class="modal-footer">
        <button class="cancel-button" @click="handleCancel">
          {{ cancelText || t('common.cancel', 'Cancel') }}
        </button>
        <button 
          class="confirm-button" 
          :class="{ 'danger-button': isDanger }"
          @click="handleConfirm"
        >
          {{ confirmText || t('common.confirm', 'Confirm') }}
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 1rem;
  animation: fadeIn 0.2s ease-out;
}

.modal-container {
  background-color: var(--card-background);
  border-radius: 12px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  animation: slideUp 0.3s ease-out;
  overflow: hidden;
}

.modal-header {
  padding: 1.25rem 1.5rem 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
}

.modal-body {
  padding: 1.5rem;
}

.modal-message {
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--text-color);
}

.modal-footer {
  padding: 1rem 1.5rem 1.5rem;
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

.cancel-button, .confirm-button {
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  border: none;
}

.cancel-button {
  background-color: var(--background-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.confirm-button {
  background-color: var(--primary-color);
  color: white;
}

.danger-button {
  background-color: #ff5252;
  color: white;
}

.cancel-button:hover {
  background-color: var(--border-color);
}

.confirm-button:hover {
  opacity: 0.9;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Add this to style.css or in a global style */
:global(.modal-open) {
  overflow: hidden;
}
</style>
