/**
 * Mock data for API services
 * Used for testing and development while waiting for the backend
 */

// Mock user data
export const mockUsers = [
  {
    id: 'user123',
    name: 'Demo User',
    phone: '9876543210',
    email: '<EMAIL>',
    gender: 'Male',
    address: {
      street: '123 Main St',
      city: 'Petaling Jaya',
      state: 'Selangor',
      zipCode: '47500',
      country: 'Malaysia'
    },
    points: 1500
  },
  {
    id: 'user456',
    name: 'Test User',
    phone: '1234567890',
    email: '<EMAIL>',
    gender: 'Female',
    address: {
      street: '456 Oak St',
      city: 'Kuala Lumpur',
      state: 'Kuala Lumpur',
      zipCode: '50000',
      country: 'Malaysia'
    },
    points: 2500
  }
];

// Mock vouchers
export const mockVouchers = [
  {
    id: 1,
    brand: 'BYC',
    title: 'BYC Free 1 Coffee',
    subtitle: 'Bring Your Cup Coffee',
    expiryDate: '2025-02-13',
    status: 'Active',
    type: 'FOOD',
    content: {
      introText: 'Enjoy a free coffee at any BYC outlet.',
      terms: [
        'Valid at all BYC outlets',
        'One redemption per user',
        'Valid until 13 Feb 2025'
      ]
    },
    bannerImage: 'https://example.com/voucher-banner.jpg'
  },
  {
    id: 2,
    brand: 'BYC',
    title: 'BYC Free 1 Coffee',
    subtitle: 'Bring Your Cup Coffee',
    expiryDate: '2025-02-13',
    status: 'Used',
    type: 'FOOD',
    content: {
      introText: 'Enjoy a free coffee at any BYC outlet.',
      terms: [
        'Valid at all BYC outlets',
        'One redemption per user',
        'Valid until 13 Feb 2025'
      ]
    },
    bannerImage: 'https://example.com/voucher-banner.jpg'
  },
  {
    id: 3,
    brand: 'Green Earth',
    title: '10% Off Eco Products',
    subtitle: 'Green Earth Eco Store',
    expiryDate: '2025-03-15',
    status: 'Active',
    type: 'RETAIL',
    content: {
      introText: 'Get 10% off on all eco-friendly products.',
      terms: [
        'Valid at all Green Earth stores',
        'Minimum purchase of RM50',
        'Valid until 15 Mar 2025'
      ]
    },
    bannerImage: 'https://example.com/green-earth-banner.jpg'
  }
];

// Mock transactions
export const mockTransactions = [
  {
    id: 1,
    type: 'VM Order',
    amount: 150.0,
    date: '2023-09-19',
    time: '15:15:00',
    category: 'Spent',
    description: 'Purchase of coffee from vending machine',
    location: 'MBMB Recycling Center',
    reference: 'VM-12345',
    items: [
      {
        name: 'Coffee',
        quantity: 1,
        price: 150.0
      }
    ]
  },
  {
    id: 2,
    type: 'RVM+ Points',
    amount: 11.0,
    date: '2023-09-19',
    time: '15:13:00',
    category: 'Collected',
    description: 'Points earned from recycling',
    location: 'MBMB Recycling Center',
    reference: 'RVM-67890',
    items: [
      {
        name: 'Plastic Bottle',
        quantity: 5,
        points: 5.0
      },
      {
        name: 'Aluminum Can',
        quantity: 6,
        points: 6.0
      }
    ]
  },
  {
    id: 3,
    type: 'Voucher Redemption',
    amount: 50.0,
    date: '2023-09-18',
    time: '10:30:00',
    category: 'Spent',
    description: 'Redemption of BYC coffee voucher',
    location: 'BYC Outlet - Petaling Jaya',
    reference: 'VCH-54321',
    items: [
      {
        name: 'BYC Coffee Voucher',
        quantity: 1,
        price: 50.0
      }
    ]
  }
];

// Mock movement challenges
export const mockChallenges = [
  {
    id: 1,
    title: 'Daily 10,000 steps',
    description: 'Keep active with our daily strides goal.',
    reward: 150,
    goal: 10000,
    unit: 'steps'
  },
  {
    id: 2,
    title: '15 minutes walk',
    description: 'Take a break and get 1,500 steps in!',
    reward: 50,
    goal: 1500,
    unit: 'steps'
  },
  {
    id: 3,
    title: 'Weekend Warrior',
    description: 'Complete 20,000 steps over the weekend.',
    reward: 300,
    goal: 20000,
    unit: 'steps'
  }
];

// Mock active challenge
export const mockActiveChallenge = {
  challengeId: 1,
  startTime: '2023-09-19T15:30:00Z',
  endTime: '2023-09-20T15:30:00Z',
  goal: 10000,
  progress: 2500,
  unit: 'steps',
  completed: false,
  timeLeft: '23:45:30'
};

// Mock forest stats
export const mockForestStats = {
  currentStage: 3,
  co2Absorbed: 3000,
  co2Target: 5000,
  totalCO2Mitigated: 4000,
  totalItemsRecycled: 357,
  totalTreesGrown: 3,
  currentTreeStage: {
    id: 3,
    name: 'Young Tree',
    co2Required: 5000
  },
  progressPercentage: 60
};

// Mock movement stats
export const mockMovementStats = {
  stepsWalked: 5000,
  pointsEarned: 50,
  challengesCompleted: 2
};

// Helper function to simulate API delay
export const simulateApiDelay = (min = 300, max = 800) => {
  const delay = Math.floor(Math.random() * (max - min + 1)) + min;
  return new Promise(resolve => setTimeout(resolve, delay));
};

// Helper function to create API response format
export const createApiResponse = (data, message = '') => {
  return {
    success: true,
    message,
    data
  };
};

// Helper function to create API error response
export const createApiErrorResponse = (code, message) => {
  return {
    success: false,
    message,
    error: {
      code
    }
  };
};

export default {
  mockUsers,
  mockVouchers,
  mockTransactions,
  mockChallenges,
  mockActiveChallenge,
  mockForestStats,
  mockMovementStats,
  simulateApiDelay,
  createApiResponse,
  createApiErrorResponse
};
