<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample Pages for In-App Browser</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .card {
            background: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        a {
            color: #3498db;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #121212;
                color: #eee;
            }
            h1 {
                color: #e0e0e0;
                border-bottom-color: #333;
            }
            .card {
                background-color: #1e1e1e;
                box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            }
            a {
                color: #64b5f6;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Sample Pages for In-App Browser</h1>
        
        <div class="card">
            <h2>Available Pages</h2>
            <ul>
                <li><a href="help-support.html">Help & Support</a></li>
                <li><a href="contact-us.html">Contact Us</a></li>
                <li><a href="privacy-policy.html">Privacy Policy</a></li>
            </ul>
        </div>
        
        <div class="card">
            <h2>About These Pages</h2>
            <p>These sample pages are designed to be used with the in-app browser feature of the RVMPlus Rewards app. They demonstrate how external content can be displayed within the app using iframes.</p>
            <p>The pages include responsive design and dark mode support to match the app's theme.</p>
        </div>
    </div>
</body>
</html>
