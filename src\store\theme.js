import { ref, watch } from 'vue';

// Theme state
const isDarkMode = ref(true);

// Initialize theme from localStorage if available
if (typeof window !== 'undefined') {
  const savedTheme = localStorage.getItem('theme');
  isDarkMode.value = savedTheme === 'dark';

  // Apply theme class to document
  updateThemeClass();
}

// Watch for theme changes and update localStorage and document class
watch(isDarkMode, () => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('theme', isDarkMode.value ? 'dark' : 'light');
    updateThemeClass();
  }
});

// Update document class based on theme
function updateThemeClass() {
  if (typeof document !== 'undefined') {
    if (isDarkMode.value) {
      document.documentElement.classList.add('dark-theme');
    } else {
      document.documentElement.classList.remove('dark-theme');
    }
  }
}

// Toggle theme function
function toggleTheme() {
  isDarkMode.value = !isDarkMode.value;
}

export const useTheme = () => {
  return {
    isDarkMode,
    toggleTheme,
  };
};
