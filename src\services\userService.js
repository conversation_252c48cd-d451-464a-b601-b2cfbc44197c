/**
 * User service for managing user profile data
 */
import { ref } from 'vue';
import httpClient from '../utils/httpClient';
import { simulateApiDelay, createApiResponse, createApiErrorResponse } from './mockData';
import { useAuthService } from './authService';
import { useBitmapService } from './bitmapService';

// Configuration
const USE_MOCK_DATA = false; // Using real API as requested by the user

// User state
const profile = ref(null);
const isLoading = ref(false);
const error = ref(null);

// Get auth service
const { user, isLoggedIn } = useAuthService();

/**
 * Get user profile
 * @param {boolean} forceRefresh - Force a refresh of the data even if cache is valid
 * @returns {Promise<Object>} User profile data
 */
const getUserProfile = async (forceRefresh = false) => {
  // If we already have profile data and we're not forcing a refresh, return it
  if (profile.value && !forceRefresh) {
    return createApiResponse(profile.value);
  }

  isLoading.value = true;
  error.value = null;

  try {
    if (!isLoggedIn.value) {
      throw new Error('User not authenticated');
    }

    if (USE_MOCK_DATA) {
      // Mock implementation
      await simulateApiDelay();

      // Use the current user from auth service
      profile.value = { ...user.value };

      return createApiResponse(profile.value);
    } else {
      // Use bitmapService to fetch profile with bitmap 2
      const { accountNumber, token } = useAuthService();
      const bitmapService = useBitmapService();
      const params = {
        Token: token.value,
        bitmap: 6,
        Func: '0',
        Ref01: accountNumber.value,
        Ref02: '',
        Lang: 'en',
      };
      const result = await bitmapService.sendBitmapRequest(params);
      const data = result?.data;
      if (!data || !Array.isArray(data) || data.length === 0) {
        throw new Error('No profile data returned');
      }
      // Extract all tables Table0-Table4 if present
      const tables = {};
      for (let i = 0; i <= 4; i++) {
        const tableKey = `Table${i}`;
        if (data[0][tableKey]) {
          tables[tableKey] = data[0][tableKey];
        }
      }
      // Merge data from all tables into a single profile object (customize as needed)
      const basicInfo = tables.Table0?.[0] || {};
      const detailedInfo = tables.Table2?.[0] || tables.Table1?.[0] || {};
      console.log('basicInfo', basicInfo);
      console.log('detailedInfo', detailedInfo);
      // You can further merge Table2, Table3, Table4 as needed
      profile.value = {
        id: accountNumber.value,
        name: basicInfo.FullName || detailedInfo.FullName || '',
        phone: basicInfo.Contact || detailedInfo.MobileNo || '',
        email: basicInfo.Email || detailedInfo.Email || '',
        gender: basicInfo.Gender || detailedInfo.Gender || '',
        dob: basicInfo.DOB || detailedInfo.DOB || '',
        address: {
          street: basicInfo.Street1 || detailedInfo.Street1 || '',
          street2: basicInfo.Street2 || detailedInfo.Street2 || '',
          street3: basicInfo.Street3 || detailedInfo.Street3 || '',
          city: basicInfo.City || detailedInfo.City || '',
          zipCode: basicInfo.ZipCd || detailedInfo.ZipCd || '',
          state: basicInfo.StateCd || detailedInfo.StateCd || '',
          country: basicInfo.CtryCd || detailedInfo.CtryCd || '',
        },
        profileImage: basicInfo.ProfileImg || detailedInfo.ProfileImg || '',
        language: basicInfo.Lang || detailedInfo.Lang || 'English',
        userId: basicInfo.UserId || detailedInfo.UserId || '',
        // Add all tables for advanced use
        tables,
      };
      // Update user in auth service to ensure consistency
      user.value = { ...profile.value };
      localStorage.setItem('user', JSON.stringify(user.value));
      return createApiResponse(profile.value);
    }
  } catch (err) {
    error.value = err.message;
    return createApiErrorResponse('PROFILE_NOT_FOUND', err.message);
  } finally {
    isLoading.value = false;
  }
};

/**
 * Compare two objects to check if they have any differences
 * @param {Object} obj1 - First object
 * @param {Object} obj2 - Second object
 * @returns {boolean} True if objects are different, false if they are the same
 */
const hasChanges = (obj1, obj2) => {
  // Handle null or undefined
  if (!obj1 && !obj2) {return false;}
  if (!obj1 || !obj2) {return true;}

  // Compare simple objects
  if (typeof obj1 !== 'object' || typeof obj2 !== 'object') {
    return obj1 !== obj2;
  }

  // For nested objects like address
  for (const key in obj1) {
    // Skip functions and inherited properties
    if (typeof obj1[key] === 'function' || !Object.prototype.hasOwnProperty.call(obj1, key)) {
      continue;
    }

    // Handle nested objects (like address)
    if (typeof obj1[key] === 'object' && obj1[key] !== null) {
      if (typeof obj2[key] !== 'object' || obj2[key] === null) {
        return true;
      }
      if (hasChanges(obj1[key], obj2[key])) {
        return true;
      }
    } else if (obj1[key] !== obj2[key]) {
      return true;
    }
  }

  // Check for properties in obj2 that aren't in obj1
  for (const key in obj2) {
    if (typeof obj2[key] !== 'function' &&
        Object.prototype.hasOwnProperty.call(obj2, key) &&
        !Object.prototype.hasOwnProperty.call(obj1, key)) {
      return true;
    }
  }

  return false;
};

/**
 * Update user profile
 * @param {Object} profileData - Updated profile data
 * @returns {Promise<Object>} Updated user profile
 */
const updateUserProfile = async (profileData) => {
  isLoading.value = true;
  error.value = null;

  try {
    if (!isLoggedIn.value) {
      throw new Error('User not authenticated');
    }

    // Validate profile data
    if (!profileData) {
      throw new Error('Profile data is required');
    }

    if (USE_MOCK_DATA) {
      // Mock implementation
      await simulateApiDelay();

      // Update profile
      profile.value = {
        ...user.value,
        ...profileData,
        // Preserve id and phone
        id: user.value.id,
        phone: user.value.phone,
      };

      // Update user in auth service
      user.value = { ...profile.value };

      // Update localStorage
      localStorage.setItem('user', JSON.stringify(user.value));

      return createApiResponse(profile.value, 'Profile updated successfully');
    } else {
      // Real API implementation
      const { accountNumber, token } = useAuthService();

      // Format the data according to the API requirements
      const formattedData = {
        Token: token.value,
        Func: 'Update',
        AcctNo: accountNumber.value,
        FullName: profileData.name || profile.value?.name,
        EmailAddr: profileData.email || profile.value?.email,
        gender: profileData.gender || profile.value?.gender,
        ContactNo: profileData.phone || profile.value?.phone,
        Street1: profileData.address?.street || profile.value?.address?.street,
        Street2: profileData.address?.street2 || profile.value?.address?.street2,
        Street3: profileData.address?.street3 || profile.value?.address?.street3 || '',
        StateCd: profileData.address?.state || profile.value?.address?.state || 'KUL',
        City: profileData.address?.city || profile.value?.address?.city || 'Kuala Lumpur',
        ZipCd: profileData.address?.zipCode || profile.value?.address?.zipCode || '50000',
        CtryCd: profileData.address?.country || profile.value?.address?.country || 'MY',
        UserId: '',
        DOB: profileData.dob || profile.value?.dob,
        Lang: profileData.language || profile.value?.language || 'en',
      };

      // Check if there are any actual changes to the profile
      const currentProfileData = {
        name: profile.value?.name,
        email: profile.value?.email,
        gender: profile.value?.gender,
        phone: profile.value?.phone,
        address: {
          street: profile.value?.address?.street,
          street2: profile.value?.address?.street2,
          street3: profile.value?.address?.street3,
          state: profile.value?.address?.state,
          city: profile.value?.address?.city,
          zipCode: profile.value?.address?.zipCode,
          country: profile.value?.address?.country,
        },
        dob: profile.value?.dob,
        language: profile.value?.language,
      };

      const newProfileData = {
        name: profileData.name || profile.value?.name,
        email: profileData.email || profile.value?.email,
        gender: profileData.gender || profile.value?.gender,
        phone: profileData.phone || profile.value?.phone,
        address: {
          street: profileData.address?.street || profile.value?.address?.street,
          street2: profileData.address?.street2 || profile.value?.address?.street2,
          street3: profileData.address?.street3 || profile.value?.address?.street3 || '',
          state: profileData.address?.state || profile.value?.address?.state || 'KUL',
          city: profileData.address?.city || profile.value?.address?.city || 'Kuala Lumpur',
          zipCode: profileData.address?.zipCode || profile.value?.address?.zipCode || '50000',
          country: profileData.address?.country || profile.value?.address?.country || 'MY',
        },
        dob: profileData.dob || profile.value?.dob,
        language: profileData.language || profile.value?.language || 'en',
      };

      // Check if there are any changes
      if (profile.value && !hasChanges(currentProfileData, newProfileData)) {
        // console.log('No changes detected in profile data, skipping API call');
        return createApiResponse(profile.value, 'No changes detected');
      }

      // console.log('Changes detected in profile data, making API call');
      // console.log('Updating profile with data:', formattedData);

      // Use the correct API endpoint based on our testing
      // Note: The API might not use Bearer token format, so we'll try without the Bearer prefix
      const response = await httpClient.post('api/UpdateProfile', formattedData, {
        headers: {
          'Authorization': token.value,
        },
      });

      // console.log('Profile update response:', response);

      // Check if update was successful
      if (response) {
        // Refresh profile data
        await getUserProfile();

        return createApiResponse(profile.value, 'Profile updated successfully');
      }

      throw new Error(response?.message || 'Failed to update profile');
    }
  } catch (err) {
    // console.error('Error updating profile:', err);
    error.value = err.message;
    return createApiErrorResponse('PROFILE_UPDATE_FAILED', err.message);
  } finally {
    isLoading.value = false;
  }
};

/**
 * Update user profile picture
 * @param {Object} imageFile - The image file to upload
 * @returns {Promise<Object>} Updated user profile
 */
const updateProfilePicture = async (imageFile) => {
  isLoading.value = true;
  error.value = null;

  try {
    if (!isLoggedIn.value) {
      throw new Error('User not authenticated');
    }

    // Validate image file
    if (!imageFile) {
      throw new Error('Valid image file is required');
    }

    // Check if the file is actually an image
    if (!imageFile.type.startsWith('image/')) {
      throw new Error('File must be an image');
    }

    // Check file size (max 5MB)
    const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    if (imageFile.size > MAX_FILE_SIZE) {
      throw new Error('Image file is too large (max 5MB)');
    }

    if (USE_MOCK_DATA) {
      // Mock implementation
      await simulateApiDelay();

      // Create a fake image URL
      const fakeImageUrl = URL.createObjectURL(imageFile);

      // Update profile
      profile.value = {
        ...profile.value,
        profileImage: fakeImageUrl,
      };

      // Update user in auth service
      user.value = { ...profile.value };

      // Update localStorage
      localStorage.setItem('user', JSON.stringify(user.value));

      return createApiResponse(profile.value, 'Profile picture updated successfully');
    } else {
      // Real API implementation
      const { accountNumber, token } = useAuthService();

      // Convert image to base64
      const reader = new FileReader();
      const base64Promise = new Promise((resolve, reject) => {
        reader.onload = () => resolve(reader.result.split(',')[1]);
        reader.onerror = reject;
      });
      reader.readAsDataURL(imageFile);
      const base64Image = await base64Promise;

      // Prepare request data according to API documentation
      const requestData = {
        Token: token.value,
        Func: 'Update',
        AcctNo: accountNumber.value,
        ImgName: imageFile.name,
        Img: base64Image,
        UserId: user.value?.userId || '',
        Lang: profile.value?.language || 'en',
      };

      // Make API call
      const response = await httpClient.post('api/UpdateProfile/Picture', requestData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token.value,
        },
      });

      // Check if update was successful
      if (response) {
        // Refresh profile data
        await getUserProfile();
        return createApiResponse(profile.value, 'Profile picture updated successfully');
      }

      throw new Error(response?.message || 'Failed to update profile picture');
    }
  } catch (err) {
    error.value = err.message;
    return createApiErrorResponse('PROFILE_PICTURE_UPDATE_FAILED', err.message);
  } finally {
    isLoading.value = false;
  }
};

/**
 * Reset error state
 */
const resetError = () => {
  error.value = null;
};

/**
 * Clear the profile cache
 * This will force a refresh on the next call to getUserProfile
 */
const clearCache = () => {
  profile.value = null;
};

// Export user service
export const useUserService = () => {
  return {
    // State
    profile,
    isLoading,
    error,

    // Methods
    getUserProfile,
    updateUserProfile,
    updateProfilePicture,
    resetError,
    clearCache,
  };
};
