import { test, expect } from '@playwright/test';

test.describe('Country Selector', () => {
  test.beforeEach(async ({ page }) => {
    // Go to the login page before each test
    await page.goto('/login');

    // Wait for country selector to be loaded
    await page.waitForSelector('.country-selector');
  });

  test('should display country selector on phone input screen', async ({ page }) => {
    // Check that the country selector is visible
    await expect(page.locator('.country-selector')).toBeVisible();

    // Check that the country flag and code are displayed correctly
    await expect(page.locator('.selected-country .country-flag')).toBeVisible();
    await expect(page.locator('.selected-country .country-code')).toBeVisible();

    // Verify the format of the country code (should start with +)
    const countryCodeText = await page.locator('.selected-country .country-code').textContent();
    expect(countryCodeText.startsWith('+')).toBeTruthy();
  });

  test('should open country dropdown when clicking on selector', async ({ page }) => {
    // Click on country selector
    await page.click('.country-selector');

    // Check that the dropdown is visible
    await expect(page.locator('.country-dropdown')).toBeVisible();

    // Check that the search input is visible
    await expect(page.locator('#country-search')).toBeVisible();

    // Wait for animation to be applied
    await page.waitForTimeout(100);

    // Check that the dropdown contains countries
    await page.waitForSelector('.country-item');
    const countryItems = await page.locator('.country-item').count();
    expect(countryItems).toBeGreaterThan(0);
  });

  test('should filter countries when searching', async ({ page }) => {
    // Click on country selector to open dropdown
    await page.click('.country-selector');

    // Wait for dropdown to be visible and countries to load
    await page.waitForSelector('.country-dropdown');
    await page.waitForSelector('.country-item');

    // Get initial count of countries
    const initialCount = await page.locator('.country-item').count();
    expect(initialCount).toBeGreaterThan(0);

    // Enter search term that should match at least one country
    await page.fill('#country-search', 'united');

    // Wait for filtering to complete
    await page.waitForTimeout(500);

    // Get filtered count of countries
    const filteredCount = await page.locator('.country-item').count();

    // There should be at least one result (United States or United Kingdom)
    expect(filteredCount).toBeGreaterThan(0);
    expect(filteredCount).toBeLessThan(initialCount);

    // Check that filtered countries contain the search term
    const countryNames = await page.locator('.country-name').allTextContents();
    let foundMatch = false;
    for (const name of countryNames) {
      if (name.toLowerCase().includes('united')) {
        foundMatch = true;
        break;
      }
    }
    expect(foundMatch).toBeTruthy();
  });

  test('should select a country when clicking on it', async ({ page }) => {
    // Get the initial country code
    const initialCountryCode = await page.locator('.selected-country .country-code').textContent();

    // Click on country selector to open dropdown
    await page.click('.country-selector');

    // Wait for dropdown to be visible and countries to load
    await page.waitForSelector('.country-dropdown');
    await page.waitForSelector('.country-item');

    // Find a country that's different from the current one
    const countryItems = page.locator('.country-item');
    const count = await countryItems.count();
    expect(count).toBeGreaterThan(1);

    // Get the second country (to ensure it's different from the default)
    const secondCountry = countryItems.nth(1);
    const secondCountryCode = await secondCountry.locator('.country-code').textContent();

    // Click on the second country
    await secondCountry.click();

    // Wait a moment for the dropdown to close
    await page.waitForTimeout(300);

    // Dropdown should be closed
    await expect(page.locator('.country-dropdown')).not.toBeVisible();

    // Selected country should be displayed in the selector
    const newCountryCode = await page.locator('.selected-country .country-code').textContent();
    expect(newCountryCode).not.toBe(initialCountryCode);
  });

  test('should proceed with login with country code', async ({ page }) => {
    // Select a specific country
    await page.click('.country-selector');
    await page.waitForSelector('.country-dropdown');
    await page.waitForSelector('.country-item');

    // Find and select a country (first one in the list)
    const firstCountry = page.locator('.country-item').first();
    await firstCountry.click();

    // Wait for dropdown to close
    await page.waitForTimeout(300);

    // Get the selected country code
    const selectedCountryCode = await page.locator('.selected-country .country-code').textContent();

    // Enter valid phone number and password
    await page.fill('#phone', '1234567890');
    await page.fill('#password', '123456');

    // Check that the login button is enabled
    const loginButton = page.locator('button.submit-button');
    await expect(loginButton).toBeEnabled();
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set viewport to mobile size
    await page.setViewportSize({ width: 375, height: 667 });

    // Check that the phone input wrapper has appropriate size
    const inputWrapper = await page.locator('.phone-input-wrapper').boundingBox();
    expect(inputWrapper.width).toBeGreaterThan(300);
    expect(inputWrapper.height).toBeLessThanOrEqual(50);

    // Click on country selector to open dropdown
    await page.click('.country-selector');

    // Wait for dropdown to be visible
    await page.waitForSelector('.country-dropdown');

    // Check that the dropdown is properly styled for mobile
    const dropdownBoundingBox = await page.locator('.country-dropdown').boundingBox();

    // On mobile, the dropdown should be full width
    expect(dropdownBoundingBox?.width).toBeGreaterThan(350);

    // Wait for animation to be applied
    await page.waitForTimeout(100);

    // Check that the dropdown contains countries on mobile
    await page.waitForSelector('.country-item');
    const countryItemsMobile = await page.locator('.country-item').count();
    expect(countryItemsMobile).toBeGreaterThan(0);
  });

  test('should have proper styling for country items', async ({ page }) => {
    // Click on country selector to open dropdown
    await page.click('.country-selector');

    // Wait for dropdown to be visible and countries to load
    await page.waitForSelector('.country-dropdown');
    await page.waitForSelector('.country-item');

    // Check styling of country items
    const firstCountryItem = page.locator('.country-item').first();

    // Check that the country item has proper structure
    await expect(firstCountryItem.locator('.country-flag')).toBeVisible();
    await expect(firstCountryItem.locator('.country-name')).toBeVisible();
    await expect(firstCountryItem.locator('.country-code')).toBeVisible();

    // Hover over the country item to check hover effect
    await firstCountryItem.hover();

    // Wait for hover effect to be applied
    await page.waitForTimeout(100);

    // Check that the country item is visible and interactive
    await expect(firstCountryItem).toBeVisible();

    // Check that clicking on the country item works
    await firstCountryItem.click();

    // Dropdown should be closed after selection
    await page.waitForTimeout(300);
    await expect(page.locator('.country-dropdown')).not.toBeVisible();
  });
});
