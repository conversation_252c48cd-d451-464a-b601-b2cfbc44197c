import { test, expect } from '@playwright/test';

/**
 * Final test script for mobile and desktop views
 * Focuses on responsive design aspects that work reliably
 */
test.describe('Mobile and Desktop View Tests', () => {
  // Define viewport sizes for testing
  const viewports = {
    mobileSm: { width: 320, height: 568 },  // iPhone 5/SE (smallest supported)
    mobileMd: { width: 375, height: 667 },  // iPhone 6/7/8
    mobileLg: { width: 414, height: 896 },  // iPhone 11 Pro Max
    tablet: { width: 768, height: 1024 },   // iPad
    desktop: { width: 1280, height: 800 },  // Standard desktop
    desktopLg: { width: 1920, height: 1080 } // Large desktop
  };

  // Test app container responsiveness
  test('App container should adapt to different screen sizes', async ({ page }) => {
    await page.goto('/login');
    
    // Test on different viewports
    const viewportSizes = [
      viewports.mobileSm,
      viewports.mobileMd,
      viewports.tablet,
      viewports.desktop
    ];
    
    const containerWidths = {};
    
    for (const [index, viewport] of viewportSizes.entries()) {
      // Set viewport size
      await page.setViewportSize(viewport);
      
      // Take screenshot
      await page.screenshot({ path: `app-container-${index}.png` });
      
      // Get container width
      const width = await page.evaluate(() => {
        // Use first app container to avoid ambiguity
        const app = document.querySelector('#app');
        return app ? window.getComputedStyle(app).width : 'not found';
      });
      
      containerWidths[`${viewport.width}x${viewport.height}`] = width;
    }
    
    console.log('App container widths:');
    console.log(JSON.stringify(containerWidths, null, 2));
    
    // Verify that container widths adapt to viewport size
    const mobileWidth = containerWidths[`${viewports.mobileMd.width}x${viewports.mobileMd.height}`];
    const desktopWidth = containerWidths[`${viewports.desktop.width}x${viewports.desktop.height}`];
    
    // Either widths are different or they maintain proper proportions
    if (mobileWidth && desktopWidth) {
      const mobileNumWidth = parseFloat(mobileWidth);
      const desktopNumWidth = parseFloat(desktopWidth);
      
      if (!isNaN(mobileNumWidth) && !isNaN(desktopNumWidth)) {
        // Check if mobile is full width and desktop is constrained
        const mobileRatio = mobileNumWidth / viewports.mobileMd.width;
        const desktopRatio = desktopNumWidth / viewports.desktop.width;
        
        console.log(`Mobile ratio: ${mobileRatio}, Desktop ratio: ${desktopRatio}`);
        
        // Mobile should be close to full width
        expect(mobileRatio).toBeGreaterThan(0.95);
        
        // Desktop should be either constrained or full width
        if (desktopRatio < 0.95) {
          console.log('Desktop has constrained width (responsive)');
        } else {
          console.log('Desktop has full width');
        }
      }
    }
  });

  // Test country selector dropdown responsiveness
  test('Country selector dropdown should adapt to different screen sizes', async ({ page }) => {
    await page.goto('/login');
    
    // Test on mobile
    await page.setViewportSize(viewports.mobileMd);
    
    // Click on country selector to open dropdown
    await page.click('.country-selector');
    
    // Wait for dropdown to be visible
    await page.waitForSelector('.country-dropdown', { state: 'visible' });
    
    // Take screenshot with dropdown open on mobile
    await page.screenshot({ path: 'country-dropdown-mobile.png' });
    
    // Check dropdown width on mobile
    const dropdownWidthMobile = await page.evaluate(() => {
      const dropdown = document.querySelector('.country-dropdown');
      return dropdown ? window.getComputedStyle(dropdown).width : 'not found';
    });
    console.log(`Country dropdown width on mobile: ${dropdownWidthMobile}`);
    
    // Close dropdown by clicking outside
    await page.click('.login-title');
    
    // Test on desktop
    await page.setViewportSize(viewports.desktop);
    
    // Click on country selector to open dropdown
    await page.click('.country-selector');
    
    // Wait for dropdown to be visible
    await page.waitForSelector('.country-dropdown', { state: 'visible' });
    
    // Take screenshot with dropdown open on desktop
    await page.screenshot({ path: 'country-dropdown-desktop.png' });
    
    // Check dropdown width on desktop
    const dropdownWidthDesktop = await page.evaluate(() => {
      const dropdown = document.querySelector('.country-dropdown');
      return dropdown ? window.getComputedStyle(dropdown).width : 'not found';
    });
    console.log(`Country dropdown width on desktop: ${dropdownWidthDesktop}`);
  });

  // Test CSS media queries
  test('CSS should include responsive media queries', async ({ page }) => {
    await page.goto('/login');
    
    // Check for media queries in the CSS
    const mediaQueries = await page.evaluate(() => {
      const queries = [];
      for (const sheet of document.styleSheets) {
        try {
          for (const rule of sheet.cssRules) {
            if (rule instanceof CSSMediaRule) {
              queries.push({
                query: rule.conditionText,
                selectors: Array.from(rule.cssRules).map(r => r.selectorText).filter(Boolean)
              });
            }
          }
        } catch (e) {
          // Skip cross-origin stylesheets
        }
      }
      return queries;
    });
    
    console.log('Media queries:');
    console.log(JSON.stringify(mediaQueries.slice(0, 10), null, 2)); // Show first 10 queries
    
    // Check for common responsive breakpoints
    const hasCommonBreakpoints = mediaQueries.some(query => 
      query.query.includes('max-width: 480px') || 
      query.query.includes('max-width: 768px') ||
      query.query.includes('min-width: 769px')
    );
    
    console.log(`Has common responsive breakpoints: ${hasCommonBreakpoints}`);
    
    // Check for safe area insets for notched phones
    const hasSafeAreaInsets = await page.evaluate(() => {
      for (const sheet of document.styleSheets) {
        try {
          for (const rule of sheet.cssRules) {
            if (rule.cssText && rule.cssText.includes('env(safe-area-inset')) {
              return true;
            }
          }
        } catch (e) {
          // Skip cross-origin stylesheets
        }
      }
      return false;
    });
    
    console.log(`Has safe area insets: ${hasSafeAreaInsets}`);
    
    // Verify that responsive CSS exists
    expect(hasCommonBreakpoints).toBeTruthy();
    expect(hasSafeAreaInsets).toBeTruthy();
  });

  // Test bottom navigation CSS
  test('Bottom navigation should have responsive CSS', async ({ page }) => {
    await page.goto('/login');
    
    // Extract CSS related to bottom navigation
    const bottomNavCSS = await page.evaluate(() => {
      // Function to extract CSS rules for a specific selector
      function getStylesForSelector(selector) {
        const styles = [];
        for (const sheet of document.styleSheets) {
          try {
            for (const rule of sheet.cssRules) {
              if (rule.selectorText && rule.selectorText.includes(selector)) {
                styles.push({
                  selector: rule.selectorText,
                  css: rule.cssText
                });
              }
            }
          } catch (e) {
            // Skip cross-origin stylesheets
          }
        }
        return styles;
      }
      
      // Get styles for bottom navigation
      return getStylesForSelector('.bottom-nav');
    });
    
    console.log('Bottom Navigation CSS:');
    console.log(JSON.stringify(bottomNavCSS, null, 2));
    
    // Check for media queries specific to bottom navigation
    const bottomNavMediaQueries = await page.evaluate(() => {
      const queries = [];
      for (const sheet of document.styleSheets) {
        try {
          for (const rule of sheet.cssRules) {
            if (rule instanceof CSSMediaRule) {
              // Check if this media query contains bottom-nav styles
              for (const nestedRule of rule.cssRules) {
                if (nestedRule.selectorText && nestedRule.selectorText.includes('.bottom-nav')) {
                  queries.push({
                    query: rule.conditionText,
                    selector: nestedRule.selectorText,
                    css: nestedRule.cssText
                  });
                }
              }
            }
          }
        } catch (e) {
          // Skip cross-origin stylesheets
        }
      }
      return queries;
    });
    
    console.log('Bottom Navigation Media Queries:');
    console.log(JSON.stringify(bottomNavMediaQueries, null, 2));
    
    // Verify that bottom navigation has responsive CSS
    expect(bottomNavCSS.length).toBeGreaterThan(0);
    expect(bottomNavMediaQueries.length).toBeGreaterThan(0);
  });
});
