/**
 * HTTP Client utility for making API requests
 * Provides consistent error handling and response formatting
 */
import { ref } from 'vue';
import { useAuth } from '@/store/auth';

// Configuration
// Using proxy in vite.config.js, so we don't need the base URL
// const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';
const API_TIMEOUT = parseInt(import.meta.env.VITE_API_TIMEOUT || '30000', 10); // Default: 30 seconds

// Global loading and error states
export const isLoading = ref(false);
export const globalError = ref(null);

/**
 * Makes an HTTP request to the API
 * @param {string} endpoint - API endpoint (without base URL)
 * @param {Object} options - Fetch options
 * @returns {Promise<Object>} - API response
 */
export const apiRequest = async (endpoint, options = {}) => {
  // Use relative URL for API requests to work with the proxy
  const url = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;

  // Default headers
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...options.headers,
  };

  // Add authorization header if token exists
  let token = options.token;
  if (!token) {
    try {
      // Try to get token from Pinia store
      const auth = useAuth();
      token = auth.token?.value;
    } catch (e) {
      // Fallback to localStorage if Pinia is not available
      token = localStorage.getItem('authToken');
    }
  }
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  // Request options
  const requestOptions = {
    ...options,
    headers,
  };

  // Add timeout signal if supported by the browser
  try {
    if (!options.signal && typeof window !== 'undefined' && window.AbortController) {
      const controller = new window.AbortController();
      setTimeout(() => controller.abort(), API_TIMEOUT);
      requestOptions.signal = controller.signal;
    }
  } catch (error) {
    console.warn('AbortController not supported, timeout will not work:', error.message);
  }

  // Set loading state
  isLoading.value = true;
  globalError.value = null;

  try {
    const response = await fetch(url, requestOptions);

    // Parse response
    let data;
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }

    // Handle API error responses
    if (!response.ok) {
      // Format error based on API error structure
      const errorMessage = data.message || 'An unexpected error occurred';
      const errorCode = data.error?.code || 'UNKNOWN_ERROR';

      const error = new Error(errorMessage);
      error.status = response.status;
      error.code = errorCode;
      error.response = data;

      throw error;
    }

    return data;
  } catch (error) {
    // Handle network errors
    if (error.name === 'AbortError') {
      const timeoutError = new Error('Request timed out');
      timeoutError.code = 'REQUEST_TIMEOUT';
      globalError.value = timeoutError;
      throw timeoutError;
    }

    // Set global error state
    globalError.value = error;

    // Rethrow for handling in the calling code
    throw error;
  } finally {
    isLoading.value = false;
  }
};

/**
 * HTTP GET request
 * @param {string} endpoint - API endpoint
 * @param {Object} options - Additional fetch options
 * @returns {Promise<Object>} - API response
 */
export const get = (endpoint, options = {}) => {
  return apiRequest(endpoint, {
    method: 'GET',
    ...options,
  });
};

/**
 * HTTP POST request
 * @param {string} endpoint - API endpoint
 * @param {Object} data - Request body data
 * @param {Object} options - Additional fetch options
 * @returns {Promise<Object>} - API response
 */
export const post = (endpoint, data, options = {}) => {
  return apiRequest(endpoint, {
    method: 'POST',
    body: JSON.stringify(data),
    ...options,
  });
};

/**
 * HTTP PUT request
 * @param {string} endpoint - API endpoint
 * @param {Object} data - Request body data
 * @param {Object} options - Additional fetch options
 * @returns {Promise<Object>} - API response
 */
export const put = (endpoint, data, options = {}) => {
  return apiRequest(endpoint, {
    method: 'PUT',
    body: JSON.stringify(data),
    ...options,
  });
};

/**
 * HTTP DELETE request
 * @param {string} endpoint - API endpoint
 * @param {Object} options - Additional fetch options
 * @returns {Promise<Object>} - API response
 */
export const del = (endpoint, options = {}) => {
  return apiRequest(endpoint, {
    method: 'DELETE',
    ...options,
  });
};

/**
 * Reset global error state
 */
export const resetError = () => {
  globalError.value = null;
};

/**
 * HTTP client with all methods
 */
export const httpClient = {
  get,
  post,
  put,
  delete: del,
  resetError,
  isLoading,
  globalError,
};

export default httpClient;
