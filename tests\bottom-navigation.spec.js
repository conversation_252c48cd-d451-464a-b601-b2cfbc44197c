import { test, expect } from '@playwright/test';

/**
 * Test suite specifically for the bottom navigation component
 * Tests appearance, functionality, and theme changes
 */
test.describe('Bottom Navigation Tests', () => {
  // Mock login function to bypass authentication
  async function mockLogin(page) {
    await page.goto('/login');
    
    // Mock the authentication state using localStorage
    await page.evaluate(() => {
      localStorage.setItem('isAuthenticated', 'true');
      localStorage.setItem('user', JSON.stringify({
        id: '123',
        name: 'Test User',
        phone: '+601234567890',
        points: 1000
      }));
    });
    
    // Navigate to home page
    await page.goto('/');
  }

  // Define viewport sizes for testing
  const viewports = {
    mobile: { width: 375, height: 667 },  // iPhone SE / common mobile size
    tablet: { width: 768, height: 1024 }, // iPad / tablet size
    desktop: { width: 1280, height: 800 } // Standard desktop size
  };

  // Test bottom navigation visibility and structure
  test('Bottom navigation should be visible and have correct structure', async ({ page }) => {
    await mockLogin(page);
    
    // Check bottom navigation visibility
    const bottomNav = page.locator('.bottom-nav');
    await expect(bottomNav).toBeVisible();
    
    // Check that it has the correct number of items
    const navItems = bottomNav.locator('.nav-item');
    const itemCount = await navItems.count();
    expect(itemCount).toBeGreaterThanOrEqual(4); // Should have at least 4 navigation items
    
    // Check for the special QR code button
    const qrCodeButton = bottomNav.locator('.qr-code-button');
    await expect(qrCodeButton).toBeVisible();
    
    // Check that the QR code button has the correct styling
    const qrIcon = qrCodeButton.locator('.qr-icon');
    await expect(qrIcon).toBeVisible();
    
    // Check that the QR label is visible
    const qrLabel = qrCodeButton.locator('.qr-label');
    await expect(qrLabel).toBeVisible();
  });

  // Test bottom navigation appearance on different viewports
  test('Bottom navigation should adapt to different viewports', async ({ page }) => {
    await mockLogin(page);
    
    // Test on mobile viewport
    await page.setViewportSize(viewports.mobile);
    
    // Check bottom navigation visibility and sizing
    const bottomNav = page.locator('.bottom-nav');
    await expect(bottomNav).toBeVisible();
    const bottomNavBounds = await bottomNav.boundingBox();
    expect(bottomNavBounds.width).toBeLessThanOrEqual(viewports.mobile.width);
    
    // Check QR button size on mobile
    const qrCodeButton = bottomNav.locator('.qr-code-button');
    const qrCodeButtonBounds = await qrCodeButton.boundingBox();
    expect(qrCodeButtonBounds.width).toBeLessThanOrEqual(60); // Should be 55-60px on mobile
    
    // Test on desktop viewport
    await page.setViewportSize(viewports.desktop);
    
    // Check that bottom navigation has desktop styling (centered with max-width)
    const bottomNavDesktop = await bottomNav.boundingBox();
    expect(bottomNavDesktop.width).toBeLessThan(viewports.desktop.width); // Should not be full width on desktop
    
    // Check QR button size on desktop
    const qrCodeButtonDesktop = await qrCodeButton.boundingBox();
    expect(qrCodeButtonDesktop.width).toBeLessThanOrEqual(60); // Should be 60px on desktop
  });

  // Test bottom navigation theme changes
  test('Bottom navigation should change appearance with theme', async ({ page }) => {
    await mockLogin(page);
    await page.goto('/profile'); // Go to profile to access theme toggle
    
    // Get initial theme state
    const bottomNav = page.locator('.bottom-nav');
    const initialThemeClass = await bottomNav.getAttribute('class');
    
    // Toggle theme by clicking the theme setting
    const themeItem = page.locator('.settings-item', { hasText: 'Theme' });
    await themeItem.click();
    
    // Wait for theme change to apply
    await page.waitForTimeout(300);
    
    // Check that bottom navigation class has changed
    const newThemeClass = await bottomNav.getAttribute('class');
    expect(newThemeClass).not.toEqual(initialThemeClass);
    
    // Toggle back
    await themeItem.click();
    await page.waitForTimeout(300);
    
    // Should be back to initial theme
    const finalThemeClass = await bottomNav.getAttribute('class');
    expect(finalThemeClass).toEqual(initialThemeClass);
  });

  // Test navigation functionality
  test('Bottom navigation should navigate to correct pages', async ({ page }) => {
    await mockLogin(page);
    
    // Test navigation to Home
    await page.locator('.nav-item', { hasText: 'Home' }).click();
    await expect(page).toHaveURL('/');
    
    // Test navigation to Vouchers
    await page.locator('.nav-item', { hasText: 'Vouchers' }).click();
    await expect(page).toHaveURL('/vouchers');
    
    // Test navigation to Locations
    await page.locator('.nav-item', { hasText: 'Locations' }).click();
    await expect(page).toHaveURL('/locations');
    
    // Test navigation to Profile
    await page.locator('.nav-item', { hasText: 'Profile' }).click();
    await expect(page).toHaveURL('/profile');
    
    // Test navigation to QR Code
    await page.locator('.qr-code-button').click();
    await expect(page).toHaveURL('/qrcode');
  });

  // Test active state of navigation items
  test('Bottom navigation should highlight active page', async ({ page }) => {
    await mockLogin(page);
    
    // Navigate to Home and check active state
    await page.goto('/');
    await expect(page.locator('.nav-item', { hasText: 'Home' })).toHaveClass(/active/);
    
    // Navigate to Vouchers and check active state
    await page.goto('/vouchers');
    await expect(page.locator('.nav-item', { hasText: 'Vouchers' })).toHaveClass(/active/);
    
    // Navigate to Locations and check active state
    await page.goto('/locations');
    await expect(page.locator('.nav-item', { hasText: 'Locations' })).toHaveClass(/active/);
    
    // Navigate to Profile and check active state
    await page.goto('/profile');
    await expect(page.locator('.nav-item', { hasText: 'Profile' })).toHaveClass(/active/);
  });

  // Test bottom navigation on different devices
  test('Bottom navigation should work on different mobile devices', async ({ page }) => {
    await mockLogin(page);
    
    // Test on iPhone SE (small iPhone)
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('.bottom-nav')).toBeVisible();
    
    // Test on iPhone 12 (notched iPhone)
    await page.setViewportSize({ width: 390, height: 844 });
    await expect(page.locator('.bottom-nav')).toBeVisible();
    
    // Test on Pixel 5 (Android)
    await page.setViewportSize({ width: 393, height: 851 });
    await expect(page.locator('.bottom-nav')).toBeVisible();
    
    // Test on iPad
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('.bottom-nav')).toBeVisible();
  });

  // Test bottom navigation safe area insets
  test('Bottom navigation should respect safe area insets', async ({ page }) => {
    await mockLogin(page);
    
    // Check that the bottom navigation has padding-bottom with env(safe-area-inset-bottom)
    const bottomNav = page.locator('.bottom-nav');
    const paddingBottom = await bottomNav.evaluate(el => {
      return window.getComputedStyle(el).paddingBottom;
    });
    
    // Should have some padding at the bottom
    expect(paddingBottom).toBeTruthy();
    
    // Check CSS for safe area inset
    const hasEnvSafeArea = await page.evaluate(() => {
      const styleSheets = Array.from(document.styleSheets);
      for (const sheet of styleSheets) {
        try {
          const rules = Array.from(sheet.cssRules);
          for (const rule of rules) {
            if (rule.cssText && rule.cssText.includes('env(safe-area-inset-bottom)')) {
              return true;
            }
          }
        } catch (e) {
          // Ignore cross-origin stylesheet errors
        }
      }
      return false;
    });
    
    // Should have env(safe-area-inset-bottom) in CSS
    expect(hasEnvSafeArea).toBeTruthy();
  });
});
