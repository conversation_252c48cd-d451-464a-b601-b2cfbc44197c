<script setup>
import { useRouter } from 'vue-router';
import { useTheme } from '../store/theme';
import { useI18n } from 'vue-i18n';

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  subtitle: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'INFO' // INFO, CAMPAIGN, etc.
  },
  content: {
    type: Object,
    required: true
  }
});

const router = useRouter();
const { isDarkMode } = useTheme();
const { t } = useI18n();

// Back button is now handled by the Header component
</script>

<template>
  <div class="information-page" :data-type="type">

    <!-- Image placeholder (gray box) -->
    <div class="image-placeholder"></div>

    <!-- Content header -->
    <div class="content-header">
      <div class="content-type">{{ type }}</div>
      <h1 class="content-title">{{ title }}</h1>
      <p class="content-subtitle">{{ subtitle }}</p>
    </div>

    <!-- Main content -->
    <div class="content-body">
      <!-- Introduction text -->
      <p v-if="content.introText" class="intro-text">{{ content.introText }}</p>

      <!-- Steps section -->
      <div v-if="content.steps" class="steps-section">
        <h2 v-if="content.stepsTitle" class="steps-title">{{ content.stepsTitle }}</h2>
        <p v-if="content.stepsIntro" class="steps-intro">{{ content.stepsIntro }}</p>

        <ol class="steps-list">
          <li v-for="(step, index) in content.steps" :key="index" class="step-item">
            <strong>{{ step.title }}</strong>
            <p>{{ step.description }}</p>
          </li>
        </ol>
      </div>

      <!-- Why it matters section -->
      <div v-if="content.whyItMatters" class="why-matters-section">
        <h2 v-if="content.whyTitle" class="why-title">{{ content.whyTitle }}</h2>
        <ul class="benefits-list">
          <li v-for="(benefit, index) in content.whyItMatters" :key="index" class="benefit-item">
            <span class="check-icon">✓</span>
            <span>{{ benefit }}</span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<style scoped>
.information-page {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  padding: 0;
  padding-top: env(safe-area-inset-top, 20px);
  padding-bottom: calc(7rem + env(safe-area-inset-bottom, 0px)); /* Increased bottom padding */
  background-color: var(--background-color);
  color: var(--text-color);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

/* Back button is now handled by the Header component */

.image-placeholder {
  width: 100%;
  height: 200px;
  background-color: #e0e0e0;
  margin-bottom: 1rem;
}

/* Dark theme adjustment for placeholder */
:global(.dark-theme) .image-placeholder {
  background-color: #333333;
}

.content-header {
  padding: 0 1rem;
  margin-bottom: 1.5rem;
}

.content-type {
  font-size: 0.8rem;
  font-weight: 500;
  color: #4ECDC4;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
}

/* Campaign type styling */
.information-page[data-type="CAMPAIGN"] .content-type {
  color: #FF9966;
}

.content-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.content-subtitle {
  font-size: 1.2rem;
  color: var(--text-light);
  font-weight: 400;
}

.content-body {
  padding: 0 1rem 4rem; /* Increased bottom padding */
}

.intro-text {
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.steps-section {
  margin-bottom: 2rem;
}

.steps-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.steps-intro {
  margin-bottom: 1rem;
}

.steps-list {
  padding-left: 1.5rem;
  counter-reset: step-counter;
}

.step-item {
  margin-bottom: 1.5rem;
  position: relative;
}

.step-item strong {
  display: block;
  margin-bottom: 0.5rem;
}

.step-item p {
  margin: 0;
  line-height: 1.5;
}

.why-matters-section {
  margin-top: 2rem;
}

.why-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.benefits-list {
  list-style: none;
  padding: 0;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.check-icon {
  color: #4ECDC4;
  margin-right: 0.5rem;
  font-weight: bold;
}

/* Dark theme specific adjustments */
:global(.dark-theme) .image-placeholder {
  background-color: #333333;
}

:global(.dark-theme) .content-type {
  color: #4ECDC4;
}

:global(.dark-theme) .check-icon {
  color: #4ECDC4;
}
</style>
