/**
 * Malaysian zip codes with city and state information
 * This is a sample dataset - in a real application, you would have a more comprehensive list
 */
export const malaysiaZipCodes = [
  // Kuala Lumpur
  { zipCode: '50000', city: 'Kuala Lumpur', state: 'KUL' },
  { zipCode: '50050', city: 'Kuala Lumpur', state: 'KUL' },
  { zipCode: '50100', city: 'Kuala Lumpur', state: 'KUL' },
  { zipCode: '50150', city: 'Kuala Lumpur', state: 'KUL' },
  { zipCode: '50200', city: 'Kuala Lumpur', state: 'KUL' },
  { zipCode: '50300', city: 'Kuala Lumpur', state: 'KUL' },
  
  // Selangor
  { zipCode: '40000', city: 'Shah Alam', state: 'SGR' },
  { zipCode: '40100', city: 'Shah Alam', state: 'SGR' },
  { zipCode: '40150', city: 'Shah Alam', state: 'SGR' },
  { zipCode: '40170', city: 'Shah Alam', state: 'SGR' },
  { zipCode: '40200', city: 'Shah Alam', state: 'SGR' },
  { zipCode: '40300', city: 'Shah Alam', state: 'SGR' },
  { zipCode: '41000', city: 'Klang', state: 'SGR' },
  { zipCode: '41100', city: 'Klang', state: 'SGR' },
  { zipCode: '41200', city: 'Klang', state: 'SGR' },
  { zipCode: '41300', city: 'Klang', state: 'SGR' },
  { zipCode: '42000', city: 'Pelabuhan Klang', state: 'SGR' },
  { zipCode: '42100', city: 'Pelabuhan Klang', state: 'SGR' },
  { zipCode: '43000', city: 'Kajang', state: 'SGR' },
  { zipCode: '43100', city: 'Kajang', state: 'SGR' },
  { zipCode: '43200', city: 'Kajang', state: 'SGR' },
  { zipCode: '43300', city: 'Kajang', state: 'SGR' },
  { zipCode: '43500', city: 'Semenyih', state: 'SGR' },
  { zipCode: '43600', city: 'Bangi', state: 'SGR' },
  { zipCode: '43650', city: 'Bandar Baru Bangi', state: 'SGR' },
  { zipCode: '43700', city: 'Beranang', state: 'SGR' },
  { zipCode: '43800', city: 'Dengkil', state: 'SGR' },
  { zipCode: '43900', city: 'Sepang', state: 'SGR' },
  { zipCode: '45000', city: 'Kuala Selangor', state: 'SGR' },
  { zipCode: '45100', city: 'Kuala Selangor', state: 'SGR' },
  { zipCode: '45200', city: 'Sabak Bernam', state: 'SGR' },
  { zipCode: '45300', city: 'Sungai Besar', state: 'SGR' },
  { zipCode: '45400', city: 'Sekinchan', state: 'SGR' },
  { zipCode: '45500', city: 'Tanjong Karang', state: 'SGR' },
  { zipCode: '45600', city: 'Batang Berjuntai', state: 'SGR' },
  { zipCode: '45700', city: 'Bukit Rotan', state: 'SGR' },
  { zipCode: '45800', city: 'Jeram', state: 'SGR' },
  { zipCode: '46000', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '46050', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '46100', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '46150', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '46200', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '46300', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '46350', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '46400', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '46500', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '46550', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '46570', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '46580', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '46590', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '46600', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '46700', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '46800', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '46900', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '47000', city: 'Sungai Buloh', state: 'SGR' },
  { zipCode: '47100', city: 'Puchong', state: 'SGR' },
  { zipCode: '47110', city: 'Puchong', state: 'SGR' },
  { zipCode: '47120', city: 'Puchong', state: 'SGR' },
  { zipCode: '47130', city: 'Puchong', state: 'SGR' },
  { zipCode: '47140', city: 'Puchong', state: 'SGR' },
  { zipCode: '47150', city: 'Puchong', state: 'SGR' },
  { zipCode: '47160', city: 'Puchong', state: 'SGR' },
  { zipCode: '47170', city: 'Puchong', state: 'SGR' },
  { zipCode: '47180', city: 'Puchong', state: 'SGR' },
  { zipCode: '47190', city: 'Puchong', state: 'SGR' },
  { zipCode: '47200', city: 'Subang', state: 'SGR' },
  { zipCode: '47300', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '47301', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '47307', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '47308', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '47400', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '47410', city: 'Petaling Jaya', state: 'SGR' },
  { zipCode: '47500', city: 'Subang Jaya', state: 'SGR' },
  { zipCode: '47600', city: 'Subang Jaya', state: 'SGR' },
  { zipCode: '47610', city: 'Subang Jaya', state: 'SGR' },
  { zipCode: '47620', city: 'Subang Jaya', state: 'SGR' },
  { zipCode: '47630', city: 'Subang Jaya', state: 'SGR' },
  { zipCode: '47640', city: 'Subang Jaya', state: 'SGR' },
  { zipCode: '47650', city: 'Subang Jaya', state: 'SGR' },
  
  // Johor
  { zipCode: '80000', city: 'Johor Bahru', state: 'JHR' },
  { zipCode: '80100', city: 'Johor Bahru', state: 'JHR' },
  { zipCode: '80150', city: 'Johor Bahru', state: 'JHR' },
  { zipCode: '80200', city: 'Johor Bahru', state: 'JHR' },
  { zipCode: '80250', city: 'Johor Bahru', state: 'JHR' },
  { zipCode: '80300', city: 'Johor Bahru', state: 'JHR' },
  { zipCode: '81000', city: 'Kulai', state: 'JHR' },
  { zipCode: '81100', city: 'Kulai', state: 'JHR' },
  { zipCode: '81200', city: 'Johor Bahru', state: 'JHR' },
  { zipCode: '81300', city: 'Skudai', state: 'JHR' },
  { zipCode: '81310', city: 'Skudai', state: 'JHR' },
  
  // Penang
  { zipCode: '10000', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10050', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10100', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10150', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10200', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10250', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10300', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10350', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10400', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10450', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10460', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10470', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10500', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10502', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10503', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10504', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10505', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10506', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10508', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10512', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10514', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10516', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10518', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10520', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10524', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10534', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10538', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10540', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10542', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10546', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10550', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10551', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10552', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10558', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10560', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10564', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10566', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10570', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10576', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10578', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10582', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10590', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10592', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10593', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10594', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10596', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10600', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10604', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10609', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10610', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10612', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10620', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10622', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10626', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10628', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10634', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10646', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10648', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10660', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10661', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10662', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10670', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10672', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10673', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10674', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10676', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10690', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10710', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10720', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10730', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10740', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10750', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10760', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10770', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10780', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10790', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10800', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10810', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10820', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10830', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10840', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10850', city: 'Georgetown', state: 'PNG' },
  { zipCode: '10910', city: 'Batu Ferringhi', state: 'PNG' },
  { zipCode: '10920', city: 'Batu Ferringhi', state: 'PNG' },
  { zipCode: '11000', city: 'Balik Pulau', state: 'PNG' },
  { zipCode: '11010', city: 'Balik Pulau', state: 'PNG' },
  { zipCode: '11020', city: 'Balik Pulau', state: 'PNG' },
  { zipCode: '11050', city: 'Teluk Bahang', state: 'PNG' },
  { zipCode: '11060', city: 'Teluk Bahang', state: 'PNG' },
  { zipCode: '11100', city: 'Batu Ferringhi', state: 'PNG' },
  { zipCode: '11200', city: 'Tanjung Bungah', state: 'PNG' },
  { zipCode: '11300', city: 'Tanjung Bungah', state: 'PNG' },
  { zipCode: '11400', city: 'Pulau Tikus', state: 'PNG' },
  { zipCode: '11500', city: 'Air Itam', state: 'PNG' },
  { zipCode: '11600', city: 'Jelutong', state: 'PNG' },
  { zipCode: '11700', city: 'Gelugor', state: 'PNG' },
  { zipCode: '11800', city: 'Universiti Sains Malaysia', state: 'PNG' },
  { zipCode: '11900', city: 'Bayan Lepas', state: 'PNG' },
  { zipCode: '11910', city: 'Bayan Lepas', state: 'PNG' },
  { zipCode: '11920', city: 'Bayan Lepas', state: 'PNG' },
  { zipCode: '11950', city: 'Bayan Lepas', state: 'PNG' },
  { zipCode: '11960', city: 'Bayan Lepas', state: 'PNG' },
  { zipCode: '12000', city: 'Butterworth', state: 'PNG' },
  { zipCode: '12100', city: 'Butterworth', state: 'PNG' },
  { zipCode: '12200', city: 'Butterworth', state: 'PNG' },
  { zipCode: '12300', city: 'Butterworth', state: 'PNG' },
  { zipCode: '12700', city: 'Butterworth', state: 'PNG' },
  { zipCode: '12710', city: 'Butterworth', state: 'PNG' },
  { zipCode: '12720', city: 'Butterworth', state: 'PNG' },
  { zipCode: '12990', city: 'Butterworth', state: 'PNG' },
  { zipCode: '13000', city: 'Butterworth', state: 'PNG' },
  { zipCode: '13009', city: 'Butterworth', state: 'PNG' },
  { zipCode: '13020', city: 'Butterworth', state: 'PNG' },
  { zipCode: '13050', city: 'Butterworth', state: 'PNG' },
  { zipCode: '13100', city: 'Penaga', state: 'PNG' },
  { zipCode: '13110', city: 'Penaga', state: 'PNG' },
  { zipCode: '13200', city: 'Kepala Batas', state: 'PNG' },
  { zipCode: '13210', city: 'Kepala Batas', state: 'PNG' },
  { zipCode: '13220', city: 'Kepala Batas', state: 'PNG' },
  { zipCode: '13300', city: 'Tasek Gelugor', state: 'PNG' },
  { zipCode: '13310', city: 'Tasek Gelugor', state: 'PNG' },
  { zipCode: '13400', city: 'Butterworth', state: 'PNG' },
  { zipCode: '13409', city: 'Butterworth', state: 'PNG' },
  { zipCode: '13500', city: 'Permatang Pauh', state: 'PNG' },
  { zipCode: '13600', city: 'Perai', state: 'PNG' },
  { zipCode: '13700', city: 'Perai', state: 'PNG' },
  { zipCode: '13800', city: 'Butterworth', state: 'PNG' },
  { zipCode: '14000', city: 'Bukit Mertajam', state: 'PNG' },
  { zipCode: '14007', city: 'Bukit Mertajam', state: 'PNG' },
  { zipCode: '14009', city: 'Bukit Mertajam', state: 'PNG' },
  { zipCode: '14020', city: 'Bukit Mertajam', state: 'PNG' },
  { zipCode: '14100', city: 'Simpang Ampat', state: 'PNG' },
  { zipCode: '14101', city: 'Simpang Ampat', state: 'PNG' },
  { zipCode: '14110', city: 'Simpang Ampat', state: 'PNG' },
  { zipCode: '14120', city: 'Simpang Ampat', state: 'PNG' },
  { zipCode: '14200', city: 'Sungai Jawi', state: 'PNG' },
  { zipCode: '14300', city: 'Nibong Tebal', state: 'PNG' },
  { zipCode: '14310', city: 'Nibong Tebal', state: 'PNG' },
  { zipCode: '14320', city: 'Nibong Tebal', state: 'PNG' },
  { zipCode: '14390', city: 'Nibong Tebal', state: 'PNG' },
  { zipCode: '14400', city: 'Kubang Semang', state: 'PNG' }
];

/**
 * Get zip code information by zip code
 * @param {string} zipCode - Zip code to search for
 * @returns {object|null} Zip code information or null if not found
 */
export const getZipCodeInfo = (zipCode) => {
  return malaysiaZipCodes.find(item => item.zipCode === zipCode) || null;
};

/**
 * Get zip codes by state code
 * @param {string} stateCode - State code to filter by
 * @returns {array} Array of zip code objects for the specified state
 */
export const getZipCodesByState = (stateCode) => {
  return malaysiaZipCodes.filter(item => item.state === stateCode);
};

/**
 * Get unique cities by state code
 * @param {string} stateCode - State code to filter by
 * @returns {array} Array of unique city names for the specified state
 */
export const getCitiesByState = (stateCode) => {
  const cities = malaysiaZipCodes
    .filter(item => item.state === stateCode)
    .map(item => item.city);
  
  // Return unique cities
  return [...new Set(cities)];
};
