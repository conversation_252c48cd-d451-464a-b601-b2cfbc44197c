import { createRouter, createWebHistory } from 'vue-router';
import { useAuth } from '../store/auth';

// Lazy-loaded route components
const Home = () => import('../views/Home.vue');
const Login = () => import('../views/Login.vue');
const Profile = () => import('../views/Profile.vue');
const EditProfile = () => import('../views/EditProfile.vue');
const Security = () => import('../views/Security.vue');
const Vouchers = () => import('../views/Vouchers.vue');
const VoucherDetail = () => import('../views/VoucherDetail.vue');
const Locations = () => import('../views/Locations.vue');
const Transactions = () => import('../views/Transactions.vue');
const QRCode = () => import('../views/QRCode.vue');
const Move = () => import('../views/Move.vue');
const Forest = () => import('../views/Forest.vue');
const Information = () => import('../views/Information.vue');
const Help = () => import('../views/Help.vue');
const InAppBrowser = () => import('../components/InAppBrowser.vue');
const HighlightDetail = () => import('../components/HighlightDetail.vue');

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { requiresAuth: true, title: 'Home' },
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { guest: true, title: 'Login' },
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: { requiresAuth: true, title: 'Profile' },
  },
  {
    path: '/edit-profile',
    name: 'EditProfile',
    component: EditProfile,
    meta: { requiresAuth: true, title: 'Edit Profile' },
  },
  {
    path: '/security',
    name: 'Security',
    component: Security,
    meta: { requiresAuth: true, title: 'Security' },
  },
  {
    path: '/vouchers',
    name: 'Vouchers',
    component: Vouchers,
    meta: { requiresAuth: true, title: 'Vouchers' },
  },
  {
    path: '/vouchers/:id',
    name: 'VoucherDetail',
    component: VoucherDetail,
    meta: { requiresAuth: true, title: 'Voucher Details' },
  },
  {
    path: '/locations',
    name: 'Locations',
    component: Locations,
    meta: { requiresAuth: true, title: 'Locations' },
  },
  {
    path: '/transactions',
    name: 'Transactions',
    component: Transactions,
    meta: { requiresAuth: true, title: 'Transactions' },
  },
  {
    path: '/qrcode',
    name: 'QRCode',
    component: QRCode,
    meta: { requiresAuth: true, title: 'QR Code' },
  },
  {
    path: '/move',
    name: 'Move',
    component: Move,
    meta: { requiresAuth: true, title: 'Your MOVE' },
  },
  {
    path: '/info/:type',
    name: 'Information',
    component: Information,
    meta: { requiresAuth: true, title: 'Information' },
  },
  // Specific routes for each information type for direct access
  {
    path: '/info/redeem',
    name: 'HowToRedeem',
    component: Information,
    meta: { requiresAuth: true, title: 'How To Redeem' },
    props: { type: 'redeem' },
  },
  {
    path: '/info/use',
    name: 'HowToUse',
    component: Information,
    meta: { requiresAuth: true, title: 'How To Use' },
    props: { type: 'use' },
  },
  {
    path: '/info/byc',
    name: 'BYC',
    component: Information,
    meta: { requiresAuth: true, title: 'BYC Information' },
    props: { type: 'byc' },
  },
  {
    path: '/info/campaign',
    name: 'Campaign',
    component: Information,
    meta: { requiresAuth: true, title: 'Campaign Details' },
    props: { type: 'campaign' },
  },
  {
    path: '/help',
    name: 'Help',
    component: () => import('../views/Help.vue'),
    meta: { requiresAuth: true, title: 'Help & Support' },
  },
  {
    path: '/browser/:url/:title?',
    name: 'InAppBrowser',
    component: InAppBrowser,
    meta: { requiresAuth: true, title: 'Browser' },
    props: true,
  },
  {
    path: '/test-request-handler-auth',
    name: 'RequestHandlerAuthTest',
    component: () => import('../views/RequestHandlerAuthTest.vue'),
    meta: { title: 'RequestHandlerAuth Test' },
  },
  {
    path: '/highlights/:id',
    name: 'HighlightDetail',
    component: HighlightDetail,
    meta: { title: 'Highlight Detail' },
  },
  {
    path: '/forest',
    name: 'Forest',
    component: Forest,
    meta: { requiresAuth: true, title: 'Forest' },
  },
  
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
});

// Navigation guards
router.beforeEach((to, from, next) => {
  const { isLoggedIn } = useAuth();

  // Routes that require authentication
  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!isLoggedIn.value) {
      // Redirect to login if not authenticated
      next({ name: 'Login' });
    } else {
      next();
    }
  }
  // Routes for guests only (like login)
  else if (to.matched.some(record => record.meta.guest)) {
    if (isLoggedIn.value) {
      // Redirect to home if already authenticated
      next({ name: 'Home' });
    } else {
      next();
    }
  } else {
    next();
  }
});

export default router;
