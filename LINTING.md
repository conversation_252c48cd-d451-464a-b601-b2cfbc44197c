# ESLint and Prettier Setup

This project uses <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> to maintain code quality and consistent formatting.

## Available Commands

```bash
# Run ESLint to check for issues
npm run lint

# Run ESLint and automatically fix issues where possible
npm run lint:fix

# Format code with Prettier
npm run format
```

## Configuration Files

- `eslint.config.js` - ESLint configuration
- `.prettierrc.js` - Prettier configuration
- `.vscode/settings.json` - VS Code editor settings

## ESLint Rules

The ESLint configuration includes rules for:

- Vue 3 best practices
- JavaScript code quality
- Consistent formatting
- Error prevention

## VS Code Integration

If you're using VS Code, the project includes settings to:

1. Format code on save
2. Fix ESLint issues on save
3. Use Prettier as the default formatter

## Recommended VS Code Extensions

- ESLint (`dbaeumer.vscode-eslint`)
- Prettier (`esbenp.prettier-vscode`)
- Volar (`Vue.volar`) - Already recommended in the project

## Customizing Rules

To modify ESLint rules, edit the `eslint.config.js` file. The rules are organized into categories:

- Vue specific rules
- JavaScript rules
- Spacing and formatting
- ES6+ features
- Best practices

## Ignoring Files

The ESLint configuration already ignores:

- Build output directories (`dist/`)
- Node modules
- Test reports
- Configuration files
- Non-code files (`.md`, `.svg`, `.json`)

To add more files or directories to ignore, update the `ignores` array in `eslint.config.js`.
