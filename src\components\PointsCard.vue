<script setup>
import { useI18n } from 'vue-i18n';
import { useAuth } from '@/store/auth';

// Use URL import for PNG file
const { t } = useI18n();
const { token, accountNumber } = useAuth();

const props = defineProps({
  points: {
    type: Number,
    required: true,
    default: 0,
  },
});

const refreshPoints = () => {
  // In a real app, this would fetch the latest points from an API
  console.log('Refreshing points...');

  const params = {
    Token: token.value,
    bitmap: 1,
    Func: '0',
    Ref01: accountNumber.value,
    Ref02: '',
    Lang: 'en',
  };
};
</script>

<template>
  <div class="points-card">
    <div class="points-header">
      <h2>{{ t('points.yourPoints') }}</h2>
    </div>
    <div class="points-value">{{ points }}</div>
  </div>
</template>

<style scoped>
.points-card {
  background: linear-gradient(135deg, #4ECDC4, #92E8E4);
  border-radius: 16px;
  padding: 1.5rem;
  color: white;
  margin: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.points-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.points-header h2 {
  font-size: 1.2rem;
  font-weight: 500;
  margin: 0;
}

.refresh-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-button img {
  width: 20px;
  height: 20px;
  filter: brightness(0) invert(1);
}

.points-value {
  font-size: 3.5rem;
  font-weight: 700;
  margin-top: 0.5rem;
}

/* Responsive styles */
@media (min-width: 769px) {
  .points-card {
    max-width: 400px;
    margin: 1rem auto;
  }
}
</style>
