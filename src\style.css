*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* Color variables - Light Theme */
  --primary-color: #32B2B8;
  --primary-light: #7dd3d8;
  --primary-dark: #2a9ca2;
  --secondary-color: #35495e;
  --text-color: #333333;
  --text-light: #666666;
  --background-color: #f5f5f5;
  --card-background: #ffffff;
  --border-color: #e0e0e0;
  --highlight-color: #f0f9f6;
  --error-color: #ff5252;
  --success-color: #4caf50;

  color: var(--text-color);
  background-color: var(--background-color);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Dark Theme */
.dark-theme {
  --primary-color: #32B2B8;
  --primary-light: #2a9ca2;
  --primary-dark: #7dd3d8;
  --secondary-color: #35495e;
  --text-color: #f0f0f0;
  --text-light: #cccccc;
  --background-color: #121212;
  --card-background: #1e1e1e;
  --border-color: #333333;
  --highlight-color: #2a3a3a;
  --error-color: #ff5252;
  --success-color: #4caf50;
}

a {
  font-weight: 500;
  color: var(--primary-color);
  text-decoration: inherit;
}
a:hover {
  color: var(--primary-dark);
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden; /* Prevent body scrolling */
}

/* Style for when country dropdown is open */
body.dropdown-open {
  overflow: hidden;
  position: fixed;
  width: 100%;
  height: 100%;
  touch-action: none; /* Disable touch scrolling */
}

/* Style for login page to prevent scrolling */
body.login-page {
  overflow: hidden;
}

body {
  min-width: 320px;
  background-color: var(--background-color);
  padding-top: env(safe-area-inset-top, 0px);
  padding-bottom: env(safe-area-inset-bottom, 0px);
  padding-left: env(safe-area-inset-left, 0px);
  padding-right: env(safe-area-inset-right, 0px);
  position: fixed; /* Prevent bounce effect on iOS */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: var(--primary-color);
  color: white;
  cursor: pointer;
  transition: all 0.25s ease;
}
button:hover {
  background-color: var(--primary-dark);
}
button:focus,
button:focus-visible {
  outline: 3px solid var(--primary-light);
}

.card {
  padding: 2em;
}

#app {
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
  padding: 0;
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--background-color);
  position: relative;
  overflow: hidden; /* Prevent app scrolling */
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-color);
}

.card {
  background-color: var(--card-background);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
  margin-bottom: 1rem;
}

.icon-button {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: var(--primary-color);
}

.icon-button:hover {
  color: var(--primary-dark);
  background: none;
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--primary-light);
}

.icon-container svg {
  width: 24px;
  height: 24px;
  color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  #app {
    max-width: 100%;
  }

  h1 {
    font-size: 2.5em;
  }

  button {
    padding: 0.5em 1em;
  }
}

@media (max-width: 480px) {
  h1 {
    font-size: 2em;
  }

  body {
    min-width: 280px;
  }

  .card {
    padding: 1rem;
  }
}
