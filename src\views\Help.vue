<script setup>
import { ref, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useTheme } from '../store/theme';
import { usePublicBitmapRequest } from '@/composables/usePublicBitmapRequest';

const { t } = useI18n();
const { isDarkMode } = useTheme();

// FAQ state
const faqs = ref([]);
const expanded = ref([]); // Array of expanded FAQ ids
const loading = ref(false);
const error = ref(null);

const { fetchData, data, isLoading, error: fetchError } = usePublicBitmapRequest();

const toggleExpand = (faqId) => {
  if (expanded.value.includes(faqId)) {
    expanded.value = expanded.value.filter(id => id !== faqId);
  } else {
    expanded.value.push(faqId);
  }
};

onMounted(async () => {
  loading.value = true;
  try {
    // Fetch FAQ data (bitmap 8)
    const res = await fetchData(8);
    // Defensive: handle both res.data and res (API shape may vary)
    const faqList = res?.data || res;
    if (Array.isArray(faqList)) {
      faqs.value = faqList;
    } else if (Array.isArray(faqList?.data)) {
      faqs.value = faqList.data;
    } else {
      faqs.value = [];
    }
  } catch (err) {
    error.value = t('common.error', 'Failed to load FAQs');
  } finally {
    loading.value = false;
  }
});
</script>

<template>
  <div class="help-container">
    <div class="help-content">
      <h1 class="faq-title">{{ t('help.faqTitle', 'Frequently Asked Questions') }}</h1>
      <div v-if="loading || isLoading" class="faq-loading">{{ t('common.loading', 'Loading...') }}</div>
      <div v-else-if="error || fetchError" class="faq-error">{{ error || fetchError }}</div>
      <div v-else>
        <div v-if="faqs.length === 0" class="faq-empty">{{ t('help.noFaqs', 'No FAQs available.') }}</div>
        <div v-else class="faq-list">
          <div v-for="faq in faqs" :key="faq.FaqId" class="faq-item">
            <div class="faq-question" @click="toggleExpand(faq.FaqId)">
              <span class="faq-toggle">{{ expanded.includes(faq.FaqId) ? '–' : '+' }}</span>
              <span class="faq-title-text">{{ faq.Subject }}</span>
            </div>
            <div v-if="expanded.includes(faq.FaqId)" class="faq-answer" v-html="faq.Descp"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.help-container {
  padding: 1rem;
  width: 100%;
  margin: 0 auto;
  margin-bottom: 7rem;
  background-color: var(--background-color);
  min-height: 100vh;
  padding-top: env(safe-area-inset-top, 20px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.help-content {
  display: flex;
  flex-direction: column;
  max-width: 600px;
  margin: 0 auto;
}

.faq-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: var(--text-color);
  text-align: center;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.faq-item {
  background: var(--card-background);
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  padding: 0.5rem 1rem;
}

.faq-question {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--primary-color);
  padding: 1rem 0;
}

.faq-toggle {
  font-size: 1.5rem;
  width: 2rem;
  display: inline-block;
  text-align: center;
  color: var(--primary-color);
  margin-right: 0.5rem;
}

.faq-title-text {
  flex: 1;
}

.faq-answer {
  padding: 0.5rem 0 1rem 2.5rem;
  color: var(--text-color);
  font-size: 1rem;
  white-space: pre-line;
}

.faq-loading, .faq-error, .faq-empty {
  text-align: center;
  color: var(--text-light);
  margin: 2rem 0;
}
</style>
