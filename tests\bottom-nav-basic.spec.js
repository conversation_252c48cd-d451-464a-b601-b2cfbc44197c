import { test, expect } from '@playwright/test';

/**
 * Basic tests for the bottom navigation component
 */
test.describe('Bottom Navigation Basic Tests', () => {
  // Define viewport sizes for testing
  const viewports = {
    mobile: { width: 375, height: 667 },  // iPhone SE / common mobile size
    desktop: { width: 1280, height: 800 } // Standard desktop size
  };

  // Helper function to set up authentication
  async function setupAuth(page) {
    // Set authentication in localStorage
    await page.evaluate(() => {
      localStorage.setItem('isAuthenticated', 'true');
      localStorage.setItem('user', JSON.stringify({
        id: '123',
        name: 'Test User',
        phone: '+601234567890',
        points: 1000
      }));
    });
  }

  // Test bottom navigation visibility on mobile
  test('Bottom navigation should be visible on mobile', async ({ page }) => {
    // Set viewport to mobile size
    await page.setViewportSize(viewports.mobile);
    
    // Set up authentication
    await page.goto('/login');
    await setupAuth(page);
    
    // Go to home page
    await page.goto('/');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Take a screenshot for debugging
    await page.screenshot({ path: 'mobile-home.png' });
    
    // Check if bottom navigation exists in the DOM
    const bottomNavExists = await page.evaluate(() => {
      return !!document.querySelector('.bottom-nav');
    });
    
    console.log(`Bottom navigation exists in DOM: ${bottomNavExists}`);
    
    // If it exists, check if it's visible
    if (bottomNavExists) {
      const isVisible = await page.locator('.bottom-nav').isVisible();
      console.log(`Bottom navigation is visible: ${isVisible}`);
    }
    
    // Check what elements are visible on the page
    const visibleElements = await page.evaluate(() => {
      const elements = Array.from(document.querySelectorAll('*'));
      return elements
        .filter(el => {
          const style = window.getComputedStyle(el);
          return style.display !== 'none' && style.visibility !== 'hidden' && el.tagName !== 'SCRIPT' && el.tagName !== 'STYLE';
        })
        .map(el => ({
          tag: el.tagName,
          id: el.id,
          classes: el.className,
        }));
    });
    
    console.log('Visible elements on the page:');
    console.log(JSON.stringify(visibleElements.slice(0, 10), null, 2)); // Show first 10 elements
  });

  // Test bottom navigation visibility on desktop
  test('Bottom navigation should be visible on desktop', async ({ page }) => {
    // Set viewport to desktop size
    await page.setViewportSize(viewports.desktop);
    
    // Set up authentication
    await page.goto('/login');
    await setupAuth(page);
    
    // Go to home page
    await page.goto('/');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Take a screenshot for debugging
    await page.screenshot({ path: 'desktop-home.png' });
    
    // Check if bottom navigation exists in the DOM
    const bottomNavExists = await page.evaluate(() => {
      return !!document.querySelector('.bottom-nav');
    });
    
    console.log(`Bottom navigation exists in DOM: ${bottomNavExists}`);
    
    // If it exists, check if it's visible
    if (bottomNavExists) {
      const isVisible = await page.locator('.bottom-nav').isVisible();
      console.log(`Bottom navigation is visible: ${isVisible}`);
    }
  });

  // Test navigation between pages
  test('Should navigate between pages using bottom navigation', async ({ page }) => {
    // Set viewport to mobile size
    await page.setViewportSize(viewports.mobile);
    
    // Set up authentication
    await page.goto('/login');
    await setupAuth(page);
    
    // Go to home page
    await page.goto('/');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Check current URL
    console.log(`Initial URL: ${page.url()}`);
    
    // Try to navigate to vouchers page
    try {
      // Click on Vouchers in bottom navigation if it exists
      if (await page.locator('.nav-item', { hasText: 'Vouchers' }).isVisible()) {
        await page.locator('.nav-item', { hasText: 'Vouchers' }).click();
        
        // Wait for navigation
        await page.waitForURL('**/vouchers');
        
        // Check new URL
        console.log(`After navigation URL: ${page.url()}`);
        
        // Should be on vouchers page
        expect(page.url()).toContain('/vouchers');
      } else {
        console.log('Vouchers navigation item not visible');
      }
    } catch (error) {
      console.log(`Navigation error: ${error.message}`);
    }
  });
});
