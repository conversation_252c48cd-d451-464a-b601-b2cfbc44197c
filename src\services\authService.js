/**
 * Authentication service for handling user authentication
 */
import { ref } from 'vue';
import { mockUsers, simulateApiDelay, createApiResponse, createApiErrorResponse } from './mockData';

// Configuration
const USE_MOCK_DATA = import.meta.env.VITE_USE_MOCK_DATA === 'true'; // Set to false to use real API

// Auth state
const user = ref(null);
const isLoggedIn = ref(false);
const isLoading = ref(false);
const error = ref(null);
const token = ref(localStorage.getItem('authToken') || null);
const accountNumber = ref(localStorage.getItem('accountNumber') || null);

/**
 * Initialize auth state from localStorage
 */
const initAuth = () => {
  const storedToken = localStorage.getItem('authToken');
  const storedUser = localStorage.getItem('user');
  const storedAcctNo = localStorage.getItem('accountNumber');

  if (storedToken && storedUser) {
    token.value = storedToken;
    user.value = JSON.parse(storedUser);
    accountNumber.value = storedAcctNo;
    isLoggedIn.value = true;
  }
};



/**
 * Login with phone and password
 * @param {string} phone - User's phone number
 * @param {string} password - User's password
 * @param {string} lang - Language code (default: 'en')
 * @returns {Promise<Object>} Login response
 */
const login = async (phone, password, lang = 'en') => {
  isLoading.value = true;
  error.value = null;

  try {
    // Validate inputs
    if (!phone || phone.length < 6) {
      throw new Error('Please enter a valid phone number');
    }

    if (!password || password.length < 4) {
      throw new Error('Please enter a valid password');
    }

    if (USE_MOCK_DATA) {
      // Mock implementation
      await simulateApiDelay();

      // Find user in mock database
      const foundUser = mockUsers.find(u => u.phone === phone);

      if (!foundUser) {
        return createApiErrorResponse('USER_NOT_FOUND', 'User not found');
      }

      // Generate mock token
      const mockToken = `mock-jwt-token-${  Date.now()}`;

      // Set user as logged in
      user.value = foundUser;
      token.value = mockToken;
      accountNumber.value = foundUser.id;
      isLoggedIn.value = true;

      // Store in localStorage
      localStorage.setItem('authToken', mockToken);
      localStorage.setItem('accountNumber', foundUser.id);
      localStorage.setItem('user', JSON.stringify(foundUser));

      return createApiResponse({
        token: mockToken,
        user: foundUser,
      }, 'Login successful');
    } else {
      try {
        // Real API implementation - using the GET endpoint that worked in our tests
        // Use a more direct approach with fetch to avoid potential issues with httpClient
        const url = `${import.meta.env.VITE_API_BASE_URL}/api/cmlogin/${phone}/${password}/${lang}`;
        console.log('Login URL:', url);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          },
        });

        console.log('Login response status:', response.status);

        // Parse the response
        let data;
        try {
          data = await response.json();
          console.log('Login response data:', data);
        } catch (e) {
          console.error('Error parsing JSON:', e);
          const text = await response.text();
          console.log('Response text:', text);
          throw new Error('Invalid response format');
        }

        // Check if the response is an array
        if (Array.isArray(data) && data.length > 0) {
          const loginData = data[0];

          // Check if login was successful
          if (loginData.AcctNo && loginData.AcctNo !== 0) {
            // Create user object from login data
            const userData = {
              id: loginData.AcctNo,
              name: loginData.FullName || 'User',
              phone: phone,
              // Other user properties will be fetched from profile API
            };

            // Get the token - it might be named differently in the response
            const accessToken = loginData.access_token || loginData.Token || loginData.token;

            console.log('Login data:', loginData);
            console.log('Access token:', accessToken);

            if (!accessToken) {
              console.error('No token found in response:', loginData);
              throw new Error('No authentication token received');
            }

            // Set user as logged in
            user.value = userData;
            token.value = accessToken;
            accountNumber.value = loginData.AcctNo;
            isLoggedIn.value = true;

            // Store in localStorage
            localStorage.setItem('authToken', accessToken);
            localStorage.setItem('accountNumber', loginData.AcctNo);
            localStorage.setItem('user', JSON.stringify(userData));

            return createApiResponse({
              token: accessToken,
              user: userData,
            }, 'Login successful');
          } else {
            // Login failed
            const errorMessage = loginData.Descp || loginData.Message || 'Login failed';
            console.error('Login failed:', errorMessage);
            throw new Error(errorMessage);
          }
        } else if (data && data.Message) {
          // Some APIs return error messages in a different format
          throw new Error(data.Message);
        } else {
          console.error('Unexpected response format:', data);
          throw new Error('Unexpected response format');
        }
      } catch (err) {
        console.error('Login error:', err);
        throw err;
      }
    }
  } catch (err) {
    console.error('Login error details:', err);
    error.value = err.message || 'Login failed. Please try again.';
    return createApiErrorResponse('LOGIN_FAILED', error.value);
  } finally {
    isLoading.value = false;
  }
};

/**
 * Logout user
 */
const logout = () => {
  user.value = null;
  token.value = null;
  accountNumber.value = null;
  isLoggedIn.value = false;


  // Clear localStorage
  localStorage.removeItem('authToken');
  localStorage.removeItem('accountNumber');
  localStorage.removeItem('user');
};



// Initialize auth state
initAuth();

// Export auth service
export const useAuthService = () => {
  return {
    // State
    user,
    isLoggedIn,
    isLoading,
    error,
    token,
    accountNumber,

    // Methods
    login,
    logout,
  };
};
