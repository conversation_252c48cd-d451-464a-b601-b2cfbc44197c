# RVMPlus 奖励系统 - 手机认证

一个使用 Vue 3 构建的应用程序，具有手机认证功能，基于 Vue Router 和 Vite 开发。

## 功能特点

- 手机号码和密码认证
- 适配移动端和桌面端的响应式设计
- 使用 Vue Router 进行导航
- 使用 Playwright 测试认证流程

## 开发指南

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 运行测试
npm run test
```

## 部署到 Vercel

本项目已配置为可轻松部署到 Vercel。

### 自动部署

1. 将代码推送到 GitHub 仓库
2. 将仓库连接到 Vercel
3. Vercel 将自动检测 Vite 配置并部署您的应用

### 手动部署

1. 安装 Vercel CLI:
   ```bash
   npm install -g vercel
   ```

2. 登录 Vercel:
   ```bash
   vercel login
   ```

3. 从项目目录部署:
   ```bash
   vercel
   ```

4. 生产环境部署:
   ```bash
   vercel --prod
   ```

## 测试

测试登录功能时，可以使用以下凭据:
- 电话: 60102431439
- 密码: 123456

## API 文档

RVMPlus 提供了多种 API 接口用于积分发放、兑换和账户管理等功能。以下是主要 API 的概述。

### 网关 API

这些 API 主要用于积分发放和兑换处理。

| API | 描述 |
| --- | --- |
| POST gateway/points | 用于根据回收物品向会员发放积分 |
| POST gateway/pointSTS | 用于检查积分发放状态 |
| POST gateway/pointCancel | 用于撤销积分发放交易 |
| POST gateway/redeem | 用于通过自动售货机处理会员兑换 |
| POST gateway/redeemStatus | 用于检查兑换状态 |
| POST gateway/redeemCancel | 用于撤销积分发放交易 |
| POST gateway/CheckBalance/{AcctNo}/{lang} | 检查客户的最新可用余额 |
| POST gateway/RedeemVoucherQR | 检查最新的二维码或创建新的二维码 |

### 应用 API

#### 内容相关

| API | 描述 |
| --- | --- |
| GET api/ads/{lang}/{func} | 根据语言和功能获取广告 |
| GET api/Announcement/{lang}/{func} | 根据语言和功能获取公告 |
| GET api/AnnouncementDetail/{Lang}/{Ids} | 获取公告详情 |
| POST api/AcccountVoucher | 返回优惠券详情 |
| GET api/FAQ/{lang}/{func} | 获取常见问题列表 |
| GET api/WebView/{id} | 获取网页视图 URL |

#### 用户管理

| API | 描述 |
| --- | --- |
| POST api/register | 用户注册功能 |
| POST api/cmlogin | 登录功能，将生成 TOKEN |
| POST api/GetProfile/{AcctNo}/{Func}/{Lang} | 获取用户资料 |
| POST api/UpdateProfile/Picture | 更新用户图片 |
| POST api/UpdateProfile | 更新用户资料 |
| POST api/UpdatePassword/{func} | 更新用户密码 |
| POST api/cmlogin/{phone}/{password}/{lang} | 使用手机号和密码登录 |
| POST api/ForgetPassword | 处理忘记密码请求 |

#### 交易和兑换

| API | 描述 |
| --- | --- |
| POST api/RedeemVoucherQR | 处理二维码兑换 |
| POST api/AccountTransaction | 返回交易列表 |
| POST api/TerminalLocation | 选择终端位置 |
| POST api/TerminalProfile | 获取终端资料 |
| POST api/QRcodeProfile | 扫描二维码访问资料 |

## 注意事项

- API 文档基于 https://staging.rvmplus.com/Help 的内容
- 这是一个测试环境，生产环境的 API 可能会有所不同
- 使用前请确保您有适当的访问权限

© 2025 - RVMPlus 应用程序
