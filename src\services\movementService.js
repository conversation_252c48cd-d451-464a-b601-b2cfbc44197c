/**
 * Movement service for managing movement tracking and challenges
 */
import { ref } from 'vue';
import httpClient from '../utils/httpClient';
import {
  mockChallenges,
  mockActiveChallenge,
  mockMovementStats,
  simulateApiDelay,
  createApiResponse,
  createApiErrorResponse
} from './mockData';
import { useAuthService } from './authService';

// Configuration
const USE_MOCK_DATA = import.meta.env.VITE_USE_MOCK_DATA === 'true'; // Set to false to use real API

// Movement state
const movementStats = ref(null);
const challenges = ref([]);
const activeChallenge = ref(null);
const isLoading = ref(false);
const error = ref(null);

// Get auth service
const { isLoggedIn } = useAuthService();

/**
 * Get movement statistics
 * @returns {Promise<Object>} Movement statistics
 */
const getMovementStats = async () => {
  isLoading.value = true;
  error.value = null;

  try {
    if (!isLoggedIn.value) {
      throw new Error('User not authenticated');
    }

    if (USE_MOCK_DATA) {
      // Mock implementation
      await simulateApiDelay();

      movementStats.value = { ...mockMovementStats };
      return createApiResponse(movementStats.value);
    } else {
      // Real API implementation
      const response = await httpClient.get('api/movement/stats');

      if (response.success) {
        movementStats.value = response.data;
      }

      return response;
    }
  } catch (err) {
    error.value = err.message;
    return createApiErrorResponse('MOVEMENT_SERVICE_UNAVAILABLE', err.message);
  } finally {
    isLoading.value = false;
  }
};

/**
 * Get available challenges
 * @returns {Promise<Array>} List of available challenges
 */
const getChallenges = async () => {
  isLoading.value = true;
  error.value = null;

  try {
    if (!isLoggedIn.value) {
      throw new Error('User not authenticated');
    }

    if (USE_MOCK_DATA) {
      // Mock implementation
      await simulateApiDelay();

      challenges.value = [...mockChallenges];
      return createApiResponse(challenges.value);
    } else {
      // Real API implementation
      const response = await httpClient.get('api/movement/challenges');

      if (response.success) {
        challenges.value = response.data;
      }

      return response;
    }
  } catch (err) {
    error.value = err.message;
    return createApiErrorResponse('MOVEMENT_SERVICE_UNAVAILABLE', err.message);
  } finally {
    isLoading.value = false;
  }
};

/**
 * Start a challenge
 * @param {number} challengeId - Challenge ID
 * @returns {Promise<Object>} Active challenge
 */
const startChallenge = async (challengeId) => {
  isLoading.value = true;
  error.value = null;

  try {
    if (!isLoggedIn.value) {
      throw new Error('User not authenticated');
    }

    if (!challengeId) {
      throw new Error('Challenge ID is required');
    }

    if (USE_MOCK_DATA) {
      // Mock implementation
      await simulateApiDelay();

      // Check if challenge exists
      const challenge = mockChallenges.find(c => c.id === parseInt(challengeId));

      if (!challenge) {
        return createApiErrorResponse('CHALLENGE_NOT_FOUND', 'Challenge not found');
      }

      // Check if there's already an active challenge
      if (activeChallenge.value) {
        return createApiErrorResponse('CHALLENGE_ALREADY_ACTIVE', 'You already have an active challenge');
      }

      // Create active challenge
      const now = new Date();
      const endTime = new Date(now);
      endTime.setDate(endTime.getDate() + 1); // 24 hours challenge

      const newActiveChallenge = {
        challengeId: parseInt(challengeId),
        startTime: now.toISOString(),
        endTime: endTime.toISOString(),
        goal: challenge.goal,
        progress: 0,
        unit: challenge.unit,
        completed: false,
        timeLeft: '23:59:59'
      };

      activeChallenge.value = newActiveChallenge;

      return createApiResponse(newActiveChallenge, 'Challenge started successfully');
    } else {
      // Real API implementation
      const response = await httpClient.post(`api/movement/challenges/${challengeId}/start`);

      if (response.success) {
        activeChallenge.value = response.data;
      }

      return response;
    }
  } catch (err) {
    error.value = err.message;
    return createApiErrorResponse('CHALLENGE_START_FAILED', err.message);
  } finally {
    isLoading.value = false;
  }
};

/**
 * Update challenge progress
 * @param {Object} progressData - Progress data
 * @param {number} progressData.steps - Number of steps
 * @param {number} progressData.distance - Distance in meters
 * @returns {Promise<Object>} Updated challenge
 */
const updateChallengeProgress = async (progressData) => {
  isLoading.value = true;
  error.value = null;

  try {
    if (!isLoggedIn.value) {
      throw new Error('User not authenticated');
    }

    if (!progressData) {
      throw new Error('Progress data is required');
    }

    if (USE_MOCK_DATA) {
      // Mock implementation
      await simulateApiDelay();

      // Check if there's an active challenge
      if (!activeChallenge.value) {
        return createApiErrorResponse('NO_ACTIVE_CHALLENGE', 'No active challenge found');
      }

      // Validate progress data
      if (progressData.steps < 0) {
        return createApiErrorResponse('INVALID_STEPS', 'Steps cannot be negative');
      }

      if (progressData.distance < 0) {
        return createApiErrorResponse('INVALID_DISTANCE', 'Distance cannot be negative');
      }

      // Update progress
      const newProgress = activeChallenge.value.progress + progressData.steps;
      const completed = newProgress >= activeChallenge.value.goal;

      // Update active challenge
      activeChallenge.value = {
        ...activeChallenge.value,
        progress: newProgress,
        completed
      };

      return createApiResponse({
        challengeId: activeChallenge.value.challengeId,
        progress: newProgress,
        goal: activeChallenge.value.goal,
        completed,
        timeLeft: activeChallenge.value.timeLeft
      });
    } else {
      // Real API implementation
      const response = await httpClient.put('api/movement/challenges/active/progress', progressData);

      if (response.success) {
        // Update active challenge
        activeChallenge.value = {
          ...activeChallenge.value,
          progress: response.data.progress,
          completed: response.data.completed,
          timeLeft: response.data.timeLeft
        };
      }

      return response;
    }
  } catch (err) {
    error.value = err.message;
    return createApiErrorResponse('CHALLENGE_UPDATE_FAILED', err.message);
  } finally {
    isLoading.value = false;
  }
};

/**
 * Get active challenge
 * @returns {Promise<Object>} Active challenge
 */
const getActiveChallenge = async () => {
  isLoading.value = true;
  error.value = null;

  try {
    if (!isLoggedIn.value) {
      throw new Error('User not authenticated');
    }

    if (USE_MOCK_DATA) {
      // Mock implementation
      await simulateApiDelay();

      // For mock purposes, return the mock active challenge
      activeChallenge.value = { ...mockActiveChallenge };
      return createApiResponse(activeChallenge.value);
    } else {
      // Real API implementation
      const response = await httpClient.get('api/movement/challenges/active');

      if (response.success) {
        activeChallenge.value = response.data;
      } else {
        // If no active challenge
        activeChallenge.value = null;
      }

      return response;
    }
  } catch (err) {
    error.value = err.message;
    return createApiErrorResponse('MOVEMENT_SERVICE_UNAVAILABLE', err.message);
  } finally {
    isLoading.value = false;
  }
};

/**
 * Reset error state
 */
const resetError = () => {
  error.value = null;
};

// Export movement service
export const useMovementService = () => {
  return {
    // State
    movementStats,
    challenges,
    activeChallenge,
    isLoading,
    error,

    // Methods
    getMovementStats,
    getChallenges,
    startChallenge,
    updateChallengeProgress,
    getActiveChallenge,
    resetError
  };
};
