import { encodeURL } from './url';

/**
 * Opens a URL in the in-app browser
 * @param {object} router - Vue Router instance
 * @param {string} url - The URL to open
 * @param {string} title - The title to display in the browser header
 * @param {string} returnPath - The path to return to when closing the browser (default: '/profile')
 */
export const openInAppBrowser = (router, url, title, returnPath = '/profile') => {
  // Store the return path in sessionStorage
  window.sessionStorage.setItem('browserReturnPath', returnPath);

  // Navigate to the in-app browser with the URL and title as parameters
  router.push({
    name: 'InAppBrowser',
    params: {
      url: encodeURL(url),
      title: encodeURL(title)
    }
  });
};
