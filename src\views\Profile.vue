<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useAuth } from '../store/auth';
import { useTheme } from '../store/theme';
import { useI18n } from 'vue-i18n';
import { useUserService } from '../services/userService';
import { useNotification } from '../store/notification';
import ConfirmationModal from '../components/ConfirmationModal.vue';
import { useProfile } from '@/composables/useProfile';
import { usePublicBitmapRequest } from '@/composables/usePublicBitmapRequest';
import { encodeURL } from '../utils/url';

const router = useRouter();
const { t } = useI18n();

const { logout } = useAuth();
const { isDarkMode, toggleTheme } = useTheme();
const { updateProfilePicture } = useUserService();
const { showSuccess } = useNotification();
const { profile, fetchProfile } = useProfile();
const { fetchData: fetchBitmapData, isLoading: isBitmapLoading } = usePublicBitmapRequest();

// File input reference
const fileInput = ref(null);
const uploadingImage = ref(false);
const uploadError = ref(null);

// Default profile image
const defaultProfileImage = '/assets/profile-icon.svg';

// Confirmation modal state
const showDeleteConfirmation = ref(false);
const isDeleting = ref(false);

// Computed properties for theme display
const themeText = computed(() => isDarkMode.value ? t('profile.darkMode') : t('profile.lightMode'));

// Notification state
const notificationsEnabled = ref(true);
const toggleNotifications = () => {
  notificationsEnabled.value = !notificationsEnabled.value;
};

// Handle logout
const handleLogout = () => {
  logout();
  // Redirect to login page
  router.push('/login');
};

// Show delete account confirmation
const showDeleteAccountConfirmation = () => {
  showDeleteConfirmation.value = true;
};

// Cancel delete account
const cancelDeleteAccount = () => {
  showDeleteConfirmation.value = false;
};

// Confirm delete account
const confirmDeleteAccount = () => {
  isDeleting.value = true;

  // Simulate API call delay (would be replaced with actual API call)
  setTimeout(() => {
    // For now, just log out the user after "deleting" the account
    console.log('Account deletion would be processed here');

    // Show success notification
    showSuccess(t('profile.accountDeletedSuccess', 'Account deleted successfully'), {
      duration: 3000,
      position: 'top',
    });

    // Log out the user
    logout();

    // Reset state
    isDeleting.value = false;
    showDeleteConfirmation.value = false;

    // Redirect to login page
    router.push('/login');
  }, 1000);
};

// Edit profile
const editProfile = () => {
  router.push('/edit-profile');
};

// Handle theme toggle
const handleThemeToggle = () => {
  toggleTheme();
  console.log('Theme toggled:', isDarkMode.value ? 'Dark mode' : 'Light mode');
};

// Trigger file input click
const triggerFileUpload = () => {
  fileInput.value.click();
};

// Handle profile image change
const handleProfileImageChange = async (event) => {
  const file = event.target.files[0];
  if (!file) {return;}

  console.log('Selected file:', file.name, file.type, file.size);

  // Check if file is an image
  if (!file.type.match('image.*')) {
    uploadError.value = t('profile.invalidImageType');
    return;
  }

  // Check file size (max 5MB)
  if (file.size > 5 * 1024 * 1024) {
    uploadError.value = t('profile.imageTooLarge');
    return;
  }

  try {
    uploadingImage.value = true;
    uploadError.value = null;

    console.log('Uploading profile picture...');

    // Upload profile picture
    const result = await updateProfilePicture(file);
    console.log('Profile picture upload result:', result);

    if (!result.success) {
      uploadError.value = result.message || t('profile.updateFailed');
    } else {
      console.log('Profile picture updated successfully');
      showSuccess(t('profile.profilePictureUpdated', 'Profile picture updated successfully'), {
        duration: 3000,
        position: 'top',
      });
      // Refresh profile data after successful upload
      await fetchProfile();
    }
  } catch (error) {
    console.error('Error uploading profile picture:', error);
    uploadError.value = t('profile.updateFailed');
  } finally {
    uploadingImage.value = false;
    // Clear file input
    event.target.value = '';
  }
};

// Load user profile data on mount
onMounted(async () => {
  await fetchProfile();
});

// Watch and log profile changes for debugging
watch(profile, (newProfile) => {
  console.log('Profile updated:', newProfile);
}, { immediate: true });

const openInAppBrowser = async (type) => {
  let url = '';
  let title = '';
  const res = await fetchBitmapData(16, type);
  url = res?.data?.[0]?.UrlLink || '';
  title = type;
  if (url) {
    router.push({
      name: 'InAppBrowser',
      params: {
        url: encodeURL(url),
        title: encodeURL(title),
      },
    });
  } else {
    showSuccess('No URL found for this action');
  }
};
</script>

<template>
  <div class="profile-container">
    <!-- User Info Section -->
    <div class="user-info-section">
      <div class="avatar-container">
        <img :src="profile?.profileImage || defaultProfileImage" alt="Profile" class="avatar" />
        <div class="edit-avatar-button" @click="triggerFileUpload">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path>
          </svg>
          <div v-if="uploadingImage" class="loading-spinner"></div>
        </div>
        <!-- Hidden file input -->
        <input
          ref="fileInput"
          type="file"
          accept="image/*"
          style="display: none"
          @change="handleProfileImageChange"
        />
      </div>

      <h2 class="user-name">{{ profile?.name || 'Demo User' }}</h2>
      <p class="user-phone">{{ profile?.phone || '' }}</p>
      <p class="user-email">{{ profile?.email || '' }}</p>
      <p v-if="profile?.gender" class="user-gender">{{ profile.gender === 'M' ? t('profile.male', 'Male') : profile.gender === 'F' ? t('profile.female', 'Female') : profile.gender }}</p>
      <p v-if="uploadError" class="upload-error">{{ uploadError }}</p>
    </div>

    <!-- Menu Items -->
    <div class="menu-list">
      <div class="menu-item" @click="editProfile">
        <div class="menu-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
            <path d="M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
          </svg>
        </div>
        <span class="menu-text">{{ t('profile.editProfileInfo') }}</span>
      </div>

      <div class="menu-item" @click="toggleNotifications">
        <div class="menu-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
            <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
          </svg>
        </div>
        <span class="menu-text">{{ t('profile.notifications') }}</span>
        <span class="menu-value">{{ notificationsEnabled ? t('profile.notificationsOn') : t('profile.notificationsOff') }}</span>
      </div>

      <div class="menu-item" @click="router.push('/security')">
        <div class="menu-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
            <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
          </svg>
        </div>
        <span class="menu-text">{{ t('profile.security', 'Security') }}</span>
        <span class="menu-arrow">›</span>
      </div>

      <div class="menu-item" @click="handleThemeToggle">
        <div class="menu-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="5"></circle>
            <path d="M12 1v2M12 21v2M4.2 4.2l1.4 1.4M18.4 18.4l1.4 1.4M1 12h2M21 12h2M4.2 19.8l1.4-1.4M18.4 5.6l1.4-1.4"></path>
          </svg>
        </div>
        <span class="menu-text">{{ t('profile.theme') }}</span>
        <span class="menu-value">{{ themeText }}</span>
      </div>

      <div class="menu-item" @click="router.push('/help')">
        <div class="menu-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
            <line x1="12" y1="17" x2="12.01" y2="17"></line>
          </svg>
        </div>
        <span class="menu-text">{{ t('common.help', 'Help & Support') }}</span>
        <span class="menu-arrow">›</span>
      </div>

      <div class="menu-item" @click="() => openInAppBrowser('About')">
        <div class="menu-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>
        </div>
        <span class="menu-text">About</span>
        <span class="menu-arrow">›</span>
      </div>
      <div class="menu-item" @click="() => openInAppBrowser('Usage')">
        <div class="menu-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><path d="M16 3v4a1 1 0 0 0 1 1h4"></path></svg>
        </div>
        <span class="menu-text">Usage</span>
        <span class="menu-arrow">›</span>
      </div>
      <div class="menu-item" @click="() => openInAppBrowser('Contact')">
        <div class="menu-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
          </svg>
        </div>
        <span class="menu-text">{{ t('common.contact', 'Contact us') }}</span>
        <span class="menu-arrow">›</span>
      </div>
      <div class="menu-item" @click="() => openInAppBrowser('Privacy')">
        <div class="menu-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
            <path d="M12 11c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"></path>
            <path d="M12 17v3"></path>
            <path d="M12 4v3"></path>
          </svg>
        </div>
        <span class="menu-text">{{ t('profile.privacyPolicy') }}</span>
        <span class="menu-arrow">›</span>
      </div>

      <!-- Delete Account Option -->
      <div class="menu-item delete-account-item" @click="showDeleteAccountConfirmation">
        <div class="menu-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#ff5252" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M3 6h18"></path>
            <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
            <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
            <line x1="10" y1="11" x2="10" y2="17"></line>
            <line x1="14" y1="11" x2="14" y2="17"></line>
          </svg>
        </div>
        <span class="menu-text">{{ t('profile.deleteAccount', 'Delete Account') }}</span>
      </div>
    </div>

    <div class="logout-button-container">
      <button class="logout-button" @click="handleLogout">
        {{ t('profile.logout') }}
      </button>
    </div>

    <!-- Delete Account Confirmation Modal -->
    <ConfirmationModal
      :show="showDeleteConfirmation"
      :title="t('profile.deleteAccountConfirmation', 'Delete Account')"
      :message="t('profile.deleteAccountMessage', 'Are you sure you want to delete your account? This action cannot be undone.')"
      :confirm-text="isDeleting ? t('common.processing', 'Processing...') : t('profile.deleteAccount', 'Delete Account')"
      :cancel-text="t('common.cancel', 'Cancel')"
      :is-danger="true"
      @confirm="confirmDeleteAccount"
      @cancel="cancelDeleteAccount"
    />
  </div>
</template>

<style scoped>
.profile-container {
  padding: 0;
  width: 100%;
  margin: 0 auto;
  padding-bottom: 7rem;
  background-color: var(--background-color);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-y: auto;
}

.user-info-section {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem 1rem;
  background-color: var(--primary-color);
  border-bottom-left-radius: 360px;
  border-bottom-right-radius: 360px;
  margin-bottom: 1rem;
  width: 100%;
}

.avatar-container {
  position: relative;
  margin-bottom: 0.5rem;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.edit-avatar-button {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  color: var(--primary-color);
}

.user-name {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0.5rem 0 0.25rem;
  color: white;
}

.user-phone {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.user-email, .user-gender {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.upload-error {
  font-size: 0.8rem;
  color: #ff5252;
  margin-top: 0.5rem;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.loading-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.menu-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0 1rem;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: var(--card-background);
  border-radius: 8px;
  margin-bottom: 0.5rem;
  cursor: pointer;
  position: relative;
  min-height: 3.5rem;
}

.menu-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-right: 1rem;
  color: #aaa;
}

.menu-text {
  flex: 1;
  font-size: 0.95rem;
  color: var(--text-color);
}

.menu-value {
  color: var(--primary-color);
  font-size: 0.9rem;
  margin-left: auto;
}

.menu-arrow {
  font-size: 1.5rem;
  color: var(--text-light);
  margin-left: 0.5rem;
}

.logout-button-container {
  padding: 1rem;
  margin-top: auto;
  width: 100%;
}

.logout-button {
  width: 100%;
  background-color: transparent;
  color: #ff5252;
  border: 1px solid #ff5252;
  padding: 0.8rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.logout-button:hover {
  background-color: rgba(255, 82, 82, 0.1);
}

/* Delete account styling */
.delete-account-item {
  color: #ff5252;
}

.delete-account-item .menu-icon {
  color: #ff5252;
}

/* Media queries for responsive design */
@media (max-width: 480px) {
  .profile-container {
    padding-bottom: 120px;
    padding-top: env(safe-area-inset-top, 0px);
  }

  .user-info-section {
    padding-top: max(env(safe-area-inset-top), 1.5rem);
  }

  .menu-item {
    margin-bottom: 0.5rem;
  }
}

@media (min-width: 769px) {
  .profile-container {
    max-width: 480px;
    margin: 0 auto;
    height: 100%;
    overflow-y: auto;
  }

  .user-info-section {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }

  .menu-list {
    max-width: 480px;
    margin: 0 auto;
  }

  .logout-button-container {
    max-width: 480px;
    margin: 2rem auto;
  }
}
</style>
