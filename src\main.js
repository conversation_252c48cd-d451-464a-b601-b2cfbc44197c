import { createApp } from 'vue';
import './style.css';
import App from './App.vue';
import router from './router';
import { useTheme } from './store/theme';
import i18n from './i18n';
import { logEnvInfo } from './utils/envConfig';
import { createLogger } from './utils/logger';

// Create logger for main app
const logger = createLogger('App');

// Initialize theme before app is mounted
// This ensures the correct theme is applied from the start
const { isDarkMode } = useTheme();

// Log environment information in development mode
logEnvInfo();

// Create and mount the app
logger.log('Initializing RVMPlus Rewards application');
const app = createApp(App);
app.use(router);
app.use(i18n);
app.mount('#app');
logger.log('Application mounted successfully');
