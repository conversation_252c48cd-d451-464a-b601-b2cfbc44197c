import { createI18n } from 'vue-i18n';
import en from './locales/en.json';
import ms from './locales/ms.json';
import zh from './locales/zh.json';
import { updateHtmlLang } from '../utils/htmlLang';

// Get the saved language or default to English
const savedLanguage = localStorage.getItem('language') || 'en';

// Update HTML lang attribute
updateHtmlLang(savedLanguage);

// Create i18n instance
const i18n = createI18n({
  legacy: false, // Use Composition API
  locale: savedLanguage, // Default locale
  fallbackLocale: 'en', // Fallback locale
  messages: {
    en,
    ms,
    zh
  }
});

// Watch for language changes and update HTML lang attribute
if (i18n.global) {
  i18n.global.locale.value = savedLanguage;
}

export default i18n;
