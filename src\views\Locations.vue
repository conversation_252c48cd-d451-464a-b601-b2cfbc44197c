<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useLocation } from '../store/location';
import { useTheme } from '../store/theme';
import { useI18n } from 'vue-i18n';

// Get theme state
const { isDarkMode } = useTheme();
const { t } = useI18n();

// Get location state and methods
const {
  currentPosition,
  nearbyLocations,
  locationError,
  isLoading,
  isTracking,
  placeName,
  isLoadingPlaceName,
  initializeLocation,
  startLocationTracking,
  stopTracking,
  updateNearbyLocations,
} = useLocation();

// UI state
const showPermissionPrompt = ref(false);
const trackingEnabled = ref(false);

// Service icons
const RecycleIcon = new URL('../assets/recycle-icon.svg', import.meta.url).href;
const CollectIcon = new URL('../assets/collect-icon.svg', import.meta.url).href;
const DisposeIcon = new URL('../assets/dispose-icon.svg', import.meta.url).href;

// Display location name or loading state
const locationDisplay = computed(() => {
  if (isLoadingPlaceName.value) {return t('locations.gettingLocationName');}
  if (placeName.value) {return placeName.value;}
  if (!currentPosition.value) {return t('locations.unknownLocation');}

  // Fallback to coordinates if no place name is available
  const { latitude, longitude } = currentPosition.value;
  return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
});

// Initialize location on component mount
onMounted(async () => {
  try {
    const position = await initializeLocation();
    if (position) {
      showPermissionPrompt.value = false;
    } else {
      showPermissionPrompt.value = true;
    }
  } catch (error) {
    showPermissionPrompt.value = true;
    console.error('Error initializing location:', error);
  }
});

// Clean up on component unmount
onUnmounted(() => {
  if (isTracking.value) {
    stopTracking();
  }
});

// Toggle location tracking
const toggleTracking = async () => {
  if (trackingEnabled.value) {
    stopTracking();
    trackingEnabled.value = false;
  } else {
    const success = await startLocationTracking();
    trackingEnabled.value = success;
  }
};

// Request location permission
const requestLocationPermission = async () => {
  try {
    const position = await initializeLocation();
    if (position) {
      showPermissionPrompt.value = false;
    }
  } catch (error) {
    console.error('Error requesting location permission:', error);
  }
};

// Refresh location data
const refreshLocation = async () => {
  try {
    await initializeLocation();
    updateNearbyLocations();
  } catch (error) {
    console.error('Error refreshing location:', error);
  }
};

// Get icon for service
const getServiceIcon = (service) => {
  switch (service) {
    case 'recycle':
      return RecycleIcon;
    case 'collect':
      return CollectIcon;
    case 'dispose':
      return DisposeIcon;
    default:
      return '';
  }
};
</script>

<template>
  <div class="locations-container">
    <!-- Current Location Section -->
    <div class="current-location-section">
      <div class="section-header">
        <h2 class="section-heading">{{ t('locations.currentLocation') }}</h2>
        <button class="refresh-button" :disabled="isLoading" @click="refreshLocation">
          <span class="refresh-icon" :class="{ 'refreshing': isLoading }">↻</span>
        </button>
      </div>

      <div v-if="currentPosition" class="current-location-column">
        <div class="location-display">
          <p class="location-value">{{ locationDisplay }}</p>
        </div>
        <div class="tracking-controls">
          <label class="toggle-switch">
            <input v-model="trackingEnabled" type="checkbox" @change="toggleTracking">
            <span class="toggle-slider"></span>
          </label>
          <span class="tracking-label">{{ trackingEnabled ? t('locations.trackingEnabled') : t('locations.enableTracking') }}</span>
        </div>
      </div>

      <div v-else-if="locationError" class="location-error">
        <p>{{ locationError }}</p>
        <button class="permission-button" @click="requestLocationPermission">
          {{ t('locations.grantAccess') }}
        </button>
      </div>

      <div v-else-if="isLoading" class="location-loading">
        <div class="spinner"></div>
        <p>{{ t('locations.gettingLocation') }}</p>
      </div>

      <div v-else-if="showPermissionPrompt" class="permission-prompt">
        <p>{{ t('locations.locationPermission') }}</p>
        <button class="permission-button" @click="requestLocationPermission">
          {{ t('locations.grantAccess') }}
        </button>
      </div>
    </div>

    <!-- Nearby Locations Section -->
    <h2 class="section-title">{{ t('locations.nearbyLocations') }}</h2>

    <div class="locations-list">
      <template v-if="nearbyLocations.length > 0">
        <div
          v-for="location in nearbyLocations"
          :key="location.id"
          class="location-card"
        >
          <div class="location-name">{{ location.name }}</div>

          <div class="location-services">
            <div v-for="service in location.services" :key="service" class="service-icon">
              <img :src="getServiceIcon(service)" :alt="service" />
            </div>
          </div>

          <div class="location-address">{{ location.address }}</div>

          <div v-if="location.distance" class="location-distance">
            <span class="distance-value">{{ location.distanceText }}</span> {{ t('locations.away') }}
          </div>
        </div>
        <!-- Spacer div to ensure scrolling to the end -->
        <div class="scroll-spacer"></div>
      </template>

      <div v-else-if="locationError" class="empty-state">
        <p>{{ locationError }}</p>
        <button class="permission-button" @click="requestLocationPermission">
          {{ t('locations.grantAccess') }}
        </button>
      </div>

      <div v-else-if="isLoading" class="empty-state">
        <div class="spinner"></div>
        <p>{{ t('locations.loadingLocations') }}</p>
      </div>

      <div v-else class="empty-state">
        <p>{{ t('locations.noLocations') }}</p>
        <p class="empty-state-subtitle">{{ t('locations.tryEnabling') }}</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.locations-container {
  padding: 1.5rem;
  max-width: 480px;
  margin: 0 auto;
  margin-bottom: 5rem; /* Space for bottom navigation */
  background-color: var(--background-color);
  height: 100vh; /* Full viewport height */
  overflow: hidden; /* Hide overflow */
  display: flex;
  flex-direction: column;
  width: 100%; /* Ensure full width */
}

/* Title removed - using the main Header.vue component */

.section-title {
  font-size: 1.4rem;
  font-weight: 600;
  margin: 2rem 0 1rem;
  color: var(--text-color);
}

.current-location-section {
  background-color: var(--card-background);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  width: 100%;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-heading {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.current-location-column {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.location-display {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.location-value {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
  padding: 0.25rem 0;
}

.refresh-button {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.refresh-button:hover {
  background-color: var(--highlight-color);
}

.refresh-button:disabled {
  color: var(--text-light);
  cursor: not-allowed;
}

.refresh-icon {
  display: inline-block;
  transition: transform 0.5s;
}

.refreshing {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.tracking-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.tracking-label {
  font-size: 0.9rem;
  color: var(--text-color);
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--border-color);
  transition: .4s;
  border-radius: 20px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: var(--card-background);
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

.location-error, .permission-prompt, .location-loading {
  text-align: center;
  padding: 1rem 0;
}

.location-error p, .permission-prompt p {
  margin-bottom: 1rem;
  color: var(--text-light);
}

.permission-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.permission-button:hover {
  background-color: var(--primary-dark);
}

.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

.locations-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: calc(100vh - 250px);
  overflow-y: auto;
  padding-right: 0.5rem;
  margin-bottom: 1rem;
  padding-bottom: 6rem; /* Add extra padding at the bottom to ensure scrolling to the end */
}

.location-card {
  background-color: var(--card-background);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 1rem;
}

.location-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--text-color);
}

.location-services {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.service-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.service-icon img {
  width: 40px;
  height: 40px;
}

.location-address {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 1rem;
}

.location-distance {
  font-size: 0.9rem;
  color: var(--text-light);
}

.distance-value {
  font-weight: 600;
  color: var(--primary-color);
}

.empty-state {
  background-color: var(--card-background);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  color: var(--text-light);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.empty-state-subtitle {
  font-size: 0.9rem;
  margin-top: 0.5rem;
  opacity: 0.7;
}

.scroll-spacer {
  height: 100px; /* Add extra space at the bottom of the list */
  width: 100%;
}

/* Responsive styles */
@media (max-width: 480px) {
  .locations-container {
    padding: 1rem;
    padding-top: 0.5rem;
  }

  .locations-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    padding-top: 1rem;
  }

  .section-title {
    font-size: 1.2rem;
    margin: 1.5rem 0 1rem;
  }

  .current-location-section {
    padding: 1.25rem;
    margin-bottom: 1.25rem;
  }

  .location-card {
    padding: 1.25rem;
    margin-bottom: 0.75rem;
  }

  .location-services {
    margin-bottom: 0.75rem;
  }

  .location-address {
    margin-bottom: 0.75rem;
  }

  .locations-list {
    max-height: calc(100vh - 230px);
    padding-bottom: 12rem; /* Increase padding to ensure scrolling to the end on mobile */
    -webkit-overflow-scrolling: touch; /* Improve scrolling on iOS */
    margin-bottom: 2rem;
  }
}
</style>
