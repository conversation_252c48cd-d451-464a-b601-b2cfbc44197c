<script setup>
import { useRoute } from 'vue-router';
import { useTheme } from '../store/theme';
import { useI18n } from 'vue-i18n';

// Use SVG icons from public folder
const HomeIcon = '/assets/home-nav-icon.svg';
const VoucherIcon = '/assets/voucher-nav-icon.svg';
const QrCodeIcon = '/assets/qrcode-nav-icon.svg';
const LocationIcon = '/assets/location-nav-icon.svg';
const ProfileIcon = '/assets/profile-nav-icon.svg';

const route = useRoute();
const { isDarkMode } = useTheme();
const { t } = useI18n();
</script>

<template>
  <nav class="bottom-nav" :class="{ 'dark-nav': isDarkMode, 'light-nav': !isDarkMode }">
    <router-link to="/" class="nav-item" :class="{ active: route.name === 'Home' }">
      <img :src="HomeIcon" alt="Home" class="nav-icon" />
      <span class="nav-label">{{ t('navigation.home') }}</span>
    </router-link>
    <router-link to="/vouchers" class="nav-item" :class="{ active: route.name === 'Vouchers' }">
      <img :src="VoucherIcon" alt="Vouchers" class="nav-icon" />
      <span class="nav-label">{{ t('navigation.vouchers') }}</span>
    </router-link>
    <router-link to="/qrcode" class="qr-code-container">
      <div class="qr-code-button">
        <img :src="QrCodeIcon" alt="QR Code" class="qr-icon" />
        <span class="qr-label">{{ t('navigation.qrcode') }}</span>
      </div>
    </router-link>
    <router-link to="/locations" class="nav-item" :class="{ active: route.name === 'Locations' }">
      <img :src="LocationIcon" alt="Locations" class="nav-icon" />
      <span class="nav-label">{{ t('navigation.locations') }}</span>
    </router-link>
    <router-link to="/profile" class="nav-item" :class="{ active: route.name === 'Profile' }">
      <img :src="ProfileIcon" alt="Profile" class="nav-icon" />
      <span class="nav-label">{{ t('navigation.profile') }}</span>
    </router-link>
  </nav>
</template>

<style scoped>
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0.5rem 0;
  padding-bottom: calc(0.5rem + env(safe-area-inset-bottom, 0px)); /* Add safe area inset for notched phones */
  z-index: 100;
  max-width: 540px;
  margin: 0 auto;
  transition: background-color 0.3s ease;
}

.dark-nav {
  background-color: #121212;
  border-top: 1px solid #333;
}

.light-nav {
  background-color: #ffffff;
  border-top: 1px solid #e0e0e0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  padding: 0.5rem;
  flex: 1;
  position: relative;
  transition: color 0.3s ease;
}

.dark-nav .nav-item {
  color: #aaa;
}

.light-nav .nav-item {
  color: #666;
}

.nav-icon {
  width: 22px;
  height: 22px;
  margin-bottom: 0.25rem;
  opacity: 0.7;
  transition: opacity 0.3s ease, filter 0.3s ease;
}

.dark-nav .nav-icon {
  filter: invert(1);
}

.nav-label {
  font-size: 0.7rem;
  font-weight: 500;
  white-space: nowrap;
}

.nav-item.active {
  color: var(--primary-color);
}

.nav-item.active .nav-icon {
  opacity: 1;
  filter: invert(75%) sepia(41%) saturate(544%) hue-rotate(131deg) brightness(92%) contrast(87%);
}

.dark-nav .nav-item.active .nav-icon {
  filter: invert(75%) sepia(41%) saturate(544%) hue-rotate(131deg) brightness(92%) contrast(87%);
}

.qr-code-container {
  position: relative;
  display: flex;
  justify-content: center;
  margin-top: -30px;
  flex: 1;
  text-decoration: none;
}

.qr-code-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--primary-color);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  padding: 0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.qr-icon {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
  margin-bottom: 0.1rem;
}

.qr-label {
  font-size: 0.6rem;
  color: white;
  font-weight: 500;
}

/* Responsive styles */
@media (max-width: 480px) {
  .bottom-nav {
    padding: 0.4rem 0;
    padding-bottom: calc(0.4rem + env(safe-area-inset-bottom, 0px));
  }

  .nav-icon {
    width: 20px;
    height: 20px;
  }

  .nav-label {
    font-size: 0.65rem;
  }

  .qr-code-button {
    width: 55px;
    height: 55px;
  }

  .qr-icon {
    width: 22px;
    height: 22px;
  }

  .qr-label {
    font-size: 0.55rem;
  }
}

@media (min-width: 769px) {
  .bottom-nav {
    position: fixed;
    max-width: 540px;
  }
}
</style>
