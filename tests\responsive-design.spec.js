import { test, expect } from '@playwright/test';

/**
 * Comprehensive test suite for responsive design
 * Tests both mobile and desktop viewports across all main pages
 */
test.describe('Responsive Design Tests', () => {
  // Mock login function to bypass authentication
  async function mockLogin(page) {
    await page.goto('/login');
    
    // Mock the authentication state using localStorage
    await page.evaluate(() => {
      localStorage.setItem('isAuthenticated', 'true');
      localStorage.setItem('user', JSON.stringify({
        id: '123',
        name: 'Test User',
        phone: '+601234567890',
        points: 1000
      }));
    });
    
    // Navigate to home page
    await page.goto('/');
  }

  // Define viewport sizes for testing
  const viewports = {
    mobile: { width: 375, height: 667 },  // iPhone SE / common mobile size
    tablet: { width: 768, height: 1024 }, // iPad / tablet size
    desktop: { width: 1280, height: 800 } // Standard desktop size
  };

  // Test home page responsiveness
  test('Home page should be responsive across different viewports', async ({ page }) => {
    await mockLogin(page);
    
    // Test on mobile viewport
    await page.setViewportSize(viewports.mobile);
    await expect(page.locator('.main-content')).toBeVisible();
    
    // Check that points card is properly sized for mobile
    const pointsCardMobile = page.locator('.points-card');
    await expect(pointsCardMobile).toBeVisible();
    const pointsCardMobileBounds = await pointsCardMobile.boundingBox();
    expect(pointsCardMobileBounds.width).toBeLessThanOrEqual(viewports.mobile.width);
    expect(pointsCardMobileBounds.width).toBeGreaterThan(viewports.mobile.width * 0.8); // Should take up most of the width
    
    // Test on desktop viewport
    await page.setViewportSize(viewports.desktop);
    
    // Check that the app container has desktop styling (centered with max-width)
    const appContainer = page.locator('#app');
    const appContainerBounds = await appContainer.boundingBox();
    expect(appContainerBounds.width).toBeLessThan(viewports.desktop.width); // Should not be full width on desktop
  });

  // Test profile page responsiveness
  test('Profile page should be responsive across different viewports', async ({ page }) => {
    await mockLogin(page);
    await page.goto('/profile');
    
    // Test on mobile viewport
    await page.setViewportSize(viewports.mobile);
    
    // Check profile container visibility and sizing
    const profileContainer = page.locator('.profile-container');
    await expect(profileContainer).toBeVisible();
    
    // Check that settings sections have proper mobile styling
    const settingsSection = page.locator('.settings-section').first();
    await expect(settingsSection).toBeVisible();
    const settingsSectionBounds = await settingsSection.boundingBox();
    expect(settingsSectionBounds.width).toBeGreaterThan(viewports.mobile.width * 0.8); // Should take up most of the width
    
    // Test on desktop viewport
    await page.setViewportSize(viewports.desktop);
    
    // Check that profile container has desktop styling (centered with max-width)
    const profileContainerDesktop = await profileContainer.boundingBox();
    expect(profileContainerDesktop.width).toBeLessThan(viewports.desktop.width); // Should not be full width on desktop
    
    // Verify logout button is visible on both viewports
    await expect(page.locator('.logout-button')).toBeVisible();
  });

  // Test vouchers page responsiveness
  test('Vouchers page should be responsive across different viewports', async ({ page }) => {
    await mockLogin(page);
    await page.goto('/vouchers');
    
    // Test on mobile viewport
    await page.setViewportSize(viewports.mobile);
    
    // Check vouchers container visibility and sizing
    const vouchersContainer = page.locator('.vouchers-container');
    await expect(vouchersContainer).toBeVisible();
    
    // Check that voucher cards have proper mobile styling
    const voucherCard = page.locator('.voucher-card').first();
    if (await voucherCard.count() > 0) {
      const voucherCardBounds = await voucherCard.boundingBox();
      expect(voucherCardBounds.width).toBeLessThanOrEqual(viewports.mobile.width);
      expect(voucherCardBounds.width).toBeGreaterThan(viewports.mobile.width * 0.8); // Should take up most of the width
    }
    
    // Test on desktop viewport
    await page.setViewportSize(viewports.desktop);
    
    // Check that vouchers container has desktop styling (centered with max-width)
    if (await voucherCard.count() > 0) {
      const voucherCardDesktop = await voucherCard.boundingBox();
      expect(voucherCardDesktop.width).toBeLessThan(viewports.desktop.width); // Should not be full width on desktop
    }
  });

  // Test locations page responsiveness
  test('Locations page should be responsive across different viewports', async ({ page }) => {
    await mockLogin(page);
    await page.goto('/locations');
    
    // Test on mobile viewport
    await page.setViewportSize(viewports.mobile);
    
    // Check locations container visibility and sizing
    const locationsContainer = page.locator('.locations-container');
    await expect(locationsContainer).toBeVisible();
    
    // Check that location cards have proper mobile styling
    const locationCard = page.locator('.location-card').first();
    if (await locationCard.count() > 0) {
      const locationCardBounds = await locationCard.boundingBox();
      expect(locationCardBounds.width).toBeLessThanOrEqual(viewports.mobile.width);
      expect(locationCardBounds.width).toBeGreaterThan(viewports.mobile.width * 0.8); // Should take up most of the width
    }
    
    // Check that locations list is scrollable on mobile
    const locationsList = page.locator('.locations-list');
    if (await locationsList.count() > 0) {
      const isScrollable = await locationsList.evaluate(el => {
        return el.scrollHeight > el.clientHeight;
      });
      // Note: This might be true or false depending on the number of locations
      // We're just checking that the property exists and is calculated
      expect(typeof isScrollable).toBe('boolean');
    }
    
    // Test on desktop viewport
    await page.setViewportSize(viewports.desktop);
    
    // Check that locations container has desktop styling (centered with max-width)
    const locationsContainerDesktop = await locationsContainer.boundingBox();
    expect(locationsContainerDesktop.width).toBeLessThan(viewports.desktop.width); // Should not be full width on desktop
  });

  // Test QR code page responsiveness
  test('QR code page should be responsive across different viewports', async ({ page }) => {
    await mockLogin(page);
    await page.goto('/qrcode');
    
    // Test on mobile viewport
    await page.setViewportSize(viewports.mobile);
    
    // Check QR code container visibility
    const qrCodeContainer = page.locator('.qr-code-container, .qr-container');
    await expect(qrCodeContainer).toBeVisible();
    
    // Test on desktop viewport
    await page.setViewportSize(viewports.desktop);
    
    // Check that QR code container is still visible and properly sized on desktop
    await expect(qrCodeContainer).toBeVisible();
    const qrCodeContainerDesktop = await qrCodeContainer.boundingBox();
    expect(qrCodeContainerDesktop.width).toBeLessThan(viewports.desktop.width); // Should not be full width on desktop
  });

  // Test login page responsiveness (no authentication needed)
  test('Login page should be responsive across different viewports', async ({ page }) => {
    await page.goto('/login');
    
    // Test on mobile viewport
    await page.setViewportSize(viewports.mobile);
    
    // Check login container visibility
    const loginContainer = page.locator('.login-container');
    await expect(loginContainer).toBeVisible();
    
    // Check that phone input has proper mobile styling
    const phoneInput = page.locator('.phone-input-wrapper');
    await expect(phoneInput).toBeVisible();
    const phoneInputBounds = await phoneInput.boundingBox();
    expect(phoneInputBounds.width).toBeLessThanOrEqual(viewports.mobile.width);
    expect(phoneInputBounds.width).toBeGreaterThan(viewports.mobile.width * 0.7); // Should take up most of the width
    
    // Test on desktop viewport
    await page.setViewportSize(viewports.desktop);
    
    // Check that login container has desktop styling (centered with max-width)
    const loginContainerDesktop = await loginContainer.boundingBox();
    expect(loginContainerDesktop.width).toBeLessThan(viewports.desktop.width); // Should not be full width on desktop
  });

  // Test content scrolling behavior
  test('Content should be scrollable on small viewports', async ({ page }) => {
    await mockLogin(page);
    
    // Set a very small viewport height to ensure scrolling is needed
    await page.setViewportSize({ width: viewports.mobile.width, height: 400 });
    
    // Check home page scrolling
    await page.goto('/');
    const contentContainer = page.locator('.content-container');
    await expect(contentContainer).toBeVisible();
    
    // Check that the content container has scrollable content
    const hasScrollableContent = await contentContainer.evaluate(el => {
      return el.scrollHeight > el.clientHeight;
    });
    
    // Should be scrollable on a small viewport
    expect(hasScrollableContent).toBeTruthy();
    
    // Check that we can scroll to the bottom
    await page.evaluate(() => {
      document.querySelector('.content-container').scrollTo(0, 1000);
    });
    
    // Wait a moment for the scroll to complete
    await page.waitForTimeout(300);
    
    // Get the current scroll position
    const scrollPosition = await page.evaluate(() => {
      return document.querySelector('.content-container').scrollTop;
    });
    
    // Should have scrolled down
    expect(scrollPosition).toBeGreaterThan(0);
  });
});
