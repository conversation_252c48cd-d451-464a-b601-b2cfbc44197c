<script setup>
import { ref, onMounted } from 'vue';
import PasswordLogin from '../components/PasswordLogin.vue';

const isLoaded = ref(false);

// Add a small delay to ensure smooth transitions
onMounted(() => {
  // Disable scrolling on the content container
  const contentContainer = document.querySelector('.content-container');
  if (contentContainer) {
    contentContainer.style.overflow = 'hidden';
  }

  // Add class to body to prevent scrolling
  document.body.classList.add('login-page');

  setTimeout(() => {
    isLoaded.value = true;
  }, 100);

  // Clean up when component is unmounted
  return () => {
    if (contentContainer) {
      contentContainer.style.overflow = 'auto';
    }
    document.body.classList.remove('login-page');
  };
});
</script>

<template>
  <div class="login-container" :class="{ 'is-loaded': isLoaded }">
    <div class="login-logo">
      <img src="/logo.svg" alt="RVMPlus Logo" class="logo-image" />
    </div>
    <div class="login-card">
      <PasswordLogin />
    </div>
  </div>
</template>

<style scoped>
.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  height: 100vh; /* Fixed height instead of min-height */
  width: 100%;
  padding: env(safe-area-inset-top) 1rem 1rem 1rem;
  background-color: var(--background-color);
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.3s ease, transform 0.3s ease;
  overflow: hidden; /* Prevent scrolling */
  position: fixed; /* Fix position to prevent scrolling */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.login-container.is-loaded {
  opacity: 1;
  transform: translateY(0);
}

.login-logo {
  margin: 2rem 0;
  text-align: center;
}

.logo-image {
  max-width: 150px;
  height: auto;
}

.login-card {
  width: 100%;
  max-width: 480px;
  background-color: var(--card-background);
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 2rem;
  max-height: calc(100vh - 150px); /* Ensure it fits in viewport */
  display: flex;
  flex-direction: column;
}

/* Mobile responsive styles */
@media (max-width: 480px) {
  .login-container {
    padding-top: max(env(safe-area-inset-top), 2rem);
    justify-content: flex-start;
  }

  .login-logo {
    margin: 1rem 0 1.5rem;
  }

  .logo-image {
    max-width: 120px;
  }
}

/* Responsive styles */
@media (max-width: 480px) {
  .login-container {
    padding: 0;
  }

  .login-card {
    border-radius: 0;
    box-shadow: none;
    height: 100vh; /* Fixed height */
    max-height: 100vh; /* Ensure it doesn't exceed viewport */
    overflow: hidden; /* Prevent scrolling */
  }
}

@media (min-width: 769px) {
  .login-card {
    border: 1px solid var(--border-color);
  }
}
</style>
