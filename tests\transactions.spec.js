import { test, expect } from '@playwright/test';

test.describe('Transactions Page', () => {
  // Helper function for login (skipped for now)
  async function login(page) {
    // This is a mock login function for testing
    await page.goto('/login');

    // Mock the login process
    await page.evaluate(() => {
      // Mock the authentication state
      localStorage.setItem('isAuthenticated', 'true');
      localStorage.setItem('user', JSON.stringify({
        id: '123',
        name: 'Test User',
        phone: '+601234567890',
        points: 1000
      }));
    });

    // Navigate to home page
    await page.goto('/');
  }

  // Skip all tests in this file
  test.beforeEach(async ({ page }) => {
    // Skip login for now
    test.skip();
  });

  test('should display transactions header and title', async ({ page }) => {
    // Check that the transactions title is displayed
    await expect(page.locator('h1.transactions-title')).toBeVisible();

    // Check that the back button is visible
    await expect(page.locator('button.back-button')).toBeVisible();
  });

  test('should display all transactions by default', async ({ page }) => {
    // Check that the "All" tab is active by default
    const allTab = page.locator('.tab-button', { hasText: 'All' }).first();
    await allTab.waitFor({ state: 'visible' });
    const hasActiveClass = await allTab.evaluate(el => el.classList.contains('active'));
    expect(hasActiveClass).toBeTruthy();

    // Check that all transactions are displayed
    const transactionCards = page.locator('.transaction-card');
    await transactionCards.first().waitFor({ state: 'visible' });
    const count = await transactionCards.count();
    expect(count).toBe(4); // Based on the sample data
  });

  test('should filter transactions when clicking on tabs', async ({ page }) => {
    // Wait for tabs to be visible
    await page.locator('.tab-navigation').waitFor({ state: 'visible' });

    // Click on "Collected" tab using JavaScript click to avoid interception
    const collectedTab = page.locator('.tab-button', { hasText: 'Collected' });
    await collectedTab.evaluate(tab => tab.click());

    // Wait for filtering to complete
    await page.waitForTimeout(500);

    // Check that only "Collected" transactions are displayed
    const collectedTransactions = page.locator('.transaction-card:has-text("Collected")');
    const collectedCount = await collectedTransactions.count();
    expect(collectedCount).toBe(3); // Based on the sample data

    // Click on "Spent" tab using JavaScript click to avoid interception
    const spentTab = page.locator('.tab-button', { hasText: 'Spent' });
    await spentTab.evaluate(tab => tab.click());

    // Wait for filtering to complete
    await page.waitForTimeout(500);

    // Check that only "Spent" transactions are displayed
    const spentTransactions = page.locator('.transaction-card:has-text("Spent")');
    const spentCount = await spentTransactions.count();
    expect(spentCount).toBe(1); // Based on the sample data
  });

  test('should navigate back when clicking back button', async ({ page }) => {
    // Click on back button using JavaScript click to avoid interception
    const backButton = page.locator('button.back-button');
    await backButton.waitFor({ state: 'visible' });
    await backButton.evaluate(button => button.click());

    // Should navigate back to home page
    await page.waitForURL('/');
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set viewport to mobile size
    await page.setViewportSize({ width: 375, height: 667 });

    // Check that the transactions container adapts to mobile size
    const transactionsContainer = page.locator('.transactions-container');
    await expect(transactionsContainer).toBeVisible();

    // Check that tab navigation has proper mobile styling
    const tabNavigation = page.locator('.tab-navigation');
    await tabNavigation.waitFor({ state: 'visible' });
    const boundingBox = await tabNavigation.boundingBox();

    // On mobile, the width should be close to the viewport width
    expect(boundingBox.width).toBeLessThanOrEqual(375);
    expect(boundingBox.width).toBeGreaterThan(300);
  });
});
