{"name": "reward", "private": true, "version": "1.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:staging": "vite build --mode staging", "build:production": "vite build --mode production", "preview": "vite preview", "test": "playwright test", "test:ui": "playwright test --ui", "test:headed": "playwright test --headed", "lint": "eslint src", "lint:fix": "eslint src --fix", "format": "prettier --write \"src/**/*.{js,vue,css}\""}, "dependencies": {"axios": "^1.9.0", "pinia": "^3.0.2", "qrcode.vue": "^3.6.0", "vue": "^3.5.13", "vue-i18n": "^9.14.4", "vue-qrcode-reader": "^5.7.2", "vue-router": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.25.1", "@playwright/test": "^1.52.0", "@vitejs/plugin-vue": "^5.2.2", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-vue": "^10.0.0", "prettier": "^3.5.3", "terser": "^5.39.2", "vite": "^6.3.1"}}