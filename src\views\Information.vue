<script setup>
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import InformationPage from '../components/InformationPage.vue';
import { useI18n } from 'vue-i18n';

const route = useRoute();
const { t } = useI18n();

// Get the information type from the route params
const infoType = computed(() => route.params.type || 'redeem');

// Define content for different information pages using translations
const getPageContent = (type) => {
  switch (type) {
    case 'redeem':
      return {
        title: t('information.redeem.title'),
        subtitle: t('information.redeem.subtitle'),
        type: 'INFO',
        content: {
          introText: t('information.redeem.introText'),
          stepsTitle: t('information.redeem.stepsTitle'),
          stepsIntro: t('information.redeem.stepsIntro'),
          steps: [
            {
              title: t('information.redeem.step1Title'),
              description: t('information.redeem.step1Desc')
            },
            {
              title: t('information.redeem.step2Title'),
              description: t('information.redeem.step2Desc')
            },
            {
              title: t('information.redeem.step3Title'),
              description: t('information.redeem.step3Desc')
            }
          ]
        }
      };
    case 'use':
      return {
        title: t('information.use.title'),
        subtitle: t('information.use.subtitle'),
        type: 'INFO',
        content: {
          introText: t('information.use.introText'),
          stepsTitle: t('information.use.stepsTitle'),
          stepsIntro: t('information.use.stepsIntro'),
          steps: [
            {
              title: t('information.use.step1Title'),
              description: t('information.use.step1Desc')
            },
            {
              title: t('information.use.step2Title'),
              description: t('information.use.step2Desc')
            },
            {
              title: t('information.use.step3Title'),
              description: t('information.use.step3Desc')
            },
            {
              title: t('information.use.step4Title'),
              description: t('information.use.step4Desc')
            },
            {
              title: t('information.use.step5Title'),
              description: t('information.use.step5Desc')
            },
            {
              title: t('information.use.step6Title'),
              description: t('information.use.step6Desc')
            }
          ]
        }
      };
    case 'byc':
      return {
        title: t('information.byc.title'),
        subtitle: t('information.byc.subtitle'),
        type: 'INFO',
        content: {
          introText: t('information.byc.introText'),
          stepsTitle: t('information.byc.stepsTitle'),
          stepsIntro: t('information.byc.stepsIntro'),
          steps: [
            {
              title: t('information.byc.step1Title'),
              description: t('information.byc.step1Desc')
            },
            {
              title: t('information.byc.step2Title'),
              description: t('information.byc.step2Desc')
            },
            {
              title: t('information.byc.step3Title'),
              description: t('information.byc.step3Desc')
            },
            {
              title: t('information.byc.step4Title'),
              description: t('information.byc.step4Desc')
            },
            {
              title: t('information.byc.step5Title'),
              description: t('information.byc.step5Desc')
            },
            {
              title: t('information.byc.step6Title'),
              description: t('information.byc.step6Desc')
            }
          ],
          whyTitle: t('information.byc.whyTitle'),
          whyItMatters: [
            t('information.byc.benefit1'),
            t('information.byc.benefit2')
          ]
        }
      };
    case 'campaign':
      return {
        title: t('information.campaign.title'),
        subtitle: t('information.campaign.subtitle'),
        type: 'CAMPAIGN',
        content: {
          introText: t('information.campaign.introText'),
          stepsTitle: t('information.campaign.stepsTitle'),
          stepsIntro: t('information.campaign.stepsIntro'),
          steps: [
            {
              title: t('information.campaign.step1Title'),
              description: t('information.campaign.step1Desc')
            },
            {
              title: t('information.campaign.step2Title'),
              description: t('information.campaign.step2Desc')
            },
            {
              title: t('information.campaign.step3Title'),
              description: t('information.campaign.step3Desc')
            },
            {
              title: t('information.campaign.step4Title'),
              description: t('information.campaign.step4Desc')
            },
            {
              title: t('information.campaign.step5Title'),
              description: t('information.campaign.step5Desc')
            }
          ],
          whyTitle: t('information.campaign.whyTitle'),
          whyItMatters: [
            t('information.campaign.benefit1'),
            t('information.campaign.benefit2')
          ]
        }
      };
    default:
      return getPageContent('redeem');
  }
};

// Get the content based on the info type
const pageContent = computed(() => {
  return getPageContent(infoType.value);
});
</script>

<template>
  <InformationPage
    :title="pageContent.title"
    :subtitle="pageContent.subtitle"
    :type="pageContent.type"
    :content="pageContent.content"
  />
</template>
