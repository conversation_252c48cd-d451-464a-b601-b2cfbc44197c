<template>
  <div class="api-example">
    <h2>API Example</h2>

    <!-- Loading indicator -->
    <div v-if="isLoading" class="loading">
      Loading...
    </div>

    <!-- Error message -->
    <div v-if="error" class="error">
      <p>{{ error }}</p>
      <button @click="resetError">Dismiss</button>
    </div>

    <!-- Authentication section -->
    <div class="section">
      <h3>Authentication</h3>
      <div v-if="!isLoggedIn">
        <input v-model="phone" placeholder="Phone number" />
        <input v-model="password" type="password" placeholder="Password" />
        <button @click="loginUser" :disabled="isLoading">Login</button>
      </div>
      <div v-else>
        <p>Logged in as: {{ user?.name }}</p>
        <button @click="logout">Logout</button>
      </div>
    </div>

    <!-- User Profile section -->
    <div v-if="isLoggedIn" class="section">
      <h3>User Profile</h3>
      <button @click="fetchUserProfile" :disabled="isLoading">Fetch Profile</button>
      <div v-if="profile">
        <p>Name: {{ profile.name }}</p>
        <p>Email: {{ profile.email }}</p>
        <p>Points: {{ profile.points }}</p>
      </div>
    </div>

    <!-- Locations section -->
    <div v-if="isLoggedIn" class="section">
      <h3>Locations</h3>
      <button @click="fetchLocations" :disabled="isLoading">Fetch Locations</button>
      <ul v-if="locations.length > 0">
        <li v-for="location in locations.slice(0, 3)" :key="location.id">
          {{ location.name }} - {{ location.distanceText || 'Distance not available' }}
          <button @click="fetchLocationDetails(location.id)" :disabled="isLoading">
            Details
          </button>
        </li>
      </ul>
      <div v-if="selectedLocation">
        <h4>{{ selectedLocation.name }}</h4>
        <p>{{ selectedLocation.address }}</p>
        <p>Services: {{ selectedLocation.services?.join(', ') }}</p>
      </div>
    </div>

    <!-- Vouchers section -->
    <div v-if="isLoggedIn" class="section">
      <h3>Vouchers</h3>
      <button @click="fetchVouchers" :disabled="isLoading">Fetch Vouchers</button>
      <ul v-if="vouchers.length > 0">
        <li v-for="voucher in vouchers" :key="voucher.id">
          {{ voucher.title }} - {{ voucher.status }}
          <button @click="fetchVoucherDetails(voucher.id)" :disabled="isLoading">
            Details
          </button>
          <button
            v-if="voucher.status === 'Active'"
            @click="redeemVoucher(voucher.id)"
            :disabled="isLoading"
          >
            Redeem
          </button>
        </li>
      </ul>
      <div v-if="selectedVoucher">
        <h4>{{ selectedVoucher.title }}</h4>
        <p>{{ selectedVoucher.subtitle }}</p>
        <p>Expires: {{ selectedVoucher.expiryDate }}</p>
      </div>
    </div>

    <!-- Transactions section -->
    <div v-if="isLoggedIn" class="section">
      <h3>Transactions</h3>
      <button @click="fetchTransactions" :disabled="isLoading">Fetch Transactions</button>
      <ul v-if="transactions.length > 0">
        <li v-for="transaction in transactions" :key="transaction.id">
          {{ transaction.type }} - {{ transaction.amount }} - {{ transaction.date }}
          <button @click="fetchTransactionDetails(transaction.id)" :disabled="isLoading">
            Details
          </button>
        </li>
      </ul>
      <div v-if="selectedTransaction">
        <h4>{{ selectedTransaction.type }}</h4>
        <p>Amount: {{ selectedTransaction.amount }}</p>
        <p>Date: {{ selectedTransaction.date }} {{ selectedTransaction.time }}</p>
        <p>Category: {{ selectedTransaction.category }}</p>
      </div>
    </div>

    <!-- Forest section -->
    <div v-if="isLoggedIn" class="section">
      <h3>Forest</h3>
      <button @click="fetchForestStats" :disabled="isLoading">Fetch Forest Stats</button>
      <div v-if="forestStats">
        <p>Stage: {{ forestStats.currentTreeStage?.name }}</p>
        <p>CO2 Absorbed: {{ forestStats.co2Absorbed }} / {{ forestStats.co2Target }}</p>
        <p>Progress: {{ Math.round(forestStats.progressPercentage) }}%</p>
        <p>Trees Grown: {{ forestStats.totalTreesGrown }}</p>
        <button @click="updateForest" :disabled="isLoading">Add Recycled Items</button>
      </div>
    </div>

    <!-- Movement section -->
    <div v-if="isLoggedIn" class="section">
      <h3>Movement</h3>
      <button @click="fetchMovementStats" :disabled="isLoading">Fetch Movement Stats</button>
      <div v-if="movementStats">
        <p>Steps Walked: {{ movementStats.stepsWalked }}</p>
        <p>Points Earned: {{ movementStats.pointsEarned }}</p>
        <p>Challenges Completed: {{ movementStats.challengesCompleted }}</p>
      </div>

      <h4>Challenges</h4>
      <button @click="fetchChallenges" :disabled="isLoading">Fetch Challenges</button>
      <ul v-if="challenges.length > 0">
        <li v-for="challenge in challenges" :key="challenge.id">
          {{ challenge.title }} - {{ challenge.reward }} points
          <button @click="startChallenge(challenge.id)" :disabled="isLoading || !!activeChallenge">
            Start
          </button>
        </li>
      </ul>

      <div v-if="activeChallenge">
        <h4>Active Challenge</h4>
        <p>Goal: {{ activeChallenge.progress }} / {{ activeChallenge.goal }} {{ activeChallenge.unit }}</p>
        <p>Time Left: {{ activeChallenge.timeLeft }}</p>
        <button @click="updateChallengeProgress" :disabled="isLoading">Add Steps</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import {
  useAuthService,
  useUserService,
  useApiLocationService,
  useVoucherService,
  useTransactionService,
  useForestService,
  useMovementService
} from '../../services';

// Auth service
const {
  user,
  isLoggedIn,
  error: authError,
  isLoading: authLoading,
  login,
  logout
} = useAuthService();

// User service
const {
  profile,
  isLoading: userLoading,
  error: userError,
  getUserProfile,
  resetError: resetUserError
} = useUserService();

// Location service
const {
  locations,
  selectedLocation,
  isLoading: locationLoading,
  error: locationError,
  getLocations,
  getLocationDetails,
  resetError: resetLocationError
} = useApiLocationService();

// Voucher service
const {
  vouchers,
  selectedVoucher,
  isLoading: voucherLoading,
  error: voucherError,
  getVouchers,
  getVoucherDetails,
  redeemVoucher: redeemVoucherApi,
  resetError: resetVoucherError
} = useVoucherService();

// Transaction service
const {
  transactions,
  selectedTransaction,
  isLoading: transactionLoading,
  error: transactionError,
  getTransactions,
  getTransactionDetails,
  resetError: resetTransactionError
} = useTransactionService();

// Forest service
const {
  forestStats,
  isLoading: forestLoading,
  error: forestError,
  getForestStats,
  updateForestStats,
  resetError: resetForestError
} = useForestService();

// Movement service
const {
  movementStats,
  challenges,
  activeChallenge,
  isLoading: movementLoading,
  error: movementError,
  getMovementStats,
  getChallenges,
  startChallenge: startChallengeApi,
  updateChallengeProgress: updateChallengeProgressApi,
  getActiveChallenge,
  resetError: resetMovementError
} = useMovementService();

// Form data
const phone = ref('');
const password = ref('');

// Combined loading and error states
const isLoading = ref(false);
const error = ref('');

// Watch loading states
const updateLoadingState = () => {
  isLoading.value =
    authLoading.value ||
    userLoading.value ||
    locationLoading.value ||
    voucherLoading.value ||
    transactionLoading.value ||
    forestLoading.value ||
    movementLoading.value;
};

// Watch error states
const updateErrorState = () => {
  error.value =
    authError.value ||
    userError.value ||
    locationError.value ||
    voucherError.value ||
    transactionError.value ||
    forestError.value ||
    movementError.value ||
    '';
};

// Reset all errors
const resetError = () => {
  resetUserError();
  resetLocationError();
  resetVoucherError();
  resetTransactionError();
  resetForestError();
  resetMovementError();
  error.value = '';
};

// Auth methods
const loginUser = async () => {
  if (!phone.value) {
    error.value = 'Please enter a phone number';
    return;
  }

  if (!password.value) {
    error.value = 'Please enter a password';
    return;
  }

  await login(phone.value, password.value);
  updateErrorState();

  if (isLoggedIn.value) {
    // Reset form
    phone.value = '';
    password.value = '';
  }
};

// User methods
const fetchUserProfile = async () => {
  const response = await getUserProfile();
  updateErrorState();
  return response;
};

// Location methods
const fetchLocations = async () => {
  // Get current position if available
  let coords = null;
  try {
    const position = await new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(resolve, reject);
    });

    coords = {
      latitude: position.coords.latitude,
      longitude: position.coords.longitude
    };
  } catch (err) {
    console.warn('Geolocation not available:', err);
  }

  const response = await getLocations(coords);
  updateErrorState();
  return response;
};

const fetchLocationDetails = async (id) => {
  const response = await getLocationDetails(id);
  updateErrorState();
  return response;
};

// Voucher methods
const fetchVouchers = async () => {
  const response = await getVouchers();
  updateErrorState();
  return response;
};

const fetchVoucherDetails = async (id) => {
  const response = await getVoucherDetails(id);
  updateErrorState();
  return response;
};

const redeemVoucher = async (id) => {
  const response = await redeemVoucherApi(id);
  updateErrorState();

  if (response.success) {
    // Refresh vouchers list
    await fetchVouchers();
  }

  return response;
};

// Transaction methods
const fetchTransactions = async () => {
  const response = await getTransactions();
  updateErrorState();
  return response;
};

const fetchTransactionDetails = async (id) => {
  const response = await getTransactionDetails(id);
  updateErrorState();
  return response;
};

// Forest methods
const fetchForestStats = async () => {
  const response = await getForestStats();
  updateErrorState();
  return response;
};

const updateForest = async () => {
  const response = await updateForestStats({
    co2Amount: 100,
    recycledItems: 10
  });
  updateErrorState();
  return response;
};

// Movement methods
const fetchMovementStats = async () => {
  const response = await getMovementStats();
  updateErrorState();
  return response;
};

const fetchChallenges = async () => {
  const response = await getChallenges();
  updateErrorState();

  // Also fetch active challenge if any
  await fetchActiveChallenge();

  return response;
};

const fetchActiveChallenge = async () => {
  const response = await getActiveChallenge();
  updateErrorState();
  return response;
};

const startChallenge = async (id) => {
  const response = await startChallengeApi(id);
  updateErrorState();
  return response;
};

const updateChallengeProgress = async () => {
  const response = await updateChallengeProgressApi({
    steps: 500,
    distance: 400
  });
  updateErrorState();
  return response;
};

// Initialize component
onMounted(async () => {
  updateLoadingState();
  updateErrorState();

  // If user is logged in, fetch initial data
  if (isLoggedIn.value) {
    await fetchUserProfile();
    await fetchLocations();
  }
});
</script>

<style scoped>
.api-example {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.loading {
  padding: 10px;
  background-color: #f0f0f0;
  border-radius: 4px;
  margin-bottom: 15px;
}

.error {
  padding: 10px;
  background-color: #ffebee;
  color: #c62828;
  border-radius: 4px;
  margin-bottom: 15px;
}

button {
  margin: 5px;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  background-color: #1976d2;
  color: white;
  cursor: pointer;
}

button:disabled {
  background-color: #bbdefb;
  cursor: not-allowed;
}

input {
  padding: 8px;
  margin: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

ul {
  list-style-type: none;
  padding: 0;
}

li {
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}
</style>
