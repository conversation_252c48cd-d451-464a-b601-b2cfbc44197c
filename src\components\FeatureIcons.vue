<script setup>
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';

// Feature icons component
// Use URL imports for PNG files
const ReceiptIcon = new URL('../assets/transactions_logo.png', import.meta.url).href;
const RunIcon = new URL('../assets/running_icon.png', import.meta.url).href;
const PlantIcon = new URL('../assets/forest.png', import.meta.url).href;

const router = useRouter();
const { t } = useI18n();

// Navigation functions
const goToTransactions = () => {
  router.push('/transactions');
};

const goToMove = () => {
  router.push('/move');
};

const goToForest = () => {
  router.push('/forest');
};
</script>

<template>
  <div class="feature-icons">
    <div class="feature-item" @click="goToTransactions">
      <div class="icon-container">
        <img :src="ReceiptIcon" :alt="t('features.transactions')" class="feature-icon" />
      </div>
      <span class="feature-label">{{ t('features.transactions') }}</span>
    </div>
    <div class="feature-item" @click="goToMove">
      <div class="icon-container">
        <img :src="RunIcon" :alt="t('features.rvmMove')" class="feature-icon" />
      </div>
      <span class="feature-label">{{ t('features.rvmMove') }}</span>
    </div>
    <div class="feature-item" @click="goToForest">
      <div class="icon-container">
        <img :src="PlantIcon" :alt="t('features.rvmForest')" class="feature-icon" />
      </div>
      <span class="feature-label">{{ t('features.rvmForest') }}</span>
    </div>
  </div>
</template>

<style scoped>
.feature-icons {
  display: flex;
  justify-content: space-around;
  padding: 1rem;
  margin-bottom: 1rem;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: transform 0.2s;
}

.feature-item:hover {
  transform: translateY(-2px);
}

.icon-container {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--primary-light);
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-icon {
  width: 28px;
  height: 28px;
  filter: brightness(0) invert(1);
}

.feature-label {
  font-size: 0.85rem;
  font-weight: 500;
  color: var(--text-color);
  text-align: center;
}

/* Responsive styles */
@media (min-width: 769px) {
  .feature-icons {
    max-width: 600px;
    margin: 0 auto 1rem auto;
  }

  .icon-container {
    width: 70px;
    height: 70px;
  }

  .feature-icon {
    width: 32px;
    height: 32px;
  }

  .feature-label {
    font-size: 1rem;
  }
}
</style>
