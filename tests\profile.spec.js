import { test, expect } from '@playwright/test';

test.describe('Profile Page', () => {
  // Helper function for login (skipped for now)
  async function login(page) {
    // This is a mock login function for testing
    await page.goto('/login');

    // Mock the login process
    await page.evaluate(() => {
      // Mock the authentication state
      localStorage.setItem('isAuthenticated', 'true');
      localStorage.setItem('user', JSON.stringify({
        id: '123',
        name: 'Test User',
        phone: '+601234567890',
        points: 1000
      }));
    });

    // Navigate to home page
    await page.goto('/');
  }

  // Skip all tests for now until we have a working backend
  test.beforeEach(async ({ page }) => {
    test.skip(true, 'Skipping profile tests until we have a working backend');
  });

  test('should display user information correctly', async ({ page }) => {
    // Check that user name and phone are displayed
    await expect(page.locator('.user-name')).toBeVisible();
    await expect(page.locator('.user-name')).toHaveText('Test User');

    // Check for phone number
    await expect(page.locator('.user-phone')).toBeVisible();
    await expect(page.locator('.user-phone')).toHaveText('1234567890');

    // Check that avatar is displayed
    await expect(page.locator('.avatar')).toBeVisible();
  });

  test('should toggle notifications setting', async ({ page }) => {
    // Get initial state
    const notificationsItem = page.locator('.settings-item', { hasText: 'Notifications' });
    await expect(notificationsItem).toBeVisible();

    const notificationValue = page.locator('.settings-item:has-text("Notifications") .setting-value');
    await expect(notificationValue).toBeVisible();

    const initialState = await notificationValue.textContent();
    expect(initialState).toBeTruthy();

    // Click on notifications setting
    await notificationsItem.click();

    // Wait for state change
    await page.waitForTimeout(300);

    // Check that state has changed
    const newState = await notificationValue.textContent();
    expect(newState).not.toEqual(initialState);

    // Toggle back
    await notificationsItem.click();
    await page.waitForTimeout(300);

    // Should be back to initial state
    const finalState = await notificationValue.textContent();
    expect(finalState).toEqual(initialState);
  });

  test('should toggle theme setting', async ({ page }) => {
    // Get initial theme state
    const themeItem = page.locator('.settings-item', { hasText: 'Theme' });
    await expect(themeItem).toBeVisible();

    const themeValue = page.locator('.settings-item:has-text("Theme") .setting-value');
    await expect(themeValue).toBeVisible();

    const initialTheme = await themeValue.textContent();
    expect(initialTheme).toBeTruthy();

    // Click on theme setting
    await themeItem.click();

    // Wait for theme change
    await page.waitForTimeout(300);

    // Check that theme has changed
    const newTheme = await themeValue.textContent();
    expect(newTheme).not.toEqual(initialTheme);

    // Toggle back
    await themeItem.click();
    await page.waitForTimeout(300);

    // Should be back to initial theme
    const finalTheme = await themeValue.textContent();
    expect(finalTheme).toEqual(initialTheme);
  });

  test('should have a visible logout button', async ({ page }) => {
    // Verify the logout button exists and is properly styled
    const logoutButton = page.locator('.logout-button');
    await expect(logoutButton).toBeVisible();
    await expect(logoutButton).toHaveText('Logout');

    // Check that the divider above logout is visible
    await expect(page.locator('.divider')).toBeVisible();

    // Check that the logout button has the correct styling
    const buttonColor = await logoutButton.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return styles.color;
    });

    // Should have a red/error color
    expect(buttonColor).toBeTruthy();
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set viewport to mobile size
    await page.setViewportSize({ width: 375, height: 667 });

    // Check that the profile container adapts to mobile size
    const profileContainer = page.locator('.profile-container');
    await expect(profileContainer).toBeVisible();

    // Check that settings sections have proper mobile styling
    const settingsSection = page.locator('.settings-section').first();
    await expect(settingsSection).toBeVisible();

    const boundingBox = await settingsSection.boundingBox();

    // On mobile, the width should be close to the viewport width (94% of 375)
    expect(boundingBox.width).toBeGreaterThan(340); // ~94% of 375 is ~352
    expect(boundingBox.width).toBeLessThan(375);

    // Check that setting values are visible
    await expect(page.locator('.setting-value').first()).toBeVisible();

    // Check that the avatar is properly sized for mobile
    const avatarBox = await page.locator('.avatar').boundingBox();
    expect(avatarBox.width).toBeLessThanOrEqual(90); // Should be 85px or less on mobile
  });

  test('should have scrollable content on mobile', async ({ page }) => {
    // Set viewport to a small mobile size to ensure scrolling is needed
    await page.setViewportSize({ width: 375, height: 500 });

    // Get the content container
    const contentContainer = page.locator('.content-container');
    await expect(contentContainer).toBeVisible();

    // Check that the content container has scrollable content
    const hasScrollableContent = await contentContainer.evaluate(el => {
      return el.scrollHeight > el.clientHeight;
    });

    expect(hasScrollableContent).toBeTruthy();

    // Check that we can scroll to the bottom
    await page.evaluate(() => {
      document.querySelector('.content-container').scrollTo(0, 1000);
    });

    // Wait for scroll to complete
    await page.waitForTimeout(300);

    // Check that the logout button is now visible in the viewport
    const logoutButtonVisible = await page.locator('.logout-button').evaluate(el => {
      const rect = el.getBoundingClientRect();
      return rect.top >= 0 && rect.bottom <= window.innerHeight;
    });

    expect(logoutButtonVisible).toBeTruthy();
  });

  test('should display all settings items correctly', async ({ page }) => {
    // Check that all settings items are visible
    const settingsItems = page.locator('.settings-item');
    const count = await settingsItems.count();

    // We should have at least 6 settings items (Edit profile, Notifications, Language, Security, Theme, Help & Support, Contact us, Privacy policy)
    expect(count).toBeGreaterThanOrEqual(6);

    // Check that each item has an icon
    for (let i = 0; i < count; i++) {
      const item = settingsItems.nth(i);
      await expect(item.locator('.settings-icon')).toBeVisible();

      // Check that the item has a label
      const hasLabel = await item.locator('span').first().isVisible();
      expect(hasLabel).toBeTruthy();
    }
  });

  test('should have a functional bottom navigation', async ({ page }) => {
    // Check that bottom navigation is visible
    const bottomNav = page.locator('.bottom-nav');
    await expect(bottomNav).toBeVisible();

    // Check that it has the correct number of items
    const navItems = bottomNav.locator('.nav-item');
    const itemCount = await navItems.count();
    expect(itemCount).toBeGreaterThanOrEqual(4); // Home, Vouchers, QR Code, Locations, Profile

    // Check that the Profile item is active
    const profileItem = bottomNav.locator('.nav-item', { hasText: 'Profile' });
    await expect(profileItem).toBeVisible();

    const isActive = await profileItem.evaluate(el => {
      return el.classList.contains('active') ||
             window.getComputedStyle(el).color !== window.getComputedStyle(document.querySelector('.nav-item')).color;
    });

    expect(isActive).toBeTruthy();

    // Check that clicking on Home navigates to home page
    const homeItem = bottomNav.locator('.nav-item', { hasText: 'Home' });
    await homeItem.click();

    // Should navigate to home
    await page.waitForURL('/');
  });
});
