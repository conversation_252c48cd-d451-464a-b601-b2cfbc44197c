# In-App Browser Implementation

This document explains how to use the in-app browser feature for displaying external content within the app.

## Overview

The in-app browser allows you to display external web content within the app using iframes. This is useful for displaying content like Help & Support, Contact Us, and Privacy Policy pages without redirecting users to an external browser.

## Features

- Display external web content within the app
- Support for both dark and light modes
- Close button to return to the previous page
- Loading indicator while content is loading
- Responsive design for both mobile and desktop

## How to Use

### 1. Opening the In-App Browser

To open the in-app browser, use the `openInAppBrowser` function from `utils/browser.js`:

```javascript
import { openInAppBrowser } from '../utils/browser';

// Open the in-app browser with a URL and title
openInAppBrowser(router, url, title, returnPath);
```

Parameters:
- `router`: Vue Router instance
- `url`: The URL to open in the in-app browser
- `title`: The title to display in the browser header
- `returnPath`: (Optional) The path to return to when closing the browser (default: '/profile')

### 2. Sample Pages

The repository includes sample HTML pages in the `sample-pages` directory that you can use for testing:

- `help-support.html`: Sample Help & Support page
- `contact-us.html`: Sample Contact Us page
- `privacy-policy.html`: Sample Privacy Policy page

To use these pages:

1. Host them on a service like GitHub Pages, Vercel, or Netlify
2. Update the URLs in the respective components to point to your hosted pages

### 3. Using GitHub HTML Preview

For quick testing, you can use GitHub HTML Preview:

```javascript
const url = 'https://htmlpreview.github.io/?https://github.com/yourusername/RewardRedemption/blob/main/sample-pages/help-support.html';
```

Replace `yourusername` with your actual GitHub username.

## Implementation Details

### Components

- `InAppBrowser.vue`: The main component that displays the iframe
- `HelpSupportPage.vue`: Component that opens the Help & Support page in the in-app browser
- `ContactUsPage.vue`: Component that opens the Contact Us page in the in-app browser
- `PrivacyPolicyPage.vue`: Component that opens the Privacy Policy page in the in-app browser

### Utilities

- `utils/url.js`: Contains functions for encoding and decoding URLs
- `utils/browser.js`: Contains the `openInAppBrowser` function

### Router Configuration

The router is configured with routes for each page and the in-app browser:

```javascript
{
  path: '/help',
  name: 'Help',
  component: () => import('../components/HelpSupportPage.vue'),
  meta: { requiresAuth: true, title: 'Help & Support' }
},
{
  path: '/contact',
  name: 'Contact',
  component: () => import('../components/ContactUsPage.vue'),
  meta: { requiresAuth: true, title: 'Contact Us' }
},
{
  path: '/privacy',
  name: 'Privacy',
  component: () => import('../components/PrivacyPolicyPage.vue'),
  meta: { requiresAuth: true, title: 'Privacy Policy' }
},
{
  path: '/browser/:url/:title',
  name: 'InAppBrowser',
  component: InAppBrowser,
  meta: { requiresAuth: true, title: 'Browser' },
  props: true
}
```

## Customization

### Styling

You can customize the appearance of the in-app browser by modifying the CSS in `InAppBrowser.vue`.

### Content

To display your own content, create HTML pages with responsive design and dark mode support, then update the URLs in the respective components.

## Troubleshooting

### Content Not Loading

If the content is not loading in the iframe, check the following:

1. Make sure the URL is correct and accessible
2. Ensure the server hosting the content allows embedding via iframes (X-Frame-Options header)
3. Check for CORS issues in the browser console

### Close Button Not Working

If the close button is not working correctly:

1. Make sure the `returnPath` parameter is set correctly
2. Check that the router is properly configured
3. Verify that there are no navigation guards preventing the navigation
