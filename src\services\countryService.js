// Country service to fetch and manage country data
import { ref } from 'vue';
import localCountryCodes from '../data/country-codes.json';
import localCountryNames from '../data/country-names.json';

// Cached data
const countryCodes = ref({});
const countryNames = ref({});
const isLoading = ref(false);
const error = ref(null);

// Fetch country phone codes from local data
const fetchCountryCodes = async () => {
  if (Object.keys(countryCodes.value).length > 0) {
    return countryCodes.value; // Return cached data if available
  }

  isLoading.value = true;
  error.value = null;

  try {
    console.log('Loading country codes from local data');
    countryCodes.value = localCountryCodes;
    return localCountryCodes;
  } catch (err) {
    error.value = 'Failed to load country codes.';
    console.error('Error loading country codes:', err);
    return {}; // Return empty object on error
  } finally {
    isLoading.value = false;
  }
};

// Fetch country names from local data
const fetchCountryNames = async () => {
  if (Object.keys(countryNames.value).length > 0) {
    return countryNames.value; // Return cached data if available
  }

  isLoading.value = true;
  error.value = null;

  try {
    console.log('Loading country names from local data');
    countryNames.value = localCountryNames;
    return localCountryNames;
  } catch (err) {
    error.value = 'Failed to load country names.';
    console.error('Error loading country names:', err);
    return {}; // Return empty object on error
  } finally {
    isLoading.value = false;
  }
};

// Get combined country data with codes and names
const getCountryData = async () => {
  const codes = await fetchCountryCodes();
  const names = await fetchCountryNames();

  const countries = [];

  // Combine data into a usable format
  for (const [code, phoneCode] of Object.entries(codes)) {
    // Only add countries that have both a code and a name
    if (code && (names[code] || code)) {
      countries.push({
        code,
        name: names[code] || code,
        phoneCode: phoneCode.startsWith('+') ? phoneCode : `+${phoneCode}`,
      });
    }
  }

  // Log the number of countries found
  console.log(`Combined ${countries.length} countries with codes and names`);

  // Sort by country name
  return countries.sort((a, b) => a.name.localeCompare(b.name));
};

// Reset any errors
const resetError = () => {
  error.value = null;
};

export const useCountryService = () => {
  return {
    countryCodes,
    countryNames,
    isLoading,
    error,
    fetchCountryCodes,
    fetchCountryNames,
    getCountryData,
    resetError,
  };
};
