/**
 * Transaction service for managing transaction history
 */
import { ref } from 'vue';
import httpClient from '../utils/httpClient';
import { mockTransactions, simulateApiDelay, createApiResponse, createApiErrorResponse } from './mockData';
import { useAuthService } from './authService';

// Configuration
const USE_MOCK_DATA = import.meta.env.VITE_USE_MOCK_DATA === 'true'; // Set to false to use real API

// Transaction state
const transactions = ref([]);
const selectedTransaction = ref(null);
const pagination = ref({
  total: 0,
  page: 1,
  limit: 20,
  totalPages: 1
});
const isLoading = ref(false);
const error = ref(null);

// Get auth service
const { isLoggedIn } = useAuthService();

/**
 * Get transactions
 * @param {Object} options - Query options
 * @param {string} options.category - Filter by category (All, Collected, Spent) (optional)
 * @param {number} options.page - Page number (optional, default: 1)
 * @param {number} options.limit - Number of items per page (optional, default: 20)
 * @returns {Promise<Object>} Transactions with pagination
 */
const getTransactions = async (options = {}) => {
  isLoading.value = true;
  error.value = null;

  try {
    if (!isLoggedIn.value) {
      throw new Error('User not authenticated');
    }

    const { category, page = 1, limit = 20 } = options;

    if (USE_MOCK_DATA) {
      // Mock implementation
      await simulateApiDelay();

      // Filter transactions by category if provided
      let filteredTransactions = [...mockTransactions];

      if (category && category.toLowerCase() !== 'all') {
        filteredTransactions = filteredTransactions.filter(transaction =>
          transaction.category.toLowerCase() === category.toLowerCase()
        );
      }

      // Calculate pagination
      const total = filteredTransactions.length;
      const totalPages = Math.ceil(total / limit);
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;

      // Get transactions for current page
      const paginatedTransactions = filteredTransactions.slice(startIndex, endIndex);

      // Update state
      transactions.value = paginatedTransactions;
      pagination.value = {
        total,
        page,
        limit,
        totalPages
      };

      return createApiResponse({
        transactions: paginatedTransactions,
        pagination: pagination.value
      });
    } else {
      // Real API implementation
      const { accountNumber } = useAuthService();

      // Map category to API function
      let func = 'All';
      if (category) {
        if (category.toLowerCase() === 'collected') {
          func = 'Point';
        } else if (category.toLowerCase() === 'spent') {
          func = 'Redeem';
        }
      }

      // Set date range for the last year
      const endDate = new Date().toISOString().split('T')[0]; // Today
      const startDate = new Date(new Date().setFullYear(new Date().getFullYear() - 1)).toISOString().split('T')[0]; // One year ago

      // Use the correct API endpoint based on our testing
      const endpoint = `api/AccountTransaction/${func}/${accountNumber.value}/${startDate}/${endDate}`;

      const response = await httpClient.get(endpoint);

      if (Array.isArray(response)) {
        // Format the transactions
        const formattedTransactions = response.map(tx => ({
          id: tx.TxnId,
          type: tx.Descp,
          amount: tx.Pts,
          date: new Date(tx.TxnDate).toISOString().split('T')[0],
          time: new Date(tx.TxnDate).toTimeString().split(' ')[0],
          category: tx.Category === 'Points' ? 'Collected' : 'Spent',
          description: tx.Descp,
          reference: tx.TxnId.toString()
        }));

        // Calculate pagination
        const total = formattedTransactions.length;
        const totalPages = Math.ceil(total / limit);
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;

        // Get transactions for current page
        const paginatedTransactions = formattedTransactions.slice(startIndex, endIndex);

        // Update state
        transactions.value = paginatedTransactions;
        pagination.value = {
          total,
          page,
          limit,
          totalPages
        };

        return createApiResponse({
          transactions: paginatedTransactions,
          pagination: pagination.value
        });
      }

      throw new Error('Failed to retrieve transactions');
    }
  } catch (err) {
    error.value = err.message;
    return createApiErrorResponse('TRANSACTION_SERVICE_UNAVAILABLE', err.message);
  } finally {
    isLoading.value = false;
  }
};

/**
 * Get transaction details by ID
 * @param {number} id - Transaction ID
 * @returns {Promise<Object>} Transaction details
 */
const getTransactionDetails = async (id) => {
  isLoading.value = true;
  error.value = null;

  try {
    if (!isLoggedIn.value) {
      throw new Error('User not authenticated');
    }

    if (!id) {
      throw new Error('Transaction ID is required');
    }

    if (USE_MOCK_DATA) {
      // Mock implementation
      await simulateApiDelay();

      // Find transaction in mock data
      const transaction = mockTransactions.find(t => t.id === parseInt(id));

      if (!transaction) {
        return createApiErrorResponse('TRANSACTION_NOT_FOUND', 'Transaction not found');
      }

      selectedTransaction.value = transaction;
      return createApiResponse(transaction);
    } else {
      // Real API implementation
      // For transaction details, we'll use the same endpoint but filter for the specific transaction
      const { accountNumber } = useAuthService();

      // Set date range for the last year
      const endDate = new Date().toISOString().split('T')[0]; // Today
      const startDate = new Date(new Date().setFullYear(new Date().getFullYear() - 1)).toISOString().split('T')[0]; // One year ago

      const endpoint = `api/AccountTransaction/All/${accountNumber.value}/${startDate}/${endDate}`;

      const response = await httpClient.get(endpoint);

      if (Array.isArray(response)) {
        // Find the specific transaction
        const transaction = response.find(tx => tx.TxnId === parseInt(id));

        if (!transaction) {
          throw new Error('Transaction not found');
        }

        // Format the transaction
        const formattedTransaction = {
          id: transaction.TxnId,
          type: transaction.Descp,
          amount: transaction.Pts,
          date: new Date(transaction.TxnDate).toISOString().split('T')[0],
          time: new Date(transaction.TxnDate).toTimeString().split(' ')[0],
          category: transaction.Category === 'Points' ? 'Collected' : 'Spent',
          description: transaction.Descp,
          reference: transaction.TxnId.toString(),
          location: transaction.Location || 'Unknown',
          items: []
        };

        selectedTransaction.value = formattedTransaction;
        return createApiResponse(formattedTransaction);
      }

      throw new Error('Failed to retrieve transaction details');
    }
  } catch (err) {
    error.value = err.message;
    return createApiErrorResponse('TRANSACTION_NOT_FOUND', err.message);
  } finally {
    isLoading.value = false;
  }
};

/**
 * Reset error state
 */
const resetError = () => {
  error.value = null;
};

// Export transaction service
export const useTransactionService = () => {
  return {
    // State
    transactions,
    selectedTransaction,
    pagination,
    isLoading,
    error,

    // Methods
    getTransactions,
    getTransactionDetails,
    resetError
  };
};
