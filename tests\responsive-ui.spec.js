import { test, expect } from '@playwright/test';

/**
 * Responsive UI tests focusing on the login page and general UI elements
 * that don't require authentication
 */
test.describe('Responsive UI Tests', () => {
  // Define viewport sizes for testing
  const viewports = {
    mobileSm: { width: 320, height: 568 },  // iPhone 5/SE (smallest supported)
    mobileMd: { width: 375, height: 667 },  // iPhone 6/7/8
    mobileLg: { width: 414, height: 896 },  // iPhone 11 Pro Max
    tablet: { width: 768, height: 1024 },   // iPad
    desktop: { width: 1280, height: 800 },  // Standard desktop
    desktopLg: { width: 1920, height: 1080 } // Large desktop
  };

  // Test login page responsiveness
  test('Login page should adapt to different screen sizes', async ({ page }) => {
    await page.goto('/login');
    
    // Test on small mobile
    await page.setViewportSize(viewports.mobileSm);
    await page.screenshot({ path: 'login-mobile-sm.png' });
    
    // Check container width on small mobile
    const containerWidthSm = await page.evaluate(() => {
      const container = document.querySelector('.login-container');
      return container ? window.getComputedStyle(container).width : 'not found';
    });
    console.log(`Login container width on small mobile: ${containerWidthSm}`);
    
    // Test on medium mobile
    await page.setViewportSize(viewports.mobileMd);
    await page.screenshot({ path: 'login-mobile-md.png' });
    
    // Check container width on medium mobile
    const containerWidthMd = await page.evaluate(() => {
      const container = document.querySelector('.login-container');
      return container ? window.getComputedStyle(container).width : 'not found';
    });
    console.log(`Login container width on medium mobile: ${containerWidthMd}`);
    
    // Test on tablet
    await page.setViewportSize(viewports.tablet);
    await page.screenshot({ path: 'login-tablet.png' });
    
    // Check container width on tablet
    const containerWidthTablet = await page.evaluate(() => {
      const container = document.querySelector('.login-container');
      return container ? window.getComputedStyle(container).width : 'not found';
    });
    console.log(`Login container width on tablet: ${containerWidthTablet}`);
    
    // Test on desktop
    await page.setViewportSize(viewports.desktop);
    await page.screenshot({ path: 'login-desktop.png' });
    
    // Check container width on desktop
    const containerWidthDesktop = await page.evaluate(() => {
      const container = document.querySelector('.login-container');
      return container ? window.getComputedStyle(container).width : 'not found';
    });
    console.log(`Login container width on desktop: ${containerWidthDesktop}`);
    
    // Verify that container widths are different between mobile and desktop
    expect(containerWidthSm).not.toEqual(containerWidthDesktop);
  });

  // Test phone input field responsiveness
  test('Phone input field should adapt to different screen sizes', async ({ page }) => {
    await page.goto('/login');
    
    // Test on small mobile
    await page.setViewportSize(viewports.mobileSm);
    
    // Check phone input width on small mobile
    const inputWidthSm = await page.evaluate(() => {
      const input = document.querySelector('.phone-input-wrapper');
      return input ? window.getComputedStyle(input).width : 'not found';
    });
    console.log(`Phone input width on small mobile: ${inputWidthSm}`);
    
    // Test on desktop
    await page.setViewportSize(viewports.desktop);
    
    // Check phone input width on desktop
    const inputWidthDesktop = await page.evaluate(() => {
      const input = document.querySelector('.phone-input-wrapper');
      return input ? window.getComputedStyle(input).width : 'not found';
    });
    console.log(`Phone input width on desktop: ${inputWidthDesktop}`);
    
    // Verify that input widths are different between mobile and desktop
    // or that they maintain proper proportions
    const smWidth = parseFloat(inputWidthSm);
    const desktopWidth = parseFloat(inputWidthDesktop);
    
    if (!isNaN(smWidth) && !isNaN(desktopWidth)) {
      // Either they're different or they maintain proper proportions to viewport
      const smRatio = smWidth / viewports.mobileSm.width;
      const desktopRatio = desktopWidth / viewports.desktop.width;
      
      console.log(`Mobile ratio: ${smRatio}, Desktop ratio: ${desktopRatio}`);
      
      // Check if the ratios are similar (responsive) or if absolute sizes differ
      const ratiosSimilar = Math.abs(smRatio - desktopRatio) < 0.1;
      const sizesDifferent = Math.abs(smWidth - desktopWidth) > 50;
      
      expect(ratiosSimilar || sizesDifferent).toBeTruthy();
    }
  });

  // Test country selector dropdown responsiveness
  test('Country selector dropdown should adapt to different screen sizes', async ({ page }) => {
    await page.goto('/login');
    
    // Test on mobile
    await page.setViewportSize(viewports.mobileMd);
    
    // Click on country selector to open dropdown
    await page.click('.country-selector');
    
    // Wait for dropdown to be visible
    await page.waitForSelector('.country-dropdown', { state: 'visible' });
    
    // Take screenshot with dropdown open on mobile
    await page.screenshot({ path: 'country-dropdown-mobile.png' });
    
    // Check dropdown width on mobile
    const dropdownWidthMobile = await page.evaluate(() => {
      const dropdown = document.querySelector('.country-dropdown');
      return dropdown ? window.getComputedStyle(dropdown).width : 'not found';
    });
    console.log(`Country dropdown width on mobile: ${dropdownWidthMobile}`);
    
    // Close dropdown by clicking outside
    await page.click('.login-title');
    
    // Test on desktop
    await page.setViewportSize(viewports.desktop);
    
    // Click on country selector to open dropdown
    await page.click('.country-selector');
    
    // Wait for dropdown to be visible
    await page.waitForSelector('.country-dropdown', { state: 'visible' });
    
    // Take screenshot with dropdown open on desktop
    await page.screenshot({ path: 'country-dropdown-desktop.png' });
    
    // Check dropdown width on desktop
    const dropdownWidthDesktop = await page.evaluate(() => {
      const dropdown = document.querySelector('.country-dropdown');
      return dropdown ? window.getComputedStyle(dropdown).width : 'not found';
    });
    console.log(`Country dropdown width on desktop: ${dropdownWidthDesktop}`);
    
    // Verify that dropdown widths are appropriate for each viewport
    const mobileWidth = parseFloat(dropdownWidthMobile);
    const desktopWidth = parseFloat(dropdownWidthDesktop);
    
    if (!isNaN(mobileWidth) && !isNaN(desktopWidth)) {
      // On mobile, dropdown should be close to full width
      const mobileRatio = mobileWidth / viewports.mobileMd.width;
      expect(mobileRatio).toBeGreaterThan(0.8);
    }
  });

  // Test app container responsiveness
  test('App container should adapt to different screen sizes', async ({ page }) => {
    await page.goto('/login');
    
    // Test on mobile
    await page.setViewportSize(viewports.mobileMd);
    
    // Check app container width on mobile
    const appWidthMobile = await page.evaluate(() => {
      // Use first app container to avoid ambiguity
      const app = document.querySelector('#app');
      return app ? window.getComputedStyle(app).width : 'not found';
    });
    console.log(`App container width on mobile: ${appWidthMobile}`);
    
    // Test on desktop
    await page.setViewportSize(viewports.desktop);
    
    // Check app container width on desktop
    const appWidthDesktop = await page.evaluate(() => {
      // Use first app container to avoid ambiguity
      const app = document.querySelector('#app');
      return app ? window.getComputedStyle(app).width : 'not found';
    });
    console.log(`App container width on desktop: ${appWidthDesktop}`);
    
    // Verify that app container adapts to viewport
    const mobileWidth = parseFloat(appWidthMobile);
    const desktopWidth = parseFloat(appWidthDesktop);
    
    if (!isNaN(mobileWidth) && !isNaN(desktopWidth)) {
      // On mobile, app should be full width
      // On desktop, app might be constrained
      const mobileRatio = mobileWidth / viewports.mobileMd.width;
      const desktopRatio = desktopWidth / viewports.desktop.width;
      
      console.log(`Mobile ratio: ${mobileRatio}, Desktop ratio: ${desktopRatio}`);
      
      // Mobile should be close to full width
      expect(mobileRatio).toBeGreaterThan(0.95);
    }
  });
});
