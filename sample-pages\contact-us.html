<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .card {
            background: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .contact-method {
            margin-bottom: 15px;
        }
        .contact-label {
            font-weight: bold;
            display: inline-block;
            width: 100px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #2980b9;
        }
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #121212;
                color: #eee;
            }
            h1 {
                color: #e0e0e0;
                border-bottom-color: #333;
            }
            .card {
                background-color: #1e1e1e;
                box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            }
            input, textarea {
                background-color: #333;
                border-color: #444;
                color: #eee;
            }
            button {
                background-color: #64b5f6;
            }
            button:hover {
                background-color: #4a8ec2;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Contact Us</h1>
        
        <div class="card">
            <h2>Get in Touch</h2>
            <p>We'd love to hear from you! Please use one of the following methods to contact us:</p>
            
            <div class="contact-method">
                <span class="contact-label">Email:</span>
                <span><EMAIL></span>
            </div>
            
            <div class="contact-method">
                <span class="contact-label">Phone:</span>
                <span>1-800-RVM-PLUS (**************)</span>
            </div>
            
            <div class="contact-method">
                <span class="contact-label">Address:</span>
                <span>123 Green Street, Eco City, EC 12345</span>
            </div>
            
            <div class="contact-method">
                <span class="contact-label">Hours:</span>
                <span>Monday to Friday, 9am to 5pm</span>
            </div>
        </div>
        
        <div class="card">
            <h2>Send Us a Message</h2>
            <form id="contact-form">
                <div class="form-group">
                    <label for="name">Name</label>
                    <input type="text" id="name" name="name" required>
                </div>
                
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="subject">Subject</label>
                    <input type="text" id="subject" name="subject" required>
                </div>
                
                <div class="form-group">
                    <label for="message">Message</label>
                    <textarea id="message" name="message" required></textarea>
                </div>
                
                <button type="submit">Send Message</button>
            </form>
        </div>
    </div>
    
    <script>
        // Simple form handling for demo purposes
        document.getElementById('contact-form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Thank you for your message! This is a demo form, so no message was actually sent.');
            this.reset();
        });
    </script>
</body>
</html>
