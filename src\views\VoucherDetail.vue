<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useTheme } from '../store/theme';
import { useI18n } from 'vue-i18n';

const route = useRoute();
const router = useRouter();
const { isDarkMode } = useTheme();
const { t } = useI18n();

// Banner image URL
const bannerImageUrl = computed(() => {
  return new URL('../assets/voucher-detail-banner.svg', import.meta.url).href;
});

// Get voucher ID from route params
const voucherId = parseInt(route.params.id);

// Sample voucher data - in a real app, this would come from an API
const vouchers = {
  1: {
    id: 1,
    brand: 'RVMPlus',
    title: 'How To Use',
    expiryDate: '31/12/2025',
    status: 'Active',
    type: 'INFO',
    subtitle: 'Reverse Vending Machine',
    content: {
      introText: 'The RVMPlus Reverse Vending Machine is a Smart AI-Driven Reverse Vending Machine designed to collect PET bottles and aluminium cans efficiently. By ensuring high-value recycling, it promotes circularity and sustainability.',
      stepsTitle: 'How To Recycle',
      stepsIntro: 'Just follow these simple steps:',
      steps: [
        {
          title: 'Locate a Machine:',
          description: 'Use the \'Locations\' tab in the app to find your nearest RVMPlus Reverse Vending Machine.'
        },
        {
          title: 'Start the Process:',
          description: 'Tap the \'Start\' button on the machine\'s screen.'
        },
        {
          title: 'Access Your Account:',
          description: 'Login by either scanning your RVMPlus Points Collection QR code using the scanner or by entering your mobile number directly on the screen.'
        },
        {
          title: 'Place Your Bottle:',
          description: 'Gently place the PET bottle or aluminium can onto the conveyor belt. The machine will process it automatically.'
        },
        {
          title: 'Finish the Process:',
          description: 'Once completed, tap the \'Done\' button on the screen.'
        },
        {
          title: 'Claim Your Rewards:',
          description: 'The screen will show the points you earned in this recycling session! Tap the \'Rebate\' button on the screen to collect your rewards.'
        }
      ],
      whyTitle: 'Why It Matters',
      whyItMatters: [
        'Environmental Impact: Each recycled item reduces landfill waste and conserves resources.',
        'Earn While You Recycle: Get rewarded with RVMPlus points for your sustainable actions.',
        'Track Your Contribution: Monitor your personal impact on sustainability through the app.'
      ]
    }
  },
  2: {
    id: 2,
    brand: 'BYC',
    title: 'BYC Free 1 Coffee',
    expiryDate: '13/02/2025',
    status: 'Used',
    type: 'INFO',
    subtitle: 'Bring your cup, buy your coffee',
    content: {
      introText: 'What Does BYC Stand For?\nBring Your Cup, Buy Your Coffee.',
      stepsTitle: 'How to Redeem Your Rewards',
      stepsIntro: 'The BYC Coffee Vending Machine delivers high-quality coffee made with freshly ground beans, offering you a café-like experience on the go. We\'re promoting sustainability by encouraging you to bring your own cup. As a reward, you\'ll receive a free coffee voucher for your next order when you bring your cup!',
      steps: [
        {
          title: 'Bring Your Cup:',
          description: 'Grab your favourite reusable cup.'
        },
        {
          title: 'Select Your Beverage:',
          description: 'Choose your coffee or drink option from the menu.'
        },
        {
          title: 'Confirm \'Bring Your Cup\':',
          description: 'Select \'Yes\' when prompted with \'Bring Your Cup?\''
        },
        {
          title: 'Access Your Account:',
          description: 'Scan your RVMPlus QR Code or enter your mobile number to log into your RVMPlus Member Account.'
        },
        {
          title: 'Wait for Your Brew:',
          description: 'Place your cup in the designated window and let the machine work its magic.'
        },
        {
          title: 'Claim Your Reward:',
          description: 'After brewing, receive a free BYC beverage voucher in your RVMPlus app for your next order!'
        }
      ],
      whyTitle: 'Why It Matters',
      whyItMatters: [
        'Sustainable Coffee on the Go: Support eco-friendly practices while enjoying premium coffee.',
        'Reduce Single-Use Waste: Every cup you bring helps decrease disposable cup waste.'
      ]
    }
  },
  3: {
    id: 3,
    brand: 'SBC',
    title: 'SBC 50% Off Pastry',
    expiryDate: '20/03/2025',
    status: 'Active',
    type: 'INFO',
    subtitle: 'Enjoy premium pastries at half price',
    content: {
      introText: 'Enjoy premium pastries at half price with this special offer from SBC.',
      stepsTitle: 'How to Redeem',
      stepsIntro: 'Follow these simple steps to enjoy your discount:',
      steps: [
        {
          title: 'Visit an SBC Store:',
          description: 'Find your nearest SBC location using the Locations tab.'
        },
        {
          title: 'Select Your Pastry:',
          description: 'Choose from our wide selection of freshly baked pastries.'
        },
        {
          title: 'Present Your Voucher:',
          description: 'Show the QR code to the cashier when making your purchase.'
        },
        {
          title: 'Enjoy Your Discount:',
          description: 'Receive 50% off your selected pastry!'
        }
      ],
      whyTitle: 'Why It Matters',
      whyItMatters: [
        'Support Local Bakeries: SBC uses locally sourced ingredients.',
        'Sustainable Packaging: All SBC pastries come in eco-friendly packaging.'
      ]
    }
  },
  4: {
    id: 4,
    brand: 'CFC',
    title: 'CFC Buy 1 Get 1 Free',
    expiryDate: '01/01/2025',
    status: 'Expired',
    type: 'CAMPAIGN',
    subtitle: 'Limited time offer',
    content: {
      introText: 'Buy one meal and get another one free at any CFC restaurant.',
      stepsTitle: 'How to Redeem',
      stepsIntro: 'Follow these simple steps to enjoy your free meal:',
      steps: [
        {
          title: 'Visit a CFC Restaurant:',
          description: 'Find your nearest CFC location using the Locations tab.'
        },
        {
          title: 'Order Your Meal:',
          description: 'Choose any meal from our regular menu.'
        },
        {
          title: 'Present Your Voucher:',
          description: 'Show the QR code to the cashier when making your purchase.'
        },
        {
          title: 'Enjoy Your Free Meal:',
          description: 'Receive a second meal of equal or lesser value for free!'
        }
      ],
      whyTitle: 'Why It Matters',
      whyItMatters: [
        'Community Support: CFC donates a portion of proceeds to local charities.',
        'Sustainable Sourcing: All ingredients are ethically sourced.'
      ]
    }
  }
};

// Get voucher details
const voucher = ref(vouchers[voucherId] || null);

// Back button is now handled by the Header component

// Handle redeem button
const redeemVoucher = () => {
  // In a real app, this would call an API to redeem the voucher
  if (voucher.value) {
    voucher.value.status = 'Used';
    console.log(`Redeeming voucher: ${voucher.value.title}`);
    // Navigate back to vouchers page after redeeming
    router.push('/vouchers');
  }
};

// Check if voucher exists
onMounted(() => {
  if (!voucher.value) {
    // Redirect to vouchers page if voucher not found
    router.push('/vouchers');
  }
});
</script>

<template>
  <div v-if="voucher" class="voucher-detail-page" :data-type="voucher.type">

    <!-- Banner image -->
    <div class="banner-image">
      <img :src="bannerImageUrl" alt="Banner" />
    </div>

    <!-- Content header -->
    <div class="content-header">
      <div class="content-type">{{ voucher.type }}</div>
      <h1 class="content-title">{{ voucher.title }}</h1>
      <p class="content-subtitle">{{ voucher.subtitle }}</p>
    </div>

    <!-- Main content -->
    <div class="content-body">
      <!-- Introduction text -->
      <p v-if="voucher.content.introText" class="intro-text">{{ voucher.content.introText }}</p>

      <!-- Steps section -->
      <div v-if="voucher.content.steps" class="steps-section">
        <h2 v-if="voucher.content.stepsTitle" class="steps-title">{{ voucher.content.stepsTitle }}</h2>
        <p v-if="voucher.content.stepsIntro" class="steps-intro">{{ voucher.content.stepsIntro }}</p>

        <ol class="steps-list">
          <li v-for="(step, index) in voucher.content.steps" :key="index" class="step-item">
            <div class="step-number">{{ index + 1 }}.</div>
            <div class="step-content">
              <strong>{{ step.title }}</strong>
              <p>{{ step.description }}</p>
            </div>
          </li>
        </ol>
      </div>

      <!-- Why it matters section -->
      <div v-if="voucher.content.whyItMatters" class="why-matters-section">
        <h2 v-if="voucher.content.whyTitle" class="why-title">{{ voucher.content.whyTitle }}</h2>
        <ul class="benefits-list">
          <li v-for="(benefit, index) in voucher.content.whyItMatters" :key="index" class="benefit-item">
            <span class="check-icon">✓</span>
            <span>{{ benefit }}</span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<style scoped>
.voucher-detail-page {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100%;
  padding: 0;
  padding-bottom: 7rem; /* Add padding to ensure content is visible above bottom nav */
  background-color: var(--background-color);
  color: var(--text-color);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

.banner-image {
  width: 100%;
  height: 200px;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
}

.banner-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Dark theme adjustment for banner */
:global(.dark-theme) .banner-image {
  background-color: #1e1e1e;
}

.content-header {
  padding: 0 1rem;
  margin-bottom: 1.5rem;
}

.content-type {
  font-size: 0.8rem;
  font-weight: 500;
  color: #4ECDC4;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
}

/* Campaign type styling */
.voucher-detail-page[data-type="CAMPAIGN"] .content-type {
  color: #FF9966;
}

.content-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.content-subtitle {
  font-size: 1.2rem;
  color: var(--text-light);
  font-weight: 400;
  margin-bottom: 0.5rem;
}

.content-body {
  padding: 0 1rem 4rem; /* Increased bottom padding */
}

.intro-text {
  margin-bottom: 1.5rem;
  line-height: 1.6;
  color: var(--text-color);
}

.steps-section {
  margin-bottom: 2rem;
}

.steps-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.steps-intro {
  margin-bottom: 1rem;
  color: var(--text-color);
}

.steps-list {
  list-style: none;
  padding: 0;
  counter-reset: step-counter;
}

.step-item {
  display: flex;
  margin-bottom: 1.5rem;
  position: relative;
}

.step-number {
  font-weight: bold;
  margin-right: 0.75rem;
  color: var(--text-color);
}

.step-content {
  flex: 1;
}

.step-content strong {
  display: block;
  margin-bottom: 0.25rem;
  color: var(--text-color);
}

.step-content p {
  margin: 0;
  line-height: 1.5;
  color: var(--text-light);
}

.why-matters-section {
  margin-top: 2rem;
}

.why-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-color);
}

.benefits-list {
  list-style: none;
  padding: 0;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.check-icon {
  color: #4ECDC4;
  margin-right: 0.5rem;
  font-weight: bold;
}

/* Dark theme specific adjustments */
:global(.dark-theme) .check-icon {
  color: #4ECDC4;
}

/* Responsive adjustments */
@media (min-width: 768px) {
  .voucher-detail-page {
    padding-bottom: 2rem; /* Less padding needed on desktop */
    height: auto;
    overflow-y: auto;
  }

  .content-header,
  .content-body {
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }

  .banner-image {
    height: 250px;
  }
}

/* Mobile specific adjustments */
@media (max-width: 767px) {
  .voucher-detail-page {
    padding-bottom: 7rem; /* More padding for mobile to account for bottom nav */
  }
}
</style>
