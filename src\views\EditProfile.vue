<script setup>
import { ref, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useProfile } from '@/composables/useProfile';
import axios from 'axios';
import { useAuth } from '../store/auth';

const router = useRouter();
const { t } = useI18n();
const { profile, fetchProfile, updateProfile } = useProfile();
const { token, accountNumber, user } = useAuth();

// Form data
const formData = ref({
  fullName: '',
  email: '',
  mobileNo: '',
  gender: '',
  dob: '',
  street: '',
  street2: '',
  street3: '',
  zipCode: '',
  city: '',
  state: '',
  country: '',
});

// Form validation
const errors = ref({});
const isFormValid = ref(true);

// Update form validation to check required fields
const validateForm = () => {
  errors.value = {};

  if (!formData.value.fullName) {
    errors.value.fullName = t('validation.required', 'This field is required');
  }

  if (!formData.value.mobileNo) {
    errors.value.mobileNo = t('validation.required', 'This field is required');
  }

  if (!formData.value.zipCode) {
    errors.value.zipCode = t('validation.required', 'This field is required');
  }

  // Update isFormValid based on errors
  isFormValid.value = Object.keys(errors.value).length === 0;

  return isFormValid.value;
};

// Gender options - using M for Male and F for Female as required by the API
const genderOptions = [
  { value: 'M', label: t('profile.male', 'Male') },
  { value: 'F', label: t('profile.female', 'Female') },
];

// Loading state
const isSaving = ref(false);
const saveError = ref(null);

// Watch for profile changes and update form data
watch(profile, (newProfile) => {
  if (newProfile) {
    formData.value = {
      fullName: newProfile.name || '',
      email: newProfile.email || '',
      mobileNo: newProfile.phone || '',
      gender: newProfile.gender || '',
      dob: newProfile.dob || '',
      street: newProfile.address?.street || '',
      street2: newProfile.address?.street2 || '',
      street3: newProfile.address?.street3 || '',
      zipCode: newProfile.address?.zipCode || '50000',
      city: newProfile.address?.city || 'Kuala Lumpur',
      state: newProfile.address?.state || 'KUL',
      country: newProfile.address?.country || 'MY',
    };
    console.log('Form data populated:', formData.value);
  }
}, { immediate: true });

// Load user profile data on mount
onMounted(async () => {
  try {
    await fetchProfile();
  } catch (error) {
    console.error('Error loading profile data:', error);
  }
});

// Save profile changes
const saveProfile = async () => {
  saveError.value = null;
  const isValid = validateForm();

  if (isValid) {
    isSaving.value = true;
    try {
      const profileData = {
        Token: token.value,
        Func: 'Update',
        AcctNo: accountNumber.value,
        FullName: formData.value.fullName,
        EmailAddr: formData.value.email,
        gender: formData.value.gender,
        ContactNo: formData.value.mobileNo,
        Street1: formData.value.street,
        Street2: formData.value.street2,
        Street3: formData.value.street3,
        StateCd: formData.value.state,
        City: formData.value.city,
        ZipCd: formData.value.zipCode,
        CtryCd: formData.value.country,
        Userid: user.value?.userId || '',
        DOB: formData.value.dob,
        Lang: 'en',
      };

      // Direct API call, no bitmap
      const response = await axios.post('/api/UpdateProfile', profileData, {
        headers: {
          Authorization: token.value, // if your backend expects it
        },
      });

      if (response.data && (response.data.IsSuccessStatusCode || response.status === 200)) {
        router.push('/profile');
        await fetchProfile();
      } else {
        saveError.value = response.data?.ReasonPhrase || t('profile.updateFailed');
      }
    } catch (error) {
      saveError.value = t('profile.updateFailed');
    } finally {
      isSaving.value = false;
    }
  }
};

// Cancel and go back to profile
const cancelEdit = () => {
  router.push('/profile');
};
</script>

<template>
  <div class="edit-profile-container">
    <div class="edit-profile-form">
      <div class="form-group">
        <label for="fullName">{{ t('profile.name') }}</label>
        <div class="input-container">
          <input
            id="fullName"
            v-model="formData.fullName"
            type="text"
            :placeholder="t('profile.name')"
            :class="{ 'error': errors.fullName }"
          />
          <div class="input-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
          </div>
        </div>
        <p v-if="errors.fullName" class="error-message">{{ errors.fullName }}</p>
      </div>

      <div class="form-group">
        <label for="email">{{ t('profile.email') }}</label>
        <div class="input-container">
          <input
            id="email"
            v-model="formData.email"
            type="email"
            :placeholder="t('profile.email')"
            :class="{ 'error': errors.email }"
          />
          <div class="input-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
          </div>
        </div>
        <p v-if="errors.email" class="error-message">{{ errors.email }}</p>
      </div>

      <div class="form-group">
        <label for="mobileNo">{{ t('profile.phone') }}</label>
        <div class="input-container">
          <input
            id="mobileNo"
            v-model="formData.mobileNo"
            type="tel"
            :placeholder="t('profile.phone')"
            :class="{ 'error': errors.mobileNo }"
          />
          <div class="input-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
            </svg>
          </div>
        </div>
        <p v-if="errors.mobileNo" class="error-message">{{ errors.mobileNo }}</p>
      </div>

      <div class="form-group">
        <label for="gender">{{ t('profile.gender') }}</label>
        <div class="select-container">
          <select
            id="gender"
            v-model="formData.gender"
            :class="{ 'error': errors.gender }"
          >
            <option value="" disabled>{{ t('profile.selectGender') }}</option>
            <option v-for="option in genderOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </option>
          </select>
          <div class="select-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="6 9 12 15 18 9"></polyline>
            </svg>
          </div>
        </div>
        <p v-if="errors.gender" class="error-message">{{ errors.gender }}</p>
      </div>

      <div class="form-group">
        <label for="dob">{{ t('profile.dob', 'Date of Birth') }}</label>
        <div class="input-container">
          <input
            id="dob"
            v-model="formData.dob"
            type="date"
            :placeholder="t('profile.dob', 'Date of Birth')"
            :class="{ 'error': errors.dob }"
          />
          <div class="input-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="16" y1="2" x2="16" y2="6"></line>
              <line x1="8" y1="2" x2="8" y2="6"></line>
              <line x1="3" y1="10" x2="21" y2="10"></line>
            </svg>
          </div>
        </div>
        <p v-if="errors.dob" class="error-message">{{ errors.dob }}</p>
      </div>

      <div class="form-group">
        <label for="street">{{ t('profile.address', 'Address Line 1') }}</label>
        <div class="input-container">
          <input
            id="street"
            v-model="formData.street"
            type="text"
            :placeholder="t('profile.addressLine1', 'Address Line 1')"
            :class="{ 'error': errors.street }"
          />
          <div class="input-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
          </div>
        </div>
        <p v-if="errors.street" class="error-message">{{ errors.street }}</p>
      </div>

      <div class="form-group">
        <label for="street2">{{ t('profile.addressLine2', 'Address Line 2') }}</label>
        <div class="input-container">
          <input
            id="street2"
            v-model="formData.street2"
            type="text"
            :placeholder="t('profile.addressLine2', 'Address Line 2')"
            :class="{ 'error': errors.street2 }"
          />
          <div class="input-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
          </div>
        </div>
        <p v-if="errors.street2" class="error-message">{{ errors.street2 }}</p>
      </div>

      <!-- <div class="form-group">
        <label for="street3">{{ t('profile.addressLine3', 'Address Line 3') }}</label>
        <div class="input-container">
          <input
            id="street3"
            v-model="formData.street3"
            type="text"
            :placeholder="t('profile.addressLine3', 'Address Line 3')"
            :class="{ 'error': errors.street3 }"
          />
          <div class="input-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
          </div>
        </div>
        <p v-if="errors.street3" class="error-message">{{ errors.street3 }}</p>
      </div> -->

      <!-- Hidden fields for state, city, and country -->
      <input v-model="formData.state" type="hidden" />
      <input v-model="formData.city" type="hidden" />
      <input v-model="formData.country" type="hidden" />

      <!-- Keep zipCode visible as it maps to ZipCd in the API -->
      <div class="form-group">
        <label for="zipCode">{{ t('profile.zipCode', 'Zip/Postal Code') }}</label>
        <div class="input-container">
          <input
            id="zipCode"
            v-model="formData.zipCode"
            type="text"
            :placeholder="t('profile.zipCode', 'Zip/Postal Code')"
            :class="{ 'error': errors.zipCode }"
          />
          <div class="input-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="2" y="4" width="20" height="16" rx="2"></rect>
              <path d="M6 8h.01M18 8h.01M6 12h.01M18 12h.01M6 16h.01M18 16h.01"></path>
            </svg>
          </div>
        </div>
        <p v-if="errors.zipCode" class="error-message">{{ errors.zipCode }}</p>
      </div>

      <p v-if="saveError" class="save-error">{{ saveError }}</p>

      <div class="form-actions">
        <button
          class="cancel-button"
          :disabled="isSaving"
          @click="cancelEdit"
        >
          {{ t('common.cancel') }}
        </button>
        <button
          class="save-button"
          :disabled="!isFormValid || isSaving"
          @click="saveProfile"
        >
          <span v-if="isSaving">{{ t('common.saving') }}...</span>
          <span v-else>{{ t('profile.saveChanges') }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.edit-profile-container {
  padding: 0;
  width: 100%;
  margin: 0 auto;
  padding-bottom: 7rem;
  background-color: var(--background-color);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-y: auto;
}

.edit-profile-form {
  padding: 1rem;
  width: 100%;
}

.form-group {
  margin-bottom: 1.5rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-color);
}

.input-container, .select-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 50px;
  border-radius: 8px;
  background-color: var(--card-background);
  border: 1px solid var(--border-color);
  overflow: hidden;
  color: var(--text-color);
}

input, select {
  flex: 1;
  height: 100%;
  padding: 0 1rem 0 3rem;
  border: none;
  background-color: transparent;
  font-size: 1rem;
  color: var(--text-color);
  outline: none;
  width: 100%;
}

select {
  padding-right: 2.5rem;
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-color: var(--card-background);
  color: var(--text-color);
}

input::placeholder, select::placeholder {
  color: var(--text-light);
  opacity: 0.7;
}

.input-icon, .select-icon {
  position: absolute;
  left: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  pointer-events: none;
}

.select-icon {
  right: 1rem;
  left: auto;
  color: var(--text-light);
}

.helper-text {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.error {
  border-color: var(--error-color);
}

.error-message {
  color: var(--error-color);
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

.save-error {
  color: var(--error-color);
  font-size: 0.9rem;
  margin-top: 1rem;
  margin-bottom: 1rem;
  text-align: center;
  padding: 0.5rem;
  background-color: rgba(255, 82, 82, 0.1);
  border-radius: 8px;
  border: 1px solid var(--error-color);
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
  gap: 1rem;
}

.cancel-button, .save-button {
  flex: 1;
  padding: 0.8rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-button {
  background-color: transparent;
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.save-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.save-button:disabled {
  background-color: var(--border-color);
  cursor: not-allowed;
}

.cancel-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.save-button:hover:not(:disabled) {
  background-color: var(--primary-dark);
}

/* Media queries for responsive design */
@media (max-width: 480px) {
  .edit-profile-container {
    padding-bottom: 120px;
    padding-top: env(safe-area-inset-top, 0px);
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .input-container, .select-container {
    height: 45px;
  }

  input, select {
    font-size: 0.95rem;
  }
}

@media (min-width: 769px) {
  .edit-profile-container {
    max-width: 480px;
    margin: 0 auto;
    height: 100%;
    overflow-y: auto;
  }

  .edit-profile-form {
    max-width: 480px;
    margin: 0 auto;
    padding: 1.5rem;
  }

  .form-actions {
    margin-top: 2.5rem;
  }
}
</style>
