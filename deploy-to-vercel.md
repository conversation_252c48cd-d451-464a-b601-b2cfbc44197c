# Deploying to Vercel

Follow these steps to deploy your Vue application to Vercel:

## Prerequisites

1. Create a Vercel account at [vercel.com](https://vercel.com)
2. Install the Vercel CLI:
   ```bash
   npm install -g vercel
   ```

## Deployment Steps

1. **Login to Vercel**
   ```bash
   vercel login
   ```

2. **Deploy from the project directory**
   ```bash
   vercel
   ```

   This will start an interactive deployment process. You can accept the default settings for most options.

3. **For production deployment**
   ```bash
   vercel --prod
   ```

## Automatic Deployment with GitHub

For continuous deployment:

1. Push your code to a GitHub repository
2. Connect to Vercel:
   - Go to [vercel.com/dashboard](https://vercel.com/dashboard)
   - Click "Add New" > "Project"
   - Import your GitHub repository
   - Configure project settings (Vercel will auto-detect Vite)
   - Click "Deploy"

## Configuration Files

The project already includes the necessary configuration files:

- `vercel.json` - Configures routing and build settings
- `vite.config.js` - Configures the build output

## Recent Changes for Deployment

The following changes were made to fix SVG loading issues:

1. Updated SVG imports in components to use direct imports:
   ```js
   import HomeIcon from '../assets/home-icon.svg'
   // Then in template:
   <img :src="HomeIcon" alt="Home" />
   ```

2. Added SVG fallbacks in the public directory:
   - Created `/public/assets/` directory with SVG fallbacks
   - Added placeholder SVGs for all icons

3. Updated Vite configuration:
   - Added `assetsInclude: ['**/*.svg']` to ensure SVGs are processed correctly
   - Added path alias for easier imports

4. Enhanced Vercel configuration:
   - Added proper caching headers for static assets
   - Added filesystem handler for static files

## Testing After Deployment

After deployment, Vercel will provide you with a URL for your application. Visit this URL to ensure everything is working correctly.

For testing the login functionality, use these phone numbers:
- 1234567890 (Test User)
- 9876543210 (Demo User)

The OTP code will be displayed on the verification screen for testing purposes.
