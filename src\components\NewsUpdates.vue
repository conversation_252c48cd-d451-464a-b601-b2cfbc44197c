<script setup>
import { ref, onMounted, computed, onUnmounted } from 'vue';
import { useI18n } from 'vue-i18n';

const props = defineProps({
  table1: { type: Array, default: () => [] },
  loading: Boolean,
  error: Object,
  onMarkAsRead: { type: Function, required: false },
});

const { t } = useI18n();

const newsList = computed(() => 
  props.table1.map(news => ({
    ...news,
    isRead: false,
  })),
);
const currentIndex = ref(0);
const newsContainer = ref(null);

const goToNext = () => {
  if (newsList.value.length === 0) {return;}
  currentIndex.value = (currentIndex.value + 1) % newsList.value.length;
  scrollToCard(currentIndex.value, true);
};

const goToPrevious = () => {
  if (newsList.value.length === 0) {return;}
  currentIndex.value = (currentIndex.value - 1 + newsList.value.length) % newsList.value.length;
  scrollToCard(currentIndex.value, true);
};

const goToSlide = (index) => {
  currentIndex.value = index;
  scrollToCard(index, true);
};

const scrollToCard = (index, smooth = false) => {
  if (!newsContainer.value) {return;}
  const container = newsContainer.value;
  const firstCard = container.querySelector('.news-card');
  const cardWidth = firstCard ? firstCard.offsetWidth : container.clientWidth;
  const scrollPosition = index * cardWidth;
  container.scrollTo({
    left: scrollPosition,
    behavior: smooth ? 'smooth' : 'auto',
  });
};

// Add ref for auto-scroll interval and scrolling state
const autoScrollInterval = ref(null);
const isScrolling = ref(false);
const scrollTimeout = ref(null);

// Start auto-scrolling
const startAutoScroll = () => {
  stopAutoScroll(); // Clear any existing interval
  autoScrollInterval.value = setInterval(() => {
    if (!isScrolling.value) {
      goToNext();
    }
  }, 3000); // Change slide every 3 seconds
};

// Stop auto-scrolling
const stopAutoScroll = () => {
  if (autoScrollInterval.value) {
    clearInterval(autoScrollInterval.value);
    autoScrollInterval.value = null;
  }
};

// Handle manual scrolling
const handleScroll = () => {
  if (!newsContainer.value || isScrolling.value) {return;}

  // Set scrolling state to true when manual scrolling starts
  isScrolling.value = true;

  // Clear previous timeout
  if (scrollTimeout.value) {
    clearTimeout(scrollTimeout.value);
  }

  // Set a timeout to detect when scrolling stops
  scrollTimeout.value = setTimeout(() => {
    const container = newsContainer.value;
    const scrollPosition = container.scrollLeft;
    const firstCard = container.querySelector('.news-card');
    const cardWidth = firstCard ? firstCard.offsetWidth : container.clientWidth;

    // Calculate which card is most visible
    const newIndex = Math.round(scrollPosition / cardWidth);

    // Update current index if it's valid
    if (newIndex >= 0 && newIndex < newsList.value.length) {
      currentIndex.value = newIndex;
    }

    // Scroll to the centered position of the current card
    scrollToCard(currentIndex.value, true);

    // Reset scrolling state after manual scroll completes
    isScrolling.value = false;
  }, 150);
};

// Lifecycle hooks
onMounted(() => {
  startAutoScroll();
});

onUnmounted(() => {
  stopAutoScroll();
  if (scrollTimeout.value) {
    clearTimeout(scrollTimeout.value);
  }
});
</script>

<template>
  <div class="news-section">
    <h2 class="section-title">{{ t('news.title') }}</h2>
    <div v-if="loading">Loading...</div>
    <div v-else-if="error">Error loading news.</div>
    <div v-else>
      <div
        v-if="newsList.length > 0"
        ref="newsContainer"
        class="news-carousel-container"
      >
          <div
            v-for="(news, index) in newsList"
            :key="news.Ids"
            class="news-card"
            :class="{ 'active': index === currentIndex }"
            @click="onMarkAsRead?.(news)"
          >
          <div class="news-date">{{ new Date(news.Date).toLocaleDateString() }}</div>
          <h3 class="news-title">{{ news.Subject }}</h3>
          <div class="news-desc">{{ news.Descp }}</div>
        </div>
      </div>
      <div v-else class="no-news">No news available.</div>
      <!-- Indicators -->
      <div v-if="newsList.length > 1" class="news-carousel-indicators">
        <button
          v-for="(news, index) in newsList"
          :key="`indicator-${news.Ids}`"
          class="indicator-dot"
          :class="{ 'active': index === currentIndex }"
          :aria-label="`Go to news ${index + 1}`"
          @click="goToSlide(index)"
        ></button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.news-section {
  padding: 0 1rem;
  margin-bottom: 5rem; /* Space for bottom navigation */
}

.news-carousel-container {
  display: flex;
  overflow-x: hidden;
  scroll-snap-type: x mandatory;
  padding-bottom: 0.5rem;
  margin: 0 -1rem;
  padding-left: 1rem;
}

.news-card {
  flex: 0 0 100%;
  scroll-snap-align: center;
  width: calc(100% - 2rem);
  margin-right: 1rem;
  background-color: var(--card-background);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.news-date {
  display: inline-block;
  background-color: #333;
  color: white;
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.news-title {
  font-size: 1.2rem;
  margin: 0.5rem 0;
  color: var(--text-color);
}

.news-desc {
  font-size: 1rem;
  color: var(--text-light);
  margin-top: 0.5rem;
}

.no-news {
  text-align: center;
  color: var(--text-light);
  padding: 2rem;
}

.news-carousel-indicators {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  gap: 0.5rem;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--border-color);
  border: none;
  padding: 0;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.indicator-dot.active {
  background-color: var(--primary-color);
  transform: scale(1.2);
}

/* Responsive styles */
@media (min-width: 769px) {
  .news-section {
    max-width: 800px;
    margin: 0 auto 5rem auto;
  }
}
</style>
