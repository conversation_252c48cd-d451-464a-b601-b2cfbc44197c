<script setup>
import { useRoute } from 'vue-router';
import { computed, onMounted, ref } from 'vue';
import Header from './components/Header.vue';
import BottomNavigation from './components/BottomNavigation.vue';
import EnvInfo from './components/debug/EnvInfo.vue';
import Notification from './components/Notification.vue';
import { useAuth } from './store/auth';
import { useTheme } from './store/theme';
import { useNotification } from './store/notification';
import { isDevelopment } from './utils/envConfig';

const route = useRoute();
const { isLoggedIn } = useAuth();
const { isDarkMode } = useTheme(); // Used to initialize theme
const { notification } = useNotification();
const envInfoRef = ref(null);

// Determine if we should show the bottom navigation
// Only show on authenticated routes and not on login
const showBottomNav = computed(() => {
  return isLoggedIn.value && route.name !== 'Login';
});

// Initialize theme on app mount
onMounted(() => {
  // The theme store will handle applying the theme class
  // This is just to ensure the theme is initialized

  // Show environment info in development mode with double tap
  if (isDevelopment()) {
    let lastTap = 0;
    document.addEventListener('touchend', (_) => {
      const currentTime = new Date().getTime();
      const tapLength = currentTime - lastTap;
      if (tapLength < 300 && tapLength > 0) {
        // Double tap detected
        if (envInfoRef.value) {
          envInfoRef.value.showDebugInfo();
        }
      }
      lastTap = currentTime;
    });
  }
});
</script>

<template>
  <div id="app">
    <Header v-if="isLoggedIn" />
    <div class="content-container">
      <router-view />
    </div>
    <BottomNavigation v-if="showBottomNav" />
    <EnvInfo ref="envInfoRef" />
    <Notification
      :show="notification.show"
      :message="notification.message"
      :type="notification.type"
      :duration="notification.duration"
      :position="notification.position"
      @close="notification.show = false"
    />
  </div>
</template>

<style scoped>
#app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden; /* Prevent double scrollbars */
}

.content-container {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  position: relative;
  height: 100%; /* Ensure it takes full height */
  display: flex; /* Use flexbox for better layout control */
  flex-direction: column; /* Stack children vertically */
}

/* We'll handle overflow in the Login component directly */

/* Responsive styles */
@media (min-width: 769px) {
  #app {
    border: 1px solid var(--border-color);
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin: 2rem auto;
    height: 90vh;
  }
}

@media (max-width: 480px) {
  #app {
    height: 100vh;
    overflow: hidden;
  }

  .content-container {
    padding-bottom: env(safe-area-inset-bottom, 0px); /* Add padding for safe area */
  }
}
</style>
