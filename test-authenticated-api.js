// Test script for authenticated API calls to staging environment

// Configuration
const API_BASE_URL = 'https://staging.rvmplus.com';
const API_TIMEOUT = 30000;

// Test credentials
const TEST_PHONE = '60102431439';
const TEST_PASSWORD = '123456';

// Helper function to make API requests
async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
  
  // Default headers
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...options.headers,
  };
  
  // Add authorization header if token exists
  const token = options.token;
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  // Request options
  const requestOptions = {
    ...options,
    headers,
  };
  
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT);
    
    requestOptions.signal = controller.signal;
    
    console.log(`Making ${options.method || 'GET'} request to: ${url}`);
    if (options.body) {
      console.log('Request body:', options.body);
    }
    
    const response = await fetch(url, requestOptions);
    clearTimeout(timeoutId);
    
    console.log('Response status:', response.status);
    
    // Parse response
    let data;
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }
    
    // Handle API error responses
    if (!response.ok) {
      const errorMessage = typeof data === 'object' && data.message ? data.message : 'An unexpected error occurred';
      const errorCode = typeof data === 'object' && data.error && data.error.code ? data.error.code : 'UNKNOWN_ERROR';
      
      const error = new Error(errorMessage);
      error.status = response.status;
      error.code = errorCode;
      error.response = data;
      
      throw error;
    }
    
    return data;
  } catch (error) {
    if (error.name === 'AbortError') {
      throw new Error('Request timed out');
    }
    
    throw error;
  }
}

// HTTP GET request
async function get(endpoint, options = {}) {
  return apiRequest(endpoint, { 
    method: 'GET',
    ...options,
  });
}

// HTTP POST request
async function post(endpoint, data, options = {}) {
  return apiRequest(endpoint, {
    method: 'POST',
    body: JSON.stringify(data),
    ...options,
  });
}

// Login and get authentication data
async function login() {
  console.log('\nLogging in...');
  try {
    // Using the GET endpoint that worked in our previous test
    const data = await get(`api/cmlogin/${TEST_PHONE}/${TEST_PASSWORD}/en`);
    console.log('Login successful!');
    
    if (Array.isArray(data) && data.length > 0) {
      const loginData = data[0];
      return {
        acctNo: loginData.AcctNo,
        token: loginData.access_token,
        tokenType: loginData.token_type
      };
    }
    
    throw new Error('Unexpected login response format');
  } catch (error) {
    console.error('Error during login:', error.message);
    if (error.response) {
      console.error('Response:', error.response);
    }
    return null;
  }
}

// Test getting user profile
async function testGetProfile(authData) {
  console.log('\nTesting GET Profile endpoint...');
  try {
    // Try both formats for profile endpoint
    console.log('Format 1: Using GetProfile/{AcctNo}/Basic/en');
    const profileData1 = await get(`api/GetProfile/${authData.acctNo}/Basic/en`, {
      token: authData.token
    });
    console.log('Profile response:', JSON.stringify(profileData1, null, 2));
    
    console.log('\nFormat 2: Using POST to GetProfile/{AcctNo}/Basic/en');
    const profileData2 = await post(`api/GetProfile/${authData.acctNo}/Basic/en`, {}, {
      token: authData.token
    });
    console.log('Profile response:', JSON.stringify(profileData2, null, 2));
    
    return profileData1 || profileData2;
  } catch (error) {
    console.error('Error fetching profile:', error.message);
    if (error.response) {
      console.error('Response:', error.response);
    }
    return null;
  }
}

// Test getting transactions
async function testGetTransactions(authData) {
  console.log('\nTesting GET Transactions endpoint...');
  try {
    const data = await get(`api/AccountTransaction/All/${authData.acctNo}/2023-01-01/2024-12-31`, {
      token: authData.token
    });
    console.log(`Received ${Array.isArray(data) ? data.length : 0} transactions`);
    if (Array.isArray(data) && data.length > 0) {
      console.log('First transaction:', JSON.stringify(data[0], null, 2));
    }
    return data;
  } catch (error) {
    console.error('Error fetching transactions:', error.message);
    if (error.response) {
      console.error('Response:', error.response);
    }
    return null;
  }
}

// Test getting vouchers
async function testGetVouchers(authData) {
  console.log('\nTesting POST Vouchers endpoint...');
  try {
    const data = await post('api/AcccountVoucher', {
      AcctNo: authData.acctNo,
      Func: 'Active',
      Lang: 'en'
    }, {
      token: authData.token
    });
    console.log(`Received ${Array.isArray(data) ? data.length : 0} vouchers`);
    if (Array.isArray(data) && data.length > 0) {
      console.log('First voucher:', JSON.stringify(data[0], null, 2));
    }
    return data;
  } catch (error) {
    console.error('Error fetching vouchers:', error.message);
    if (error.response) {
      console.error('Response:', error.response);
    }
    return null;
  }
}

// Run tests
async function runTests() {
  console.log('Starting authenticated API tests...');
  console.log('API Base URL:', API_BASE_URL);
  
  // Login and get authentication data
  const authData = await login();
  
  if (authData) {
    console.log('\nAuthentication successful!');
    console.log('Account Number:', authData.acctNo);
    console.log('Token available:', !!authData.token);
    
    // Test authenticated endpoints
    await testGetProfile(authData);
    await testGetTransactions(authData);
    await testGetVouchers(authData);
  } else {
    console.log('\nAuthentication failed. Cannot test authenticated endpoints.');
  }
  
  console.log('\nAll tests completed!');
}

// Execute tests
runTests();
