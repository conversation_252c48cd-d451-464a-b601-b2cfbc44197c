// Test script for profile API calls to staging environment

// Configuration
const API_BASE_URL = 'https://staging.rvmplus.com';
const API_TIMEOUT = 30000;

// Test credentials
const TEST_PHONE = '60102431439';
const TEST_PASSWORD = '123456';

// Helper function to make API requests
async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;

  // Default headers
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...options.headers,
  };

  // Add authorization header if token exists
  const token = options.token;
  if (token) {
    // Note: The API might not use Bearer token format, so we'll try without the Bearer prefix
    headers['Authorization'] = token;
  }

  // Request options
  const requestOptions = {
    ...options,
    headers,
  };

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT);

    requestOptions.signal = controller.signal;

    console.log(`Making ${options.method || 'GET'} request to: ${url}`);
    if (options.body) {
      console.log('Request body:', options.body);
    }

    const response = await fetch(url, requestOptions);
    clearTimeout(timeoutId);

    console.log('Response status:', response.status);

    // Parse response
    let data;
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }

    // Handle API error responses
    if (!response.ok) {
      const errorMessage = typeof data === 'object' && data.message ? data.message : 'An unexpected error occurred';
      const errorCode = typeof data === 'object' && data.error && data.error.code ? data.error.code : 'UNKNOWN_ERROR';

      const error = new Error(errorMessage);
      error.status = response.status;
      error.code = errorCode;
      error.response = data;

      throw error;
    }

    return data;
  } catch (error) {
    if (error.name === 'AbortError') {
      throw new Error('Request timed out');
    }

    throw error;
  }
}

// HTTP GET request
async function get(endpoint, options = {}) {
  return apiRequest(endpoint, {
    method: 'GET',
    ...options,
  });
}

// HTTP POST request
async function post(endpoint, data, options = {}) {
  return apiRequest(endpoint, {
    method: 'POST',
    body: JSON.stringify(data),
    ...options,
  });
}

// Login and get authentication data
async function login() {
  console.log('\nLogging in...');
  try {
    // Using the GET endpoint that worked in our previous test
    const data = await get(`api/cmlogin/${TEST_PHONE}/${TEST_PASSWORD}/en`);
    console.log('Login successful!');

    if (Array.isArray(data) && data.length > 0) {
      const loginData = data[0];
      return {
        acctNo: loginData.AcctNo,
        token: loginData.access_token,
        tokenType: loginData.token_type
      };
    }

    throw new Error('Unexpected login response format');
  } catch (error) {
    console.error('Error during login:', error.message);
    if (error.response) {
      console.error('Response:', error.response);
    }
    return null;
  }
}

// Test getting user profile
async function testGetProfile(authData) {
  console.log('\nTesting GET Profile endpoint...');
  try {
    const profileData = await get(`api/GetProfile/${authData.acctNo}/Basic/en`, {
      token: authData.token
    });
    console.log('Profile response:', JSON.stringify(profileData, null, 2));
    return profileData;
  } catch (error) {
    console.error('Error fetching profile:', error.message);
    if (error.response) {
      console.error('Response:', error.response);
    }
    return null;
  }
}

// Test updating user profile
async function testUpdateProfile(authData, profileData) {
  console.log('\nTesting Update Profile endpoint...');
  try {
    // Format the data according to the API requirements
    const formattedData = {
      AcctNo: authData.acctNo,
      FullName: profileData.name || 'Test User',
      Email: profileData.email || '<EMAIL>',
      Gender: profileData.gender || 'Male',
      Street1: profileData.address?.street || '123 Test St',
      Street2: profileData.address?.street2 || '',
      ZipCd: profileData.address?.zipCode || '47500',
      City: profileData.address?.city || 'Petaling Jaya',
      StateCd: profileData.address?.state || 'SGR',
      Lang: profileData.language || 'en'
    };

    console.log('Update profile request data:', formattedData);

    const response = await post('api/UpdateProfile', formattedData, {
      token: authData.token
    });

    console.log('Update profile response:', JSON.stringify(response, null, 2));
    return response;
  } catch (error) {
    console.error('Error updating profile:', error.message);
    if (error.response) {
      console.error('Response:', error.response);
    }
    return null;
  }
}

// Run tests
async function runTests() {
  console.log('Starting profile API tests...');
  console.log('API Base URL:', API_BASE_URL);

  // Login and get authentication data
  const authData = await login();

  if (authData) {
    console.log('\nAuthentication successful!');
    console.log('Account Number:', authData.acctNo);
    console.log('Token available:', !!authData.token);

    // Test getting profile
    const profileData = await testGetProfile(authData);

    if (profileData) {
      // Test updating profile with a small change
      const updatedProfileData = {
        name: profileData.Table0[0].FullName,
        email: profileData.Table1 && profileData.Table1.length > 0 ? profileData.Table1[0].Email : '<EMAIL>',
        gender: profileData.Table1 && profileData.Table1.length > 0 ? profileData.Table1[0].Gender : 'Male',
        address: {
          street: profileData.Table1 && profileData.Table1.length > 0 ? profileData.Table1[0].Street1 : '123 Test St',
          street2: profileData.Table1 && profileData.Table1.length > 0 ? profileData.Table1[0].Street2 : '',
          zipCode: profileData.Table1 && profileData.Table1.length > 0 ? profileData.Table1[0].ZipCd : '47500',
          city: profileData.Table1 && profileData.Table1.length > 0 ? profileData.Table1[0].City : 'Petaling Jaya',
          state: profileData.Table1 && profileData.Table1.length > 0 ? profileData.Table1[0].StateCd : 'SGR'
        }
      };

      // Make a small change to test the update
      updatedProfileData.email = `test${Date.now()}@example.com`;

      await testUpdateProfile(authData, updatedProfileData);

      // Verify the update by getting the profile again
      await testGetProfile(authData);
    }
  } else {
    console.log('\nAuthentication failed. Cannot test profile endpoints.');
  }

  console.log('\nAll tests completed!');
}

// Execute tests
runTests();
