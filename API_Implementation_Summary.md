# API Implementation Summary

This document provides a summary of the API requirements and implementation details for the RewardRedemption application.

## Overview

The RewardRedemption application requires an ASP.NET backend to handle various features including:

1. Authentication with OTP
2. User profile management
3. Location services
4. Voucher management
5. Transaction history
6. Movement tracking
7. Forest/environmental impact tracking

## API Structure

The API follows a RESTful design with the following structure:

```
/api
  /auth
    POST /send-otp
    POST /verify-otp
  /users
    GET /profile
    PUT /profile
  /locations
    GET /
    GET /{id}
    GET /nearby
  /vouchers
    GET /
    GET /{id}
    POST /{id}/redeem
  /transactions
    GET /
    GET /{id}
  /movement
    GET /stats
    GET /challenges
    POST /challenges/{id}/start
    PUT /challenges/active/progress
  /forest
    GET /stats
    POST /update
```

## Response Format

All API endpoints follow a consistent response format:

### Success Response

```json
{
  "success": true,
  "message": "Optional success message",
  "data": {
    // Response data specific to each endpoint
  }
}
```

### Error Response

```json
{
  "success": false,
  "message": "Error message",
  "error": {
    "code": "ERROR_CODE"
  }
}
```

For detailed error codes and their meanings, refer to the `API_Error_Codes.md` document.

## Authentication

The API uses JWT (JSON Web Token) for authentication. After verifying the OTP, the server issues a JWT token that should be included in the `Authorization` header of subsequent requests.

```
Authorization: Bearer {token}
```

## Error Handling

The API uses a consistent error handling approach with specific error codes for different scenarios. The frontend is responsible for displaying appropriate error messages based on these codes.

For a complete list of error codes, refer to the `API_Error_Codes.md` document.

The error response format is:

```json
{
  "success": false,
  "message": "Error message",
  "error": {
    "code": "ERROR_CODE"
  }
}
```

This approach allows for:
1. Consistent error handling across the application
2. Localization of error messages on the frontend
3. Clear separation of concerns between backend and frontend

## Implementation Requirements

### Controllers

The ASP.NET backend should implement the following controllers:

1. **AuthController**: Handles user authentication with OTP
2. **UserController**: Manages user profile information
3. **LocationController**: Provides location-related operations
4. **VoucherController**: Manages voucher operations
5. **TransactionController**: Handles transaction history
6. **MovementController**: Manages movement tracking and challenges
7. **ForestController**: Tracks environmental impact

### Services

Each controller should be supported by corresponding services:

1. **AuthService**: Handles OTP generation, sending, and verification
2. **UserService**: Manages user data
3. **LocationService**: Provides location data and distance calculations
4. **VoucherService**: Manages voucher data and redemption
5. **TransactionService**: Handles transaction records
6. **MovementService**: Manages movement tracking and challenges
7. **ForestService**: Tracks environmental impact

### Data Access

The backend should implement data access using Entity Framework Core with the following repositories:

1. **UserRepository**: Manages user data
2. **LocationRepository**: Manages location data
3. **VoucherRepository**: Manages voucher data
4. **TransactionRepository**: Manages transaction data
5. **ChallengeRepository**: Manages challenge data
6. **ForestRepository**: Manages forest/environmental data

## Database Schema

The database should include the following tables:

1. **Users**: Store user information
   - Id (PK)
   - Name
   - Phone
   - Email
   - Gender
   - Address (JSON or separate address table)
   - CreatedAt
   - UpdatedAt

2. **OtpCodes**: Store OTP codes for verification
   - Id (PK)
   - Phone
   - Code
   - ExpiresAt
   - IsUsed
   - CreatedAt

3. **Locations**: Store RVM locations
   - Id (PK)
   - Name
   - Address
   - Latitude
   - Longitude
   - Services (JSON array)
   - Description
   - PhoneNumber
   - Email
   - Website
   - OpeningHours (JSON array)
   - Images (JSON array)

4. **Vouchers**: Store voucher information
   - Id (PK)
   - Brand
   - Title
   - Subtitle
   - ExpiryDate
   - Type
   - Content (JSON)
   - BannerImage
   - CreatedAt

5. **UserVouchers**: Store user-voucher relationships
   - Id (PK)
   - UserId (FK)
   - VoucherId (FK)
   - Status (Active, Used, Expired)
   - RedeemedAt
   - CreatedAt

6. **Transactions**: Store transaction history
   - Id (PK)
   - UserId (FK)
   - Type
   - Amount
   - Date
   - Time
   - Category
   - Description
   - Location
   - Reference
   - Items (JSON array)
   - CreatedAt

7. **Challenges**: Store available challenges
   - Id (PK)
   - Title
   - Description
   - Reward
   - Goal
   - Unit
   - CreatedAt

8. **UserChallenges**: Store user-challenge relationships
   - Id (PK)
   - UserId (FK)
   - ChallengeId (FK)
   - StartTime
   - EndTime
   - Progress
   - Status (Active, Completed, Expired)
   - CreatedAt
   - UpdatedAt

9. **ForestStats**: Store user's forest statistics
   - Id (PK)
   - UserId (FK)
   - CurrentStage
   - CO2Absorbed
   - CO2Target
   - TotalCO2Mitigated
   - TotalItemsRecycled
   - TotalTreesGrown
   - CreatedAt
   - UpdatedAt

## Security Considerations

1. **HTTPS**: All API endpoints must be accessible only via HTTPS
2. **JWT Expiration**: JWT tokens should expire after 24 hours
3. **Refresh Tokens**: Implement refresh tokens for seamless re-authentication
4. **Rate Limiting**: Implement rate limiting for sensitive endpoints like OTP verification
5. **Input Validation**: Validate all input data to prevent injection attacks
6. **CORS**: Configure CORS to allow only trusted origins
7. **Password Hashing**: If passwords are used, use strong hashing algorithms (e.g., bcrypt)
8. **Sensitive Data**: Encrypt sensitive data in the database
9. **API Keys**: Use API keys for external service integrations

## Performance Considerations

1. **Caching**: Implement caching for frequently accessed data like locations
2. **Pagination**: Implement pagination for endpoints that return large datasets
3. **Compression**: Enable response compression to reduce bandwidth usage
4. **Async/Await**: Use async/await pattern for all I/O operations
5. **Database Indexing**: Properly index database tables for efficient queries
6. **Connection Pooling**: Configure proper connection pooling for database access
7. **Lazy Loading**: Use lazy loading for related entities when appropriate

## Conclusion

This API implementation summary provides a blueprint for developing the ASP.NET backend for the RewardRedemption application. By following these guidelines and implementing the specified endpoints with proper error handling, the backend will provide a robust foundation for the mobile application.

The error handling approach, with error codes returned to the frontend, allows for a clean separation of concerns and enables localization of error messages on the client side.

For detailed API endpoint specifications, refer to the `API_Requirements.md` document. For a complete list of error codes, refer to the `API_Error_Codes.md` document.
