<template>
  <div class="env-info" v-if="showDebug">
    <h3>Environment Information</h3>
    <div class="env-info-content">
      <p><strong>Mode:</strong> {{ mode }}</p>
      <p><strong>API Base URL:</strong> {{ apiBaseUrl }}</p>
      <p><strong>Using Mock Data:</strong> {{ useMockData ? 'Yes' : 'No' }}</p>
      <p><strong>App Name:</strong> {{ appName }}</p>
      <p><strong>App Version:</strong> {{ appVersion }}</p>
    </div>
    <button @click="hideDebugInfo" class="close-button">Close</button>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

// Environment variables
const mode = import.meta.env.MODE;
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
const useMockData = import.meta.env.VITE_USE_MOCK_DATA === 'true';
const appName = import.meta.env.VITE_APP_NAME;
const appVersion = import.meta.env.VITE_APP_VERSION;

// Debug state
const showDebug = ref(false);

// Show debug info
const showDebugInfo = () => {
  showDebug.value = true;
};

// Hide debug info
const hideDebugInfo = () => {
  showDebug.value = false;
};

// Expose methods
defineExpose({
  showDebugInfo
});
</script>

<style scoped>
.env-info {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 1rem;
  z-index: 9999;
  border-top: 2px solid #42b883;
}

.env-info-content {
  margin-bottom: 1rem;
}

.env-info h3 {
  margin-top: 0;
  color: #42b883;
}

.close-button {
  background-color: #42b883;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
}

.close-button:hover {
  background-color: #3aa876;
}
</style>
