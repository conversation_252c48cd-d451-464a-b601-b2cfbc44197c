{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "search": "Search", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "confirm": "Confirm", "retry": "Retry", "refresh": "Refresh", "help": "Help & Support", "contact": "Contact us", "sending": "Sending...", "saving": "Saving", "noResults": "No results found"}, "navigation": {"home": "Home", "vouchers": "Vouchers", "qrcode": "QR Code", "locations": "Locations", "profile": "Profile", "transactions": "Transactions", "move": "Move", "rvmMove": "RVM Move", "rvmForest": "RVM Forest"}, "auth": {"login": "<PERSON><PERSON>", "loginWithPassword": "Login with your phone number and password", "logout": "Logout", "phone": "Phone Number", "enterPhone": "Enter your phone number", "password": "Password", "enterPassword": "Enter your password", "show": "Show", "hide": "<PERSON>de", "loggingIn": "Logging in...", "countryCode": "Country Code", "otp": "Verification Code", "enterOtp": "Enter the 6-digit code sent to", "enterOtpPlaceholder": "Enter 6-digit code", "sendOtp": "Send Verification Code", "verifyOtp": "Verify OTP", "resendOtp": "Resend Code", "otpSent": "OTP sent to your phone", "invalidOtp": "Invalid OTP. Please try again.", "loginSuccess": "Login successful", "verifyPhone": "Verify Your Phone", "verify": "Verify", "verifying": "Verifying...", "resendCodeIn": "Resend code in", "didntReceiveCode": "Didn't receive the code?", "backToPhoneEntry": "Back to Phone Entry", "testNote": "For testing, use: 60102431439 / 123456", "testCodeNote": "For testing, use code:"}, "locations": {"title": "Locations", "currentLocation": "Current Location", "nearbyLocations": "Nearby Recycling Points", "trackingEnabled": "Tracking Enabled", "enableTracking": "Enable Tracking", "gettingLocation": "Getting your location...", "locationPermission": "We need your location to show nearby recycling points.", "grantAccess": "Grant Location Access", "loadingLocations": "Loading nearby locations...", "noLocations": "No locations available near you.", "tryEnabling": "Try enabling location access or refreshing your location.", "away": "away", "unknownLocation": "Unknown location", "gettingLocationName": "Getting location name..."}, "profile": {"title": "Profile", "name": "Name", "email": "Email", "phone": "Phone", "language": "Language", "theme": "Theme", "darkMode": "Dark Mode", "lightMode": "Light Mode", "logout": "Logout", "editProfile": "Edit Profile", "saveChanges": "Save Changes", "selectLanguage": "Select Language", "editProfileInfo": "Edit profile information", "notifications": "Notifications", "notificationsOn": "ON", "notificationsOff": "OFF", "privacyPolicy": "Privacy policy", "security": "Security", "demoUser": "Demo User", "gender": "Gender", "male": "Male", "female": "Female", "other": "Other", "selectGender": "Select gender", "address": "Address", "zipCode": "Zip Code", "city": "City", "state": "State/Province", "selectState": "Select state", "selectCity": "Select city", "selectZipCode": "Select zip code", "zipCodeFilterHelp": "Select a state first to filter zip codes"}, "languages": {"en": "English", "ms": "Bahasa Melayu", "zh": "中文"}, "points": {"yourPoints": "Your RVMPlus Points"}, "vouchers": {"title": "Vouchers", "active": "Active", "used": "Used", "expired": "Expired", "noActiveVouchers": "No active vouchers available.", "noUsedVouchers": "No used vouchers available.", "noExpiredVouchers": "No expired vouchers available.", "expiresOn": "Expires on", "redeem": "REDEEM"}, "qrcode": {"pointsCollection": "Your Points Collection QR Code", "description": "Use this QR Code at RVMPlus Machines to collect your points for your sustainability efforts. Every scan brings you closer to exciting rewards!", "scanQrCode": "Scan QR Code", "scanDescription": "Scan a QR code to make a payment or redeem a voucher.", "scanningInstructions": "Position the QR code within the frame to scan", "scanResult": "<PERSON><PERSON>", "processingPayment": "Processing payment...", "paymentSuccess": "Payment successful!", "paymentFailed": "Payment failed. Please try again.", "collection": "Collection", "payment": "Payment", "cameraAccessDenied": "Camera access denied. Please allow camera access to scan QR codes.", "noCameraFound": "No camera found on this device.", "browserNotSupported": "Your browser does not support camera access."}, "transactions": {"title": "Transactions", "all": "All", "collected": "Collected", "spent": "Spent", "noTransactions": "No transactions available."}, "move": {"title": "Your MOVE", "activeChallenge": "Active Challenge", "noActiveChallenge": "No Active Challenge", "startChallenge": "Start a Challenge", "availableChallenges": "Available Challenges", "steps": "steps", "minutes": "minutes", "meters": "meters", "progress": "Progress", "timeLeft": "Time Left", "selectChallenge": "Select a challenge to start moving and earning rewards!", "startNow": "START NOW", "completeChallenge": "COMPLETE CHALLENGE", "reward": "<PERSON><PERSON>"}, "features": {"transactions": "Transactions", "rvmMove": "RVM Move", "rvmForest": "RVM Forest"}, "highlights": {"title": "Highlights", "noHighlights": "No highlights available."}, "news": {"title": "News and Updates"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "phone": "Please enter a valid phone number", "minLength": "This field must be at least {min} characters", "maxLength": "This field must be at most {max} characters"}, "forest": {"title": "Your FOREST", "currentTree": "Current tree", "stage": "Stage", "totalCO2": "Total CO2e mitigated", "totalItems": "Total items recycled", "totalTrees": "Total trees grown", "co2Absorbed": "CO2e absorbed", "nextStage": "Next stage", "plantNewTree": "Plant a new tree", "treeInfo": "Tree information"}, "security": {"title": "Security", "phoneNumber": "Phone Number", "currentPhone": "Current Phone", "changePhone": "Change Phone Number", "twoFactor": "Two-Factor Authentication", "twoFactorAuth": "Two-Factor Authentication", "twoFactorDescription": "Receive a verification code when logging in from a new device", "notifications": "Security Notifications", "securityAlerts": "Security Alerts", "alertsDescription": "Get notified about suspicious login attempts", "loginHistory": "Login History", "currentDevice": "Current", "deviceManagement": "Device Management", "removeDevice": "Remove"}, "information": {"redeem": {"title": "How To Redeem", "subtitle": "RVMPlus Points Redemption", "introText": "Every RVMPlus Point is worth 1 cent. Redeem your points easily at our partner machines: ESGPlus Vending Machines and BYC Coffee Vending Machines. Enjoy the rewards of your commitment to sustainability!", "stepsTitle": "How To Redeem", "stepsIntro": "Just follow these simple steps:", "step1Title": "Locate a Machine:", "step1Desc": "Use the 'Locations' tab in the app to find your nearest ESGPlus Vending machine or BYC Coffee Vending Machine.", "step2Title": "Select Your Item:", "step2Desc": "Browse the available items and choose your desired product.", "step3Title": "Pay with Points:", "step3Desc": "Scan your RVMPlus Points Redemption QR Code at the machine to complete your purchase."}, "use": {"title": "How To Use", "subtitle": "Reverse Vending Machine", "introText": "The RVMPlus Reverse Vending Machine is a Smart AI-Driven Reverse Vending Machine designed to collect PET bottles and aluminium cans efficiently. By ensuring high-value recycling, it promotes circularity and sustainability.", "stepsTitle": "How To Recycle", "stepsIntro": "Just follow these simple steps:", "step1Title": "Locate a Machine:", "step1Desc": "Use the 'Locations' tab in the app to find your nearest RVMPlus Reverse Vending Machine.", "step2Title": "Start the Process:", "step2Desc": "Tap the 'Start' button on the machine's screen.", "step3Title": "Access Your Account:", "step3Desc": "Login by either scanning your RVMPlus Points Collection QR code using the scanner or by entering your mobile number directly on the screen.", "step4Title": "Place Your Bottle:", "step4Desc": "Gently place the PET bottle or aluminum can onto the conveyor belt. The machine will process it automatically.", "step5Title": "Finish the Process:", "step5Desc": "Once completed, tap the 'Done' button on the screen.", "step6Title": "Claim Your Rewards:", "step6Desc": "The screen will show the points you earned in this recycling session! Tap the 'Rebate' button on the screen to collect your rewards."}, "byc": {"title": "BYC", "subtitle": "Bring your cup, buy your coffee", "introText": "What Does BYC Stand For?\nBring Your Cup, Buy Your Coffee.", "stepsTitle": "How to Redeem Your Rewards", "stepsIntro": "The BYC Coffee Vending Machine delivers high-quality coffee made with freshly ground beans, offering you a café-like experience on the go. We're promoting sustainability by encouraging you to bring your own cup. As a reward, you'll receive a free coffee voucher for your next order when you bring your cup!", "step1Title": "Bring Your Cup:", "step1Desc": "Grab your favourite reusable cup.", "step2Title": "Select Your Beverage:", "step2Desc": "Choose your coffee or drink option from the menu.", "step3Title": "Confirm 'Bring Your Cup':", "step3Desc": "Select 'Yes' when prompted with 'Bring Your Cup?'", "step4Title": "Access Your Account:", "step4Desc": "Scan your RVMPlus QR Code or enter your mobile number to log into your RVMPlus Member Account.", "step5Title": "Wait for Your Brew:", "step5Desc": "Place your cup in the designated window and let the machine work its magic.", "step6Title": "Claim Your Reward:", "step6Desc": "After brewing, receive a free BYC beverage voucher in your RVMPlus app for your next order!", "whyTitle": "Why It Matters", "benefit1": "Sustainable Coffee on the Go: Support eco-friendly practices while enjoying premium coffee.", "benefit2": "Reduce Single-Use Waste: Every cup you bring helps decrease disposable cup waste."}, "campaign": {"title": "Buy 1, Recycle 1, <PERSON> 1", "subtitle": "Campaign", "introText": "Enjoy More with Aqviva & RVMPlus. Purchase one bottle of Aqviva drinking water from an ESGPlus vending machine and get another bottle for FREE- just by recycling!", "stepsTitle": "How It Works", "stepsIntro": "Just follow these simple steps:", "step1Title": "Scan Your QR Code:", "step1Desc": "When purchasing an Aqviva bottle from the ESGPlus vending machine, scan your RVMPlus Points Collection QR Code.", "step2Title": "Enjoy Your Drink:", "step2Desc": "Refresh yourself with your Aqviva drinking water.", "step3Title": "Recycle Your Bottle:", "step3Desc": "Deposit the empty bottle into an RVMPlus reverse vending machine.", "step4Title": "Receive Your Voucher:", "step4Desc": "Get a voucher for 1 FREE bottle of drinking water.", "step5Title": "Redeem Your Reward:", "step5Desc": "Use your voucher QR code at the ESGPlus vending machine to claim your free bottle!", "whyTitle": "Why It Matters", "benefit1": "Sustainability in Action: Your efforts help reduce waste and promote recycling.", "benefit2": "Rewarding Responsibility: A little effort goes a long way- for you and the environment!"}}}