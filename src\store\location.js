// Location store to manage location state
import { ref, computed } from 'vue';
import { useLocationService } from '../services/locationService';

// Import mock location data
import mockLocations from '../data/locations.json';

// Get location service
const {
  currentPosition,
  isTracking,
  locationError,
  isLoading,
  placeName,
  isLoadingPlaceName,
  getCurrentPosition,
  startTracking,
  stopTracking,
  calculateDistance,
  getPlaceNameFromCoordinates,
  resetError,
} = useLocationService();

// Store state
const locations = ref([]);
const nearbyLocations = ref([]);
const selectedLocation = ref(null);
const locationHistory = ref([]);
const maxHistoryLength = 20; // Maximum number of location history entries to keep

// Load locations
const loadLocations = async () => {
  try {
    // In a real app, this would fetch from an API
    // For now, we'll use the mock data
    locations.value = mockLocations;
    return locations.value;
  } catch (error) {
    console.error('Error loading locations:', error);
    return [];
  }
};

// Update nearby locations based on current position
const updateNearbyLocations = () => {
  if (!currentPosition.value || locations.value.length === 0) {
    nearbyLocations.value = [];
    return;
  }

  const { latitude, longitude } = currentPosition.value;

  // Calculate distance for each location and sort by proximity
  nearbyLocations.value = locations.value
    .map(location => {
      const distance = calculateDistance(
        latitude,
        longitude,
        location.latitude,
        location.longitude,
      );

      return {
        ...location,
        distance: distance,
        distanceText: formatDistance(distance),
      };
    })
    .sort((a, b) => {
      // Primary sort by distance
      return a.distance - b.distance;
    });
};

// Format distance for display
const formatDistance = (distance) => {
  if (distance < 1) {
    // If less than 1 km, show in meters
    return `${Math.round(distance * 1000)} m`;
  }
  // Otherwise show in kilometers with 1 decimal place
  return `${distance.toFixed(1)} km`;
};

// Add current position to location history
const addToLocationHistory = (position) => {
  if (!position) {return;}

  // Add to the beginning of the array
  locationHistory.value.unshift({
    ...position,
    timestamp: position.timestamp || Date.now(),
  });

  // Limit the history length
  if (locationHistory.value.length > maxHistoryLength) {
    locationHistory.value = locationHistory.value.slice(0, maxHistoryLength);
  }
};

// Initialize location tracking
const initializeLocation = async () => {
  try {
    const position = await getCurrentPosition();
    if (position) {
      addToLocationHistory(position);
      await loadLocations();
      updateNearbyLocations();

      // Get place name for the current position
      const { latitude, longitude } = position;
      await getPlaceNameFromCoordinates(latitude, longitude);
    }
    return position;
  } catch (error) {
    console.error('Error initializing location:', error);
    return null;
  }
};

// Start continuous location tracking
const startLocationTracking = async () => {
  // Define a callback to handle position updates
  const positionUpdateCallback = async (position) => {
    if (position) {
      addToLocationHistory(position);
      updateNearbyLocations();

      // Get place name for the updated position
      const { latitude, longitude } = position;
      await getPlaceNameFromCoordinates(latitude, longitude);
    }
  };

  // Start tracking with the callback
  const success = startTracking(positionUpdateCallback);
  return success;
};

// Select a location
const selectLocation = (location) => {
  selectedLocation.value = location;
};

// Clear selected location
const clearSelectedLocation = () => {
  selectedLocation.value = null;
};

// Export the location store
export const useLocation = () => {
  return {
    // State
    currentPosition,
    locations,
    nearbyLocations,
    selectedLocation,
    locationHistory,
    isTracking,
    locationError,
    isLoading,
    placeName,
    isLoadingPlaceName,

    // Actions
    initializeLocation,
    loadLocations,
    updateNearbyLocations,
    startLocationTracking,
    stopTracking,
    selectLocation,
    clearSelectedLocation,
    getPlaceNameFromCoordinates,
    resetError,
  };
};
