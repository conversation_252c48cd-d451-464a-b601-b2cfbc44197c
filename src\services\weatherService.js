/**
 * Weather service to fetch weather data based on location
 */
import { ref } from 'vue';
import { useLocationService } from './locationService';

// State variables
const weatherData = ref(null);
const isLoading = ref(false);
const error = ref(null);

// Weather types
export const WEATHER_TYPES = {
  SUNNY: 'sunny',
  CLOUDY: 'cloudy',
  RAINY: 'rainy'
};

// Mock weather data for testing
const mockWeatherData = {
  temperature: 28,
  weatherType: WEATHER_TYPES.SUNNY,
  description: 'Sunny',
  humidity: 65,
  windSpeed: 5
};

// Get location service
const { getCurrentPosition } = useLocationService();

/**
 * Get weather data based on coordinates
 * @param {number} latitude - Latitude
 * @param {number} longitude - Longitude
 * @returns {Promise<Object>} Weather data
 */
const getWeatherByCoordinates = async (latitude, longitude) => {
  isLoading.value = true;
  error.value = null;

  try {
    // For a real implementation, you would use a weather API like OpenWeatherMap
    // Example API call: https://api.openweathermap.org/data/2.5/weather?lat={lat}&lon={lon}&appid={API key}
    
    // For now, we'll use mock data with some randomization
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
    
    // Randomly select a weather type for demonstration
    const weatherTypes = Object.values(WEATHER_TYPES);
    const randomWeatherType = weatherTypes[Math.floor(Math.random() * weatherTypes.length)];
    
    // Create weather data with some randomization
    const weather = {
      temperature: Math.floor(Math.random() * 15) + 20, // Random temp between 20-35°C
      weatherType: randomWeatherType,
      description: getWeatherDescription(randomWeatherType),
      humidity: Math.floor(Math.random() * 30) + 50, // Random humidity between 50-80%
      windSpeed: Math.floor(Math.random() * 10) + 1 // Random wind speed between 1-10 km/h
    };
    
    weatherData.value = weather;
    return weather;
  } catch (err) {
    error.value = err.message;
    console.error('Error fetching weather data:', err);
    return null;
  } finally {
    isLoading.value = false;
  }
};

/**
 * Get weather data for current location
 * @returns {Promise<Object>} Weather data
 */
const getWeatherForCurrentLocation = async () => {
  try {
    const position = await getCurrentPosition();
    if (position) {
      const { latitude, longitude } = position;
      return await getWeatherByCoordinates(latitude, longitude);
    }
    return null;
  } catch (err) {
    error.value = err.message;
    console.error('Error getting weather for current location:', err);
    return null;
  }
};

/**
 * Get weather description based on weather type
 * @param {string} weatherType - Weather type
 * @returns {string} Weather description
 */
const getWeatherDescription = (weatherType) => {
  switch (weatherType) {
    case WEATHER_TYPES.SUNNY:
      return 'Sunny';
    case WEATHER_TYPES.CLOUDY:
      return 'Cloudy';
    case WEATHER_TYPES.RAINY:
      return 'Rainy';
    default:
      return 'Unknown';
  }
};

/**
 * Reset error state
 */
const resetError = () => {
  error.value = null;
};

// Export the weather service
export const useWeatherService = () => {
  return {
    // State
    weatherData,
    isLoading,
    error,
    
    // Methods
    getWeatherByCoordinates,
    getWeatherForCurrentLocation,
    resetError
  };
};
