# Environment Configuration

This document explains how to use environment variables in the RVMPlus Rewards application.

## Environment Files

The application uses the following environment files:

- `.env` - Default environment variables
- `.env.development` - Development environment variables (used with `npm run dev`)
- `.env.staging` - Staging environment variables (used with `npm run build:staging`)
- `.env.production` - Production environment variables (used with `npm run build:production`)

## Available Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `VITE_API_BASE_URL` | Base URL for API requests | `http://localhost:5000` |
| `VITE_API_TIMEOUT` | API request timeout in milliseconds | `30000` |
| `VITE_USE_MOCK_DATA` | Whether to use mock data instead of real API calls | `true` |
| `VITE_APP_NAME` | Application name | `RVMPlus Rewards` |
| `VITE_APP_VERSION` | Application version | `1.0.0` |

## Environment-Specific Configurations

### Development

```
VITE_API_BASE_URL=http://localhost:5000
VITE_USE_MOCK_DATA=true
```

### Staging

```
VITE_API_BASE_URL=https://staging.rvmplus.com
VITE_USE_MOCK_DATA=false
```

### Production

```
VITE_API_BASE_URL=https://api.rvmplus.com
VITE_USE_MOCK_DATA=false
```

## Usage in Code

Environment variables are accessed using `import.meta.env`:

```javascript
// Access environment variables
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
const useMockData = import.meta.env.VITE_USE_MOCK_DATA === 'true';
```

## Build Commands

The following build commands are available:

- `npm run build` - Build using default environment
- `npm run build:staging` - Build using staging environment
- `npm run build:production` - Build using production environment

## Vercel Deployment

The application is configured to use the appropriate environment based on the Vercel deployment:

- Preview deployments use the staging environment
- Production deployments use the production environment

This is configured in `vercel.json`:

```json
{
  "buildCommand": "npm run build:${VERCEL_ENV}"
}
```

## Adding New Environment Variables

1. Add the variable to all environment files (`.env`, `.env.development`, `.env.staging`, `.env.production`)
2. Prefix the variable with `VITE_` to make it accessible in the client-side code
3. Access the variable in code using `import.meta.env.VITE_YOUR_VARIABLE`

## Local Development with Different Environments

To run the development server with a specific environment:

```bash
# Development environment (default)
npm run dev

# Staging environment
npm run dev -- --mode staging

# Production environment
npm run dev -- --mode production
```

## Testing Environment Variables

You can verify that environment variables are loaded correctly by checking the console:

```javascript
console.log('API Base URL:', import.meta.env.VITE_API_BASE_URL);
console.log('Using Mock Data:', import.meta.env.VITE_USE_MOCK_DATA);
```
