// Forest store to manage forest-related state
import { ref, computed } from 'vue';

// Forest state
const currentStage = ref(3);
const co2Absorbed = ref(3000);
const co2Target = ref(5000);
const totalCO2Mitigated = ref(4000);
const totalItemsRecycled = ref(357);
const totalTreesGrown = ref(3);

// Tree stages
const treeStages = [
  { id: 1, name: 'Seedling', co2Required: 1000 },
  { id: 2, name: 'Sapling', co2Required: 3000 },
  { id: 3, name: 'Young Tree', co2Required: 5000 },
  { id: 4, name: 'Mature Tree', co2Required: 10000 },
  { id: 5, name: 'Ancient Tree', co2Required: 20000 },
];

// Computed properties
const currentTreeStage = computed(() => {
  return treeStages.find(stage => stage.id === currentStage.value) || treeStages[0];
});

const progressPercentage = computed(() => {
  return (co2Absorbed.value / co2Target.value) * 100;
});

// Methods
const addCO2 = (amount) => {
  co2Absorbed.value += amount;
  totalCO2Mitigated.value += amount;
  
  // Check if we should advance to the next stage
  if (co2Absorbed.value >= co2Target.value) {
    advanceStage();
  }
};

const addRecycledItem = (count = 1) => {
  totalItemsRecycled.value += count;
  
  // Each recycled item contributes some CO2 absorption (e.g., 10 units)
  addCO2(count * 10);
};

const advanceStage = () => {
  // Find the next stage
  const nextStage = treeStages.find(stage => stage.id === currentStage.value + 1);
  
  if (nextStage) {
    // Advance to the next stage
    currentStage.value++;
    totalTreesGrown.value++;
    
    // Reset CO2 absorbed for the new stage
    co2Absorbed.value = 0;
    co2Target.value = nextStage.co2Required;
  } else {
    // If we're at the max stage, plant a new tree
    currentStage.value = 1;
    totalTreesGrown.value++;
    
    // Reset CO2 absorbed for the new tree
    co2Absorbed.value = 0;
    co2Target.value = treeStages[0].co2Required;
  }
};

// Export the forest store
export function useForest() {
  return {
    // State
    currentStage,
    co2Absorbed,
    co2Target,
    totalCO2Mitigated,
    totalItemsRecycled,
    totalTreesGrown,
    treeStages,
    
    // Computed
    currentTreeStage,
    progressPercentage,
    
    // Methods
    addCO2,
    addRecycledItem,
    advanceStage,
  };
}
