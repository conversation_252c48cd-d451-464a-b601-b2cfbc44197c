# API Requirements for ASP.NET Backend

This document outlines the API endpoints required for the RewardRedemption application, including data formats and endpoint specifications. For detailed error codes, refer to the `API_Error_Codes.md` document.

## Response Format

All API endpoints should follow a consistent response format:

### Success Response

```json
{
  "success": true,
  "message": "Optional success message",
  "data": {
    // Response data specific to each endpoint
  }
}
```

### Error Response

```json
{
  "success": false,
  "message": "Error message",
  "error": {
    "code": "ERROR_CODE"
  }
}
```

## Authentication API

### 1. Send OTP
- **Endpoint**: `POST /api/auth/send-otp`
- **Description**: Sends a one-time password to the user's phone number
- **Request Format**:
```json
{
  "phoneNumber": "9876543210",
  "countryCode": "MY",
  "phoneCode": "+60"
}
```
- **Response Format (Success)**:
```json
{
  "success": true,
  "message": "O<PERSON> sent successfully",
  "data": {
    "otpSent": true,
    "phoneNumber": "9876543210"
  }
}
```
- **Possible Error Codes**: `INVALID_PHONE`, `RATE_LIMIT_EXCEEDED`, `SERVICE_UNAVAILABLE`

### 2. Verify OTP
- **Endpoint**: `POST /api/auth/verify-otp`
- **Description**: Verifies the OTP entered by the user
- **Request Format**:
```json
{
  "phoneNumber": "9876543210",
  "otp": "123456"
}
```
- **Response Format (Success)**:
```json
{
  "success": true,
  "message": "OTP verified successfully",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "user123",
      "name": "Demo User",
      "phone": "9876543210",
      "email": "<EMAIL>"
    }
  }
}
```
- **Possible Error Codes**: `INVALID_OTP`, `OTP_EXPIRED`, `USER_NOT_FOUND`

## User Profile API

### 1. Get User Profile
- **Endpoint**: `GET /api/users/profile`
- **Description**: Gets the user's profile information
- **Headers**: `Authorization: Bearer {token}`
- **Response Format (Success)**:
```json
{
  "success": true,
  "data": {
    "id": "user123",
    "name": "Demo User",
    "phone": "9876543210",
    "email": "<EMAIL>",
    "gender": "Male",
    "address": {
      "street": "123 Main St",
      "city": "Petaling Jaya",
      "state": "Selangor",
      "zipCode": "47500",
      "country": "Malaysia"
    },
    "points": 1500
  }
}
```
- **Possible Error Codes**: `UNAUTHORIZED`, `PROFILE_NOT_FOUND`

### 2. Update User Profile
- **Endpoint**: `PUT /api/users/profile`
- **Description**: Updates the user's profile information
- **Headers**: `Authorization: Bearer {token}`
- **Request Format**:
```json
{
  "name": "Demo User",
  "email": "<EMAIL>",
  "gender": "Male",
  "address": {
    "street": "123 Main St",
    "city": "Petaling Jaya",
    "state": "Selangor",
    "zipCode": "47500",
    "country": "Malaysia"
  }
}
```
- **Response Format (Success)**:
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "id": "user123",
    "name": "Demo User",
    "phone": "9876543210",
    "email": "<EMAIL>",
    "gender": "Male",
    "address": {
      "street": "123 Main St",
      "city": "Petaling Jaya",
      "state": "Selangor",
      "zipCode": "47500",
      "country": "Malaysia"
    }
  }
}
```
- **Possible Error Codes**: `UNAUTHORIZED`, `VALIDATION_ERROR`, `INVALID_EMAIL`, `INVALID_NAME`, `INVALID_GENDER`, `INVALID_ADDRESS`, `PROFILE_UPDATE_FAILED`

## Location API

### 1. Get Locations
- **Endpoint**: `GET /api/locations`
- **Description**: Gets a list of all RVM locations
- **Headers**: `Authorization: Bearer {token}`
- **Query Parameters**:
  - `latitude`: User's current latitude (optional)
  - `longitude`: User's current longitude (optional)
- **Response Format (Success)**:
```json
{
  "success": true,
  "data": [
    {
      "id": "mbmb-001",
      "name": "MELAKA- MBMB",
      "address": "1, Jalan Tun Abdul Razak, 75450 Melaka",
      "latitude": 2.1896,
      "longitude": 102.2501,
      "services": ["recycle", "collect", "dispose"],
      "distance": 1.5,
      "distanceText": "1.5 km"
    },
    {
      "id": "dataran-001",
      "name": "MELAKA- DATARAN QUAYSIDE",
      "address": "5, Bandar Hilir, 75000, Malacca, Malaysia",
      "latitude": 2.1905,
      "longitude": 102.2487,
      "services": ["recycle", "collect", "dispose"],
      "distance": 2.3,
      "distanceText": "2.3 km"
    }
  ]
}
```
- **Possible Error Codes**: `UNAUTHORIZED`, `LOCATION_SERVICE_UNAVAILABLE`

### 2. Get Location Details
- **Endpoint**: `GET /api/locations/{id}`
- **Description**: Gets details of a specific location
- **Headers**: `Authorization: Bearer {token}`
- **Response Format (Success)**:
```json
{
  "success": true,
  "data": {
    "id": "mbmb-001",
    "name": "MELAKA- MBMB",
    "address": "1, Jalan Tun Abdul Razak, 75450 Melaka",
    "latitude": 2.1896,
    "longitude": 102.2501,
    "services": ["recycle", "collect", "dispose"],
    "description": "Main recycling center in Melaka",
    "phoneNumber": "+60123456789",
    "email": "<EMAIL>",
    "website": "https://mbmb.gov.my",
    "openingHours": [
      "Monday-Friday: 9:00 AM - 6:00 PM",
      "Saturday: 9:00 AM - 1:00 PM",
      "Sunday: Closed"
    ],
    "images": [
      "https://example.com/location-images/mbmb-001-1.jpg",
      "https://example.com/location-images/mbmb-001-2.jpg"
    ]
  }
}
```
- **Possible Error Codes**: `UNAUTHORIZED`, `LOCATION_NOT_FOUND`

### 3. Get Nearby Locations
- **Endpoint**: `GET /api/locations/nearby`
- **Description**: Gets a list of nearby locations based on user's coordinates
- **Headers**: `Authorization: Bearer {token}`
- **Query Parameters**:
  - `latitude`: User's current latitude (required)
  - `longitude`: User's current longitude (required)
  - `radius`: Search radius in kilometers (optional, default: 10.0)
- **Response Format (Success)**:
```json
{
  "success": true,
  "data": [
    {
      "id": "mbmb-001",
      "name": "MELAKA- MBMB",
      "address": "1, Jalan Tun Abdul Razak, 75450 Melaka",
      "latitude": 2.1896,
      "longitude": 102.2501,
      "services": ["recycle", "collect", "dispose"],
      "distance": 1.5,
      "distanceText": "1.5 km"
    },
    {
      "id": "dataran-001",
      "name": "MELAKA- DATARAN QUAYSIDE",
      "address": "5, Bandar Hilir, 75000, Malacca, Malaysia",
      "latitude": 2.1905,
      "longitude": 102.2487,
      "services": ["recycle", "collect", "dispose"],
      "distance": 2.3,
      "distanceText": "2.3 km"
    }
  ]
}
```
- **Possible Error Codes**: `UNAUTHORIZED`, `INVALID_COORDINATES`, `INVALID_RADIUS`, `LOCATION_SERVICE_UNAVAILABLE`

## Voucher API

### 1. Get Vouchers
- **Endpoint**: `GET /api/vouchers`
- **Description**: Gets a list of vouchers for the user
- **Headers**: `Authorization: Bearer {token}`
- **Query Parameters**:
  - `status`: Filter by status (Active, Used, Expired) (optional)
- **Response Format (Success)**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "brand": "BYC",
      "title": "BYC Free 1 Coffee",
      "expiryDate": "2025-02-13",
      "status": "Active",
      "type": "FOOD"
    },
    {
      "id": 2,
      "brand": "BYC",
      "title": "BYC Free 1 Coffee",
      "expiryDate": "2025-02-13",
      "status": "Used",
      "type": "FOOD"
    }
  ]
}
```
- **Possible Error Codes**: `UNAUTHORIZED`, `INVALID_VOUCHER_STATUS`

### 2. Get Voucher Details
- **Endpoint**: `GET /api/vouchers/{id}`
- **Description**: Gets details of a specific voucher
- **Headers**: `Authorization: Bearer {token}`
- **Response Format (Success)**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "brand": "BYC",
    "title": "BYC Free 1 Coffee",
    "subtitle": "Bring Your Cup Coffee",
    "expiryDate": "2025-02-13",
    "status": "Active",
    "type": "FOOD",
    "content": {
      "introText": "Enjoy a free coffee at any BYC outlet.",
      "terms": [
        "Valid at all BYC outlets",
        "One redemption per user",
        "Valid until 13 Feb 2025"
      ]
    },
    "bannerImage": "https://example.com/voucher-banner.jpg"
  }
}
```
- **Possible Error Codes**: `UNAUTHORIZED`, `VOUCHER_NOT_FOUND`

### 3. Redeem Voucher
- **Endpoint**: `POST /api/vouchers/{id}/redeem`
- **Description**: Redeems a voucher
- **Headers**: `Authorization: Bearer {token}`
- **Response Format (Success)**:
```json
{
  "success": true,
  "message": "Voucher redeemed successfully",
  "data": {
    "id": 1,
    "status": "Used",
    "redeemedAt": "2023-09-19T15:30:00Z"
  }
}
```
- **Possible Error Codes**: `UNAUTHORIZED`, `VOUCHER_NOT_FOUND`, `VOUCHER_EXPIRED`, `VOUCHER_ALREADY_REDEEMED`, `REDEMPTION_FAILED`

## Transaction API

### 1. Get Transactions
- **Endpoint**: `GET /api/transactions`
- **Description**: Gets a list of transactions for the user
- **Headers**: `Authorization: Bearer {token}`
- **Query Parameters**:
  - `category`: Filter by category (All, Collected, Spent) (optional)
  - `page`: Page number (optional, default: 1)
  - `limit`: Number of items per page (optional, default: 20)
- **Response Format (Success)**:
```json
{
  "success": true,
  "data": {
    "transactions": [
      {
        "id": 1,
        "type": "VM Order",
        "amount": 150.0,
        "date": "2023-09-19",
        "time": "15:15:00",
        "category": "Spent"
      },
      {
        "id": 2,
        "type": "RVM+ Points",
        "amount": 11.0,
        "date": "2023-09-19",
        "time": "15:13:00",
        "category": "Collected"
      }
    ],
    "pagination": {
      "total": 4,
      "page": 1,
      "limit": 20,
      "totalPages": 1
    }
  }
}
```
- **Possible Error Codes**: `UNAUTHORIZED`, `INVALID_CATEGORY`, `INVALID_PAGE`, `INVALID_LIMIT`

### 2. Get Transaction Details
- **Endpoint**: `GET /api/transactions/{id}`
- **Description**: Gets details of a specific transaction
- **Headers**: `Authorization: Bearer {token}`
- **Response Format (Success)**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "type": "VM Order",
    "amount": 150.0,
    "date": "2023-09-19",
    "time": "15:15:00",
    "category": "Spent",
    "description": "Purchase of coffee from vending machine",
    "location": "MBMB Recycling Center",
    "reference": "VM-12345",
    "items": [
      {
        "name": "Coffee",
        "quantity": 1,
        "price": 150.0
      }
    ]
  }
}
```
- **Possible Error Codes**: `UNAUTHORIZED`, `TRANSACTION_NOT_FOUND`

## Movement API

### 1. Get Movement Stats
- **Endpoint**: `GET /api/movement/stats`
- **Description**: Gets the user's movement statistics
- **Headers**: `Authorization: Bearer {token}`
- **Response Format (Success)**:
```json
{
  "success": true,
  "data": {
    "stepsWalked": 5000,
    "pointsEarned": 50,
    "challengesCompleted": 2
  }
}
```
- **Possible Error Codes**: `UNAUTHORIZED`, `MOVEMENT_SERVICE_UNAVAILABLE`

### 2. Get Available Challenges
- **Endpoint**: `GET /api/movement/challenges`
- **Description**: Gets a list of available movement challenges
- **Headers**: `Authorization: Bearer {token}`
- **Response Format (Success)**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "Daily 10,000 steps",
      "description": "Keep active with our daily strides goal.",
      "reward": 150,
      "goal": 10000,
      "unit": "steps"
    },
    {
      "id": 2,
      "title": "15 minutes walk",
      "description": "Take a break and get 1,500 steps in!",
      "reward": 50,
      "goal": 1500,
      "unit": "steps"
    }
  ]
}
```
- **Possible Error Codes**: `UNAUTHORIZED`, `MOVEMENT_SERVICE_UNAVAILABLE`

### 3. Start Challenge
- **Endpoint**: `POST /api/movement/challenges/{id}/start`
- **Description**: Starts a movement challenge
- **Headers**: `Authorization: Bearer {token}`
- **Response Format (Success)**:
```json
{
  "success": true,
  "message": "Challenge started successfully",
  "data": {
    "challengeId": 1,
    "startTime": "2023-09-19T15:30:00Z",
    "endTime": "2023-09-20T15:30:00Z",
    "goal": 10000,
    "progress": 0,
    "unit": "steps"
  }
}
```
- **Possible Error Codes**: `UNAUTHORIZED`, `CHALLENGE_NOT_FOUND`, `CHALLENGE_ALREADY_ACTIVE`, `CHALLENGE_EXPIRED`, `CHALLENGE_START_FAILED`

### 4. Update Challenge Progress
- **Endpoint**: `PUT /api/movement/challenges/active/progress`
- **Description**: Updates the progress of the active challenge
- **Headers**: `Authorization: Bearer {token}`
- **Request Format**:
```json
{
  "steps": 500,
  "distance": 400
}
```
- **Response Format (Success)**:
```json
{
  "success": true,
  "data": {
    "challengeId": 1,
    "progress": 2500,
    "goal": 10000,
    "completed": false,
    "timeLeft": "23:45:30"
  }
}
```
- **Possible Error Codes**: `UNAUTHORIZED`, `NO_ACTIVE_CHALLENGE`, `INVALID_STEPS`, `INVALID_DISTANCE`, `CHALLENGE_UPDATE_FAILED`

## Forest API

### 1. Get Forest Stats
- **Endpoint**: `GET /api/forest/stats`
- **Description**: Gets the user's forest statistics
- **Headers**: `Authorization: Bearer {token}`
- **Response Format (Success)**:
```json
{
  "success": true,
  "data": {
    "currentStage": 3,
    "co2Absorbed": 3000,
    "co2Target": 5000,
    "totalCO2Mitigated": 4000,
    "totalItemsRecycled": 357,
    "totalTreesGrown": 3,
    "currentTreeStage": {
      "id": 3,
      "name": "Young Tree",
      "co2Required": 5000
    },
    "progressPercentage": 60
  }
}
```
- **Possible Error Codes**: `UNAUTHORIZED`, `FOREST_STATS_NOT_FOUND`, `FOREST_SERVICE_UNAVAILABLE`

### 2. Update Forest Stats
- **Endpoint**: `POST /api/forest/update`
- **Description**: Updates the user's forest statistics based on recycling activity
- **Headers**: `Authorization: Bearer {token}`
- **Request Format**:
```json
{
  "co2Amount": 100,
  "recycledItems": 10
}
```
- **Response Format (Success)**:
```json
{
  "success": true,
  "message": "Forest statistics updated successfully",
  "data": {
    "currentStage": 3,
    "co2Absorbed": 3100,
    "co2Target": 5000,
    "totalCO2Mitigated": 4100,
    "totalItemsRecycled": 367,
    "progressPercentage": 62,
    "levelUp": false
  }
}
```
- **Possible Error Codes**: `UNAUTHORIZED`, `INVALID_CO2_AMOUNT`, `INVALID_RECYCLED_ITEMS`, `FOREST_UPDATE_FAILED`

## Authentication Requirements

- JWT-based authentication
- Token expiration: 24 hours
- Include refresh token mechanism
- Secure token storage
- HTTPS required for all API calls

## API Security Considerations

1. **HTTPS**: All API endpoints must be accessible only via HTTPS
2. **Input Validation**: Validate all input data to prevent injection attacks
3. **Rate Limiting**: Implement rate limiting for sensitive endpoints
4. **CORS**: Configure CORS to allow only trusted origins
5. **Logging**: Implement comprehensive logging for security monitoring
