<script setup>
import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
import { useTheme } from '../store/theme';

const { isDarkMode } = useTheme();

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  message: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'success', // success, error, info, warning
    validator: (value) => ['success', 'error', 'info', 'warning'].includes(value)
  },
  duration: {
    type: Number,
    default: 3000 // milliseconds
  },
  position: {
    type: String,
    default: 'top', // top, bottom
    validator: (value) => ['top', 'bottom'].includes(value)
  }
});

// Emits
const emit = defineEmits(['close']);

// Local state
const isVisible = ref(false);
const timeoutId = ref(null);

// Hide notification
const hideNotification = () => {
  isVisible.value = false;
  emit('close');
};

// Show notification
const showNotification = () => {
  isVisible.value = true;

  // Auto-hide after duration
  if (props.duration > 0) {
    clearTimeout(timeoutId.value);
    timeoutId.value = setTimeout(() => {
      hideNotification();
    }, props.duration);
  }
};

// Watch for show prop changes
watch(() => props.show, (newValue) => {
  if (newValue) {
    showNotification();
  } else {
    hideNotification();
  }
}, { immediate: true });

// Clean up timeout on component unmount
onBeforeUnmount(() => {
  if (timeoutId.value) {
    clearTimeout(timeoutId.value);
  }
});
</script>

<template>
  <transition name="notification">
    <div
      v-if="isVisible"
      class="notification"
      :class="[
        `notification-${type}`,
        `notification-${position}`,
        { 'dark-mode': isDarkMode }
      ]"
    >
      <div class="notification-content">
        <div class="notification-icon">
          <!-- Success Icon -->
          <svg v-if="type === 'success'" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>

          <!-- Error Icon -->
          <svg v-else-if="type === 'error'" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="15" y1="9" x2="9" y2="15"></line>
            <line x1="9" y1="9" x2="15" y2="15"></line>
          </svg>

          <!-- Info Icon -->
          <svg v-else-if="type === 'info'" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="16" x2="12" y2="12"></line>
            <line x1="12" y1="8" x2="12.01" y2="8"></line>
          </svg>

          <!-- Warning Icon -->
          <svg v-else-if="type === 'warning'" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
            <line x1="12" y1="9" x2="12" y2="13"></line>
            <line x1="12" y1="17" x2="12.01" y2="17"></line>
          </svg>
        </div>
        <div class="notification-message">{{ message }}</div>
        <button class="notification-close" @click="hideNotification">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
    </div>
  </transition>
</template>

<style scoped>
.notification {
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  width: 90%;
  max-width: 400px;
  padding: 0;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.notification-top {
  top: 20px;
}

.notification-bottom {
  bottom: 20px;
}

.notification-content {
  display: flex;
  align-items: center;
  padding: 16px;
}

.notification-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-message {
  flex: 1;
  font-size: 0.95rem;
  font-weight: 500;
}

.notification-close {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: inherit;
  opacity: 0.7;
}

.notification-close:hover {
  opacity: 1;
}

/* Notification types */
.notification-success {
  background-color: #e6f7ee;
  color: #0c6b58;
  border-left: 4px solid #0c6b58;
}

.notification-error {
  background-color: #fee7e7;
  color: #d32f2f;
  border-left: 4px solid #d32f2f;
}

.notification-info {
  background-color: #e3f2fd;
  color: #0288d1;
  border-left: 4px solid #0288d1;
}

.notification-warning {
  background-color: #fff8e1;
  color: #f57c00;
  border-left: 4px solid #f57c00;
}

/* Dark mode */
.notification.dark-mode.notification-success {
  background-color: #0c6b58;
  color: #e6f7ee;
  border-left: 4px solid #e6f7ee;
}

.notification.dark-mode.notification-error {
  background-color: #d32f2f;
  color: #fee7e7;
  border-left: 4px solid #fee7e7;
}

.notification.dark-mode.notification-info {
  background-color: #0288d1;
  color: #e3f2fd;
  border-left: 4px solid #e3f2fd;
}

.notification.dark-mode.notification-warning {
  background-color: #f57c00;
  color: #fff8e1;
  border-left: 4px solid #fff8e1;
}

/* Animation */
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from,
.notification-leave-to {
  opacity: 0;
  transform: translateX(-50%) translateY(-20px);
}

.notification-top.notification-enter-from,
.notification-top.notification-leave-to {
  transform: translateX(-50%) translateY(-20px);
}

.notification-bottom.notification-enter-from,
.notification-bottom.notification-leave-to {
  transform: translateX(-50%) translateY(20px);
}
</style>
