import { test, expect } from '@playwright/test';

/**
 * Comprehensive responsive design tests for mobile and desktop views
 */
test.describe('Responsive Design Tests', () => {
  // Define viewport sizes for testing
  const viewports = {
    mobileSm: { width: 320, height: 568 },  // iPhone 5/SE (smallest supported)
    mobileMd: { width: 375, height: 667 },  // iPhone 6/7/8
    mobileLg: { width: 414, height: 896 },  // iPhone 11 Pro Max
    tablet: { width: 768, height: 1024 },   // iPad
    desktop: { width: 1280, height: 800 },  // Standard desktop
    desktopLg: { width: 1920, height: 1080 } // Large desktop
  };

  // Test login page responsiveness
  test('Login page should adapt to different screen sizes', async ({ page }) => {
    await page.goto('/login');
    
    // Test on small mobile
    await page.setViewportSize(viewports.mobileSm);
    await page.screenshot({ path: 'login-mobile-sm.png' });
    
    // Check container width on small mobile
    const containerWidthSm = await page.evaluate(() => {
      const container = document.querySelector('.login-container');
      return container ? window.getComputedStyle(container).width : 'not found';
    });
    console.log(`Login container width on small mobile: ${containerWidthSm}`);
    
    // Test on medium mobile
    await page.setViewportSize(viewports.mobileMd);
    await page.screenshot({ path: 'login-mobile-md.png' });
    
    // Check container width on medium mobile
    const containerWidthMd = await page.evaluate(() => {
      const container = document.querySelector('.login-container');
      return container ? window.getComputedStyle(container).width : 'not found';
    });
    console.log(`Login container width on medium mobile: ${containerWidthMd}`);
    
    // Test on tablet
    await page.setViewportSize(viewports.tablet);
    await page.screenshot({ path: 'login-tablet.png' });
    
    // Check container width on tablet
    const containerWidthTablet = await page.evaluate(() => {
      const container = document.querySelector('.login-container');
      return container ? window.getComputedStyle(container).width : 'not found';
    });
    console.log(`Login container width on tablet: ${containerWidthTablet}`);
    
    // Test on desktop
    await page.setViewportSize(viewports.desktop);
    await page.screenshot({ path: 'login-desktop.png' });
    
    // Check container width on desktop
    const containerWidthDesktop = await page.evaluate(() => {
      const container = document.querySelector('.login-container');
      return container ? window.getComputedStyle(container).width : 'not found';
    });
    console.log(`Login container width on desktop: ${containerWidthDesktop}`);
    
    // Verify that container widths are different between mobile and desktop
    expect(containerWidthSm).not.toEqual(containerWidthDesktop);
  });

  // Test country selector dropdown responsiveness
  test('Country selector dropdown should adapt to different screen sizes', async ({ page }) => {
    await page.goto('/login');
    
    // Test on mobile
    await page.setViewportSize(viewports.mobileMd);
    
    // Click on country selector to open dropdown
    await page.click('.country-selector');
    
    // Wait for dropdown to be visible
    await page.waitForSelector('.country-dropdown', { state: 'visible' });
    
    // Take screenshot with dropdown open on mobile
    await page.screenshot({ path: 'country-dropdown-mobile.png' });
    
    // Check dropdown width on mobile
    const dropdownWidthMobile = await page.evaluate(() => {
      const dropdown = document.querySelector('.country-dropdown');
      return dropdown ? window.getComputedStyle(dropdown).width : 'not found';
    });
    console.log(`Country dropdown width on mobile: ${dropdownWidthMobile}`);
    
    // Close dropdown by clicking outside
    await page.click('.login-title');
    
    // Test on desktop
    await page.setViewportSize(viewports.desktop);
    
    // Click on country selector to open dropdown
    await page.click('.country-selector');
    
    // Wait for dropdown to be visible
    await page.waitForSelector('.country-dropdown', { state: 'visible' });
    
    // Take screenshot with dropdown open on desktop
    await page.screenshot({ path: 'country-dropdown-desktop.png' });
    
    // Check dropdown width on desktop
    const dropdownWidthDesktop = await page.evaluate(() => {
      const dropdown = document.querySelector('.country-dropdown');
      return dropdown ? window.getComputedStyle(dropdown).width : 'not found';
    });
    console.log(`Country dropdown width on desktop: ${dropdownWidthDesktop}`);
    
    // Verify that dropdown widths are appropriate for each viewport
    const mobileWidth = parseFloat(dropdownWidthMobile);
    const desktopWidth = parseFloat(dropdownWidthDesktop);
    
    if (!isNaN(mobileWidth) && !isNaN(desktopWidth)) {
      // On mobile, dropdown should be close to full width
      const mobileRatio = mobileWidth / viewports.mobileMd.width;
      expect(mobileRatio).toBeGreaterThan(0.8);
    }
  });

  // Test app container responsiveness
  test('App container should adapt to different screen sizes', async ({ page }) => {
    await page.goto('/login');
    
    // Test on mobile
    await page.setViewportSize(viewports.mobileMd);
    
    // Check app container width on mobile
    const appWidthMobile = await page.evaluate(() => {
      // Use first app container to avoid ambiguity
      const app = document.querySelector('#app');
      return app ? window.getComputedStyle(app).width : 'not found';
    });
    console.log(`App container width on mobile: ${appWidthMobile}`);
    
    // Test on desktop
    await page.setViewportSize(viewports.desktop);
    
    // Check app container width on desktop
    const appWidthDesktop = await page.evaluate(() => {
      // Use first app container to avoid ambiguity
      const app = document.querySelector('#app');
      return app ? window.getComputedStyle(app).width : 'not found';
    });
    console.log(`App container width on desktop: ${appWidthDesktop}`);
    
    // Verify that app container adapts to viewport
    const mobileWidth = parseFloat(appWidthMobile);
    const desktopWidth = parseFloat(appWidthDesktop);
    
    if (!isNaN(mobileWidth) && !isNaN(desktopWidth)) {
      // On mobile, app should be full width
      // On desktop, app might be constrained
      const mobileRatio = mobileWidth / viewports.mobileMd.width;
      const desktopRatio = desktopWidth / viewports.desktop.width;
      
      console.log(`Mobile ratio: ${mobileRatio}, Desktop ratio: ${desktopRatio}`);
      
      // Mobile should be close to full width
      expect(mobileRatio).toBeGreaterThan(0.95);
    }
  });

  // Test bottom navigation component CSS
  test('Bottom navigation CSS should have responsive styles', async ({ page }) => {
    // Go directly to the CSS file or extract CSS from the page
    await page.goto('/login');
    
    // Extract CSS related to bottom navigation
    const bottomNavCSS = await page.evaluate(() => {
      // Function to extract CSS rules for a specific selector
      function getStylesForSelector(selector) {
        const styles = [];
        for (const sheet of document.styleSheets) {
          try {
            for (const rule of sheet.cssRules) {
              if (rule.selectorText && rule.selectorText.includes(selector)) {
                styles.push({
                  selector: rule.selectorText,
                  css: rule.cssText
                });
              }
            }
          } catch (e) {
            // Skip cross-origin stylesheets
          }
        }
        return styles;
      }
      
      // Get styles for bottom navigation
      return getStylesForSelector('.bottom-nav');
    });
    
    console.log('Bottom Navigation CSS:');
    console.log(JSON.stringify(bottomNavCSS, null, 2));
    
    // Check for media queries in the CSS
    const hasMediaQueries = await page.evaluate(() => {
      let found = false;
      for (const sheet of document.styleSheets) {
        try {
          for (const rule of sheet.cssRules) {
            if (rule instanceof CSSMediaRule) {
              // Check if this media query contains bottom-nav styles
              for (const nestedRule of rule.cssRules) {
                if (nestedRule.selectorText && nestedRule.selectorText.includes('.bottom-nav')) {
                  found = true;
                  break;
                }
              }
            }
          }
        } catch (e) {
          // Skip cross-origin stylesheets
        }
      }
      return found;
    });
    
    console.log(`Has media queries for bottom navigation: ${hasMediaQueries}`);
    
    // Check for responsive properties in the CSS
    const hasResponsiveProperties = bottomNavCSS.some(rule => 
      rule.css.includes('max-width') || 
      rule.css.includes('width') || 
      rule.css.includes('@media')
    );
    
    console.log(`Has responsive properties: ${hasResponsiveProperties}`);
    
    // Check for safe area insets for notched phones
    const hasSafeAreaInsets = bottomNavCSS.some(rule => 
      rule.css.includes('env(safe-area-inset-bottom')
    );
    
    console.log(`Has safe area insets: ${hasSafeAreaInsets}`);
  });

  // Test bottom navigation component structure
  test('Bottom navigation component should have correct structure', async ({ page }) => {
    // Go to login page
    await page.goto('/login');
    
    // Set authentication in localStorage
    await page.evaluate(() => {
      localStorage.setItem('isAuthenticated', 'true');
      localStorage.setItem('user', JSON.stringify({
        id: '123',
        name: 'Test User',
        phone: '+601234567890',
        points: 1000
      }));
    });
    
    // Try to go to home page
    await page.goto('/');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Take a screenshot for debugging
    await page.screenshot({ path: 'home-page.png' });
    
    // Check if bottom navigation exists in the DOM
    const bottomNavExists = await page.evaluate(() => {
      return !!document.querySelector('.bottom-nav');
    });
    
    console.log(`Bottom navigation exists in DOM: ${bottomNavExists}`);
    
    // If bottom navigation exists, check its structure
    if (bottomNavExists) {
      // Check for navigation items
      const navItems = await page.evaluate(() => {
        const items = document.querySelectorAll('.nav-item');
        return Array.from(items).map(item => ({
          text: item.textContent.trim(),
          hasIcon: !!item.querySelector('.nav-icon')
        }));
      });
      
      console.log('Navigation items:');
      console.log(JSON.stringify(navItems, null, 2));
      
      // Check for QR code button
      const qrButtonExists = await page.evaluate(() => {
        return !!document.querySelector('.qr-code-button');
      });
      
      console.log(`QR code button exists: ${qrButtonExists}`);
    } else {
      // If bottom navigation doesn't exist, check why
      console.log('Bottom navigation not found. Checking authentication state...');
      
      const authState = await page.evaluate(() => {
        return {
          isAuthenticated: localStorage.getItem('isAuthenticated'),
          user: localStorage.getItem('user'),
          currentUrl: window.location.href
        };
      });
      
      console.log('Authentication state:');
      console.log(JSON.stringify(authState, null, 2));
    }
  });
});
