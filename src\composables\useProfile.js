import { computed } from 'vue';
import { useBitmapRequest } from './useBitmapRequest';
import { requestHandlerAuth } from '../services/bitmapService';

export function useProfile() {
  const { data, isLoading, error, fetchData } = useBitmapRequest();

  const profile = computed(() => {
    if (!data.value) {return null;}

    // Extract all tables Table0-Table4 if present
    const tables = {};
    for (let i = 0; i <= 4; i++) {
      const tableKey = `Table${i}`;
      if (data.value[tableKey]) {
        tables[tableKey] = data.value[tableKey];
      }
    }

    const basicInfo = tables.Table0?.[0] || {};
    const detailedInfo = tables.Table2?.[0] || tables.Table1?.[0] || {};

    return {
      id: data.value.Ref01,
      name: basicInfo.FullName || detailedInfo.FullName || '',
      phone: basicInfo.Contact || detailedInfo.MobileNo || '',
      email: basicInfo.Email || detailedInfo.Email || '',
      gender: basicInfo.Gender || detailedInfo.Gender || '',
      dob: basicInfo.DOB || detailedInfo.DOB || '',
      address: {
        street: basicInfo.Street1 || detailedInfo.Street1 || '',
        street2: basicInfo.Street2 || detailedInfo.Street2 || '',
        street3: basicInfo.Street3 || detailedInfo.Street3 || '',
        city: basicInfo.City || detailedInfo.City || '',
        zipCode: basicInfo.ZipCd || detailedInfo.ZipCd || '',
        state: basicInfo.StateCd || detailedInfo.StateCd || '',
        country: basicInfo.CtryCd || detailedInfo.CtryCd || '',
      },
      profileImage: basicInfo.ProfileImg || detailedInfo.ProfileImg || '',
      language: basicInfo.Lang || detailedInfo.Lang || 'English',
      userId: basicInfo.UserId || detailedInfo.UserId || '',
      tables,
    };
  });

  const fetchProfile = async () => {
    await fetchData(6); // bitmap 6 for profile
    return profile.value;
  };

  const updateProfile = async (profileData) => {
    try {
      const params = {
        Token: data.value?.Token,
        bitmap: 6,
        Func: '1', // Update function
        Ref01: data.value?.Ref01,
        Ref02: '',
        Lang: profileData.language || 'en',
        // Map the profile data to the API format
        FullName: profileData.name,
        Email: profileData.email,
        Contact: profileData.phone,
        Gender: profileData.gender,
        DOB: profileData.dob,
        Street1: profileData.address.street,
        Street2: profileData.address.street2,
        Street3: profileData.address.street3,
        City: profileData.address.city,
        ZipCd: profileData.address.zipCode,
        StateCd: profileData.address.state,
        CtryCd: profileData.address.country,
      };

      const res = await requestHandlerAuth(params);
      
      if (res?.data?.[0]?.Status === 'SUCCESS') {
        // Refresh profile data after successful update
        await fetchProfile();
        return { success: true };
      } else {
        return { 
          success: false, 
          message: res?.data?.[0]?.Message || 'Failed to update profile', 
        };
      }
    } catch (err) {
      console.error('Error updating profile:', err);
      return { 
        success: false, 
        message: 'An error occurred while updating profile', 
      };
    }
  };

  return {
    profile,
    isLoading,
    error,
    fetchProfile,
    updateProfile,
  };
} 