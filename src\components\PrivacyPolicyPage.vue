<script setup>
import { useRouter } from 'vue-router';
import { useTheme } from '../store/theme';
import { useI18n } from 'vue-i18n';
import { onMounted } from 'vue';
import { openInAppBrowser } from '../utils/browser';

const router = useRouter();
const { isDarkMode } = useTheme();
const { t } = useI18n();

// URL for the privacy policy page - use a URL that works with iframes
const privacyPolicyUrl = 'https://www.privacypolicies.com/live/************************************'; // Direct working URL for privacy policy

// Open the in-app browser with the privacy policy page
onMounted(() => {
  openInAppBrowser(router, privacyPolicyUrl, t('common.privacyPolicy', 'Privacy Policy'), '/profile');
});
</script>

<template>
  <!-- This is just a placeholder template. The component will redirect to the in-app browser -->
  <div class="loading-container">
    <div class="spinner"></div>
    <p>{{ t('common.loading', 'Loading...') }}</p>
  </div>
</template>

<style scoped>
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: var(--background-color);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

p {
  color: var(--text-color);
  font-size: 1rem;
}
</style>
