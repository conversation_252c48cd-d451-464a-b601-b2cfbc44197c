<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background elements -->
  <circle cx="200" cy="150" r="120" fill="#818cf8" opacity="0.05" />

  <!-- Person running with elements -->
  <g transform="translate(100, 50) scale(1.5)">
    <!-- Body -->
    <path d="M120 80C120 80 130 60 140 70C150 80 160 90 150 100C140 110 130 100 120 80Z" fill="#1e293b" stroke="#818cf8" stroke-width="2.5"/>

    <!-- Head -->
    <circle cx="145" cy="60" r="15" fill="#1e293b" stroke="#818cf8" stroke-width="2.5"/>

    <!-- Arms -->
    <path d="M130 80C130 80 110 70 100 90" stroke="#818cf8" stroke-width="3.5" stroke-linecap="round"/>
    <path d="M150 85C150 85 170 75 180 95" stroke="#818cf8" stroke-width="3.5" stroke-linecap="round"/>

    <!-- Legs -->
    <path d="M130 100C130 100 120 130 110 140" stroke="#818cf8" stroke-width="3.5" stroke-linecap="round"/>
    <path d="M145 100C145 100 160 130 170 140" stroke="#818cf8" stroke-width="3.5" stroke-linecap="round"/>

    <!-- Motion lines -->
    <path d="M80 60C80 60 60 65 50 60" stroke="#818cf8" stroke-width="2.5" stroke-linecap="round"/>
    <path d="M80 70C80 70 55 80 45 70" stroke="#818cf8" stroke-width="2.5" stroke-linecap="round"/>
    <path d="M80 80C80 80 60 90 50 80" stroke="#818cf8" stroke-width="2.5" stroke-linecap="round"/>
  </g>

  <!-- Floating elements -->
  <rect x="40" y="60" width="30" height="30" rx="6" fill="#1e293b" stroke="#818cf8" stroke-width="2.5"/>
  <rect x="320" y="80" width="40" height="40" rx="8" fill="#1e293b" stroke="#818cf8" stroke-width="2.5"/>
  <circle cx="80" cy="220" r="20" fill="#1e293b" stroke="#818cf8" stroke-width="2.5"/>
  <circle cx="330" cy="200" r="15" fill="#1e293b" stroke="#818cf8" stroke-width="2.5"/>

  <!-- Additional elements -->
  <path d="M50 150 Q 100 100, 150 150 T 250 150" stroke="#818cf8" stroke-width="2" stroke-dasharray="5,5" fill="none"/>
  <path d="M150 250 Q 200 200, 250 250 T 350 250" stroke="#818cf8" stroke-width="2" stroke-dasharray="5,5" fill="none"/>

  <!-- Step counter icon -->
  <g transform="translate(320, 30)">
    <rect x="0" y="0" width="40" height="25" rx="5" fill="#1e293b" stroke="#818cf8" stroke-width="1.5"/>
    <text x="20" y="17" font-family="Arial" font-size="12" fill="#818cf8" text-anchor="middle">STEPS</text>
  </g>
</svg>
